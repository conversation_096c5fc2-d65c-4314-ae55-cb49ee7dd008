import { useState, useRef, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const UserMenu = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { currentUser, logout } = useAuth();
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={menuRef}>
      <div 
        className="w-[38px] flex items-center gap-1 cursor-pointer" 
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="w-[18px] h-[18px] relative">
          <div className="w-[18px] h-[18px] absolute top-0 left-0 bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] shadow-[0px_3px_20px_rgba(160,183,205,0.20)] rounded-full" />
          <svg className="absolute left-[4.48px] top-[4.41px]" width="10" height="13" viewBox="0 0 10 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.99993 12.8671C3.30793 12.8671 1.75993 12.3271 0.481934 11.4283C0.499934 11.2843 0.535934 11.1414 0.571934 10.9974C0.679189 10.6071 0.836495 10.2324 1.03993 9.88252C1.23793 9.54052 1.47193 9.23452 1.75993 8.96452C2.02993 8.69452 2.35393 8.44364 2.67793 8.24564C3.01993 8.04764 3.37993 7.90364 3.77593 7.79564C4.17501 7.68807 4.58661 7.63398 4.99993 7.63477C6.22692 7.62608 7.40882 8.09674 8.29394 8.94652C8.70794 9.36052 9.03193 9.84652 9.26593 10.4034C9.39194 10.7274 9.48194 11.0694 9.53594 11.4283C8.20751 12.3622 6.6238 12.8646 4.99993 12.8671ZM2.24593 4.54214C2.08733 4.17901 2.00758 3.78637 2.01193 3.39014C2.01193 2.99527 2.08393 2.59927 2.24593 2.23927C2.40793 1.87927 2.62393 1.55639 2.89393 1.28639C3.16393 1.01639 3.48793 0.801518 3.84793 0.639518C4.20793 0.477518 4.60393 0.405518 4.99993 0.405518C5.41393 0.405518 5.79193 0.477518 6.15193 0.639518C6.51193 0.801518 6.83593 1.01752 7.10593 1.28639C7.37593 1.55639 7.59193 1.88039 7.75393 2.23927C7.91593 2.59927 7.98793 2.99527 7.98793 3.39014C7.98793 3.80414 7.91593 4.18214 7.75393 4.54102C7.59759 4.89569 7.37801 5.21896 7.10593 5.49502C6.82978 5.76669 6.50652 5.98589 6.15193 6.14189C5.40811 6.44757 4.57375 6.44757 3.82993 6.14189C3.47535 5.98589 3.15209 5.76669 2.87593 5.49502C2.60348 5.22298 2.38914 4.89954 2.24593 4.54214Z" fill="white"/>
          </svg>
        </div>
        <svg 
          className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          width="16" 
          height="16" 
          viewBox="0 0 16 16" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M11.3331 7.13795C11.3334 7.21119 11.3177 7.28356 11.2872 7.34974C11.2567 7.41592 11.2122 7.47423 11.157 7.52037L8.30009 9.88847C8.21489 9.96058 8.10803 10 7.99774 10C7.88745 10 7.78058 9.96058 7.69539 9.88847L4.83851 7.43702C4.74128 7.3538 4.68013 7.23422 4.66852 7.10457C4.65691 6.97492 4.69579 6.84584 4.77661 6.74572C4.85743 6.64559 4.97357 6.58263 5.09948 6.57067C5.22538 6.55872 5.35074 6.59876 5.44798 6.68198L8.00012 8.87357L10.5523 6.75552C10.6222 6.69557 10.7073 6.65748 10.7975 6.64578C10.8878 6.63407 10.9794 6.64923 11.0615 6.68946C11.1437 6.72969 11.2129 6.79331 11.2611 6.87279C11.3092 6.95227 11.3342 7.04428 11.3331 7.13795Z" fill="#0E7EE4"/>
        </svg>
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md bg-white border border-[#DBE4FF] shadow-[4px_1px_10px_rgba(57,124,221,0.08)]">
          <div className="py-1">
            <div className="px-4 py-2 text-[10px] text-[#0E7EE4] font-poppins border-b border-[#DBE4FF] bg-white">
              {currentUser?.username || 'User'}
            </div>
            <button
              onClick={logout}
              className="w-full text-left px-4 py-2 text-[10px] text-[#0E7EE4] hover:bg-[#DBE4FF] font-poppins flex items-center gap-2 transition-colors duration-150 bg-white"
            >
              <svg width="12" height="12" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2 4C2 2.89543 2.89543 2 4 2H8C8.55228 2 9 1.55228 9 1C9 0.447715 8.55228 0 8 0H4C1.79086 0 0 1.79086 0 4V12C0 14.2091 1.79086 16 4 16H8C8.55228 16 9 15.5523 9 15C9 14.4477 8.55228 14 8 14H4C2.89543 14 2 13.1046 2 12V4Z" fill="#0E7EE4"/>
                <path d="M15.7071 7.29289C16.0976 7.68342 16.0976 8.31658 15.7071 8.70711L11.7071 12.7071C11.3166 13.0976 10.6834 13.0976 10.2929 12.7071C9.90237 12.3166 9.90237 11.6834 10.2929 11.2929L12.5858 9L5 9C4.44772 9 4 8.55229 4 8C4 7.44772 4.44772 7 5 7L12.5858 7L10.2929 4.70711C9.90237 4.31658 9.90237 3.68342 10.2929 3.29289C10.6834 2.90237 11.3166 2.90237 11.7071 3.29289L15.7071 7.29289Z" fill="#0E7EE4"/>
              </svg>
              Log out
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserMenu; 