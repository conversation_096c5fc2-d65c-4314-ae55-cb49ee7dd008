import { useState, useEffect } from 'react';
import { WeatherData, WeatherResponse, WeatherForecast } from '../types/weather';
import { api } from './api';

/**
 * Fetch weather forecast data from the Django backend
 */
export const fetchWeatherData = async (): Promise<WeatherData> => {
  try {
    const response = await api.get('/mongodb/weather-forecast');
    return response.data as WeatherData;
  } catch (error) {
    console.error('Error fetching weather data:', error);
    throw error;
  }
};

/**
 * React hook to fetch and manage weather data
 */
export const useWeatherData = (): WeatherResponse => {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getWeatherData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const data = await fetchWeatherData();
        setWeatherData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch weather data');
        console.error('Weather data fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    getWeatherData();

    // Refresh weather data every 10 minutes
    const refreshInterval = setInterval(getWeatherData, 10 * 60 * 1000);

    return () => clearInterval(refreshInterval);
  }, []);

  return { weatherData, loading, error };
};

/**
 * Helper function to get the current temperature from forecast data
 * @param weatherData - Weather data object
 */
export const getCurrentTemperature = (weatherData: WeatherData): number | null => {
  if (!weatherData?.forecasts || weatherData.forecasts.length === 0) {
    return null;
  }
  
  return fahrenheitToCelsius(weatherData.forecasts[0].drybulb_temperature);
};

/**
 * Helper function to get the next hours forecast
 * @param weatherData - Weather data object
 * @param count - Number of hours to return (default: 4)
 */
export const getNextHoursForecast = (weatherData: WeatherData, count: number = 4): WeatherForecast[] => {
  if (!weatherData?.forecasts || weatherData.forecasts.length === 0) {
    return [];
  }
  
  return weatherData.forecasts.slice(0, count).map(forecast => ({
    ...forecast,
    drybulb_temperature: fahrenheitToCelsius(forecast.drybulb_temperature)
  }));
};

const fahrenheitToCelsius = (fahrenheit: number): number => {
  return (fahrenheit - 32) * 5 / 9;
};