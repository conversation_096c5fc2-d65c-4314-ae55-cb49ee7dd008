import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Wrench } from 'lucide-react';
import MaintenanceTicketDialog from './components/MaintenanceTicketDialog';
import { useDevice, Device } from '@/contexts/DeviceContext';
import { useToast } from '@/components/ui/use-toast';

const Maintenance: React.FC = () => {
  // Get maintenance context
  const { 
    maintenanceData, 
    isDeviceUnderMaintenance, 
    getDeviceMaintenanceInfo,
    getDeviceName,
    getDevicesByType
  } = useDevice();
  
  const { toast } = useToast();
  
  // State
  const [deviceTypes, setDeviceTypes] = useState<Record<string, Device[]>>({});
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);
  const [isMaintenanceDialogOpen, setIsMaintenanceDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  // Get device data from context
  useEffect(() => {
    try {
      const devicesByType = getDevicesByType();
      setDeviceTypes(devicesByType);
    } catch (error) {
      console.error('Error getting devices by type:', error);
      toast({
        title: "Error",
        description: "Failed to load equipment data. Please refresh the page.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [getDevicesByType]);
  
  // Handle maintenance request
  const handleMaintenanceRequest = (deviceId: string) => {
    setSelectedDeviceId(deviceId);
    setIsMaintenanceDialogOpen(true);
  };

  // Close maintenance dialog
  const closeMaintenanceDialog = () => {
    setIsMaintenanceDialogOpen(false);
    setSelectedDeviceId(null);
  };
  
  // Equipment card component - more compact version
  const EquipmentCard = ({ device }: { device: Device }) => {
    const underMaintenance = isDeviceUnderMaintenance(device.deviceId);
    
    return (
      <div 
        className={`flex items-center justify-between py-1.5 px-3 rounded-lg transition-all duration-300 ${
          !underMaintenance ? 'hover:bg-muted/10' : 'hover:bg-warning/10'
        }`}
        onClick={() => handleMaintenanceRequest(device.deviceId)}
      >
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            !underMaintenance ? 'bg-muted' : 'bg-warning'
          }`} />
          <span className={`text-sm font-medium tracking-wide ${underMaintenance ? 'text-warning' : 'text-muted'}`}>{device.name.replace('chiller', 'CH').replace(/_/g, '-').toUpperCase()}</span>
        </div>
        <div className="flex items-center">
          <span className={`text-xs font-medium tracking-wide ${
            !underMaintenance ? 'text-muted' : 'text-warning'
          }`}>
            {underMaintenance ? 'Under Maintenance' : 'Operational'}
          </span>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleMaintenanceRequest(device.deviceId);
            }}
            className={`ml-3 p-1.5 rounded-lg transition-all duration-300 ${
              !underMaintenance ? 'hover:bg-muted-bg' : 'hover:bg-warning-bg'
            } group`}
            title={!underMaintenance ? 'Open maintenance ticket' : 'Resolve maintenance ticket'}
          >
            <Wrench className={`w-4 h-4 transition-colors fill-current ${
              !underMaintenance 
                ? 'text-muted group-hover:text-muted'
                : 'text-warning group-hover:text-warning'
            }`} />
          </button>
        </div>
      </div>
    );
  };
  
  // Equipment section component
  const EquipmentSection = ({ title, devices }: { title: string, devices: Device[] }) => {
    return (
      <Card className="border shadow-sm bg-white/90">
        <CardContent className="p-3">
          <h2 className="text-sm font-semibold mb-2 text-foreground text-center">{title}</h2>
          <div className="h-px w-full bg-border mb-2"></div>
          <div className="grid grid-cols-1 gap-1.5">
            {devices.map((device) => (
              <EquipmentCard key={device.deviceId} device={device} />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Display loading state
  if (isLoading) {
    return (
      <div className="p-4">
        <div className="text-center">Loading equipment data...</div>
      </div>
    );
  }

  // Get equipment types and their display names
  const equipmentTypes: Record<string, string> = {
    'chiller': 'Chillers',
    'pchp': 'Primary Chilled Water Pumps',
    'cdp': 'Condenser Water Pumps',
    'schp': 'Secondary Chilled Water Pumps',
    'vsd': 'Variable Speed Drives',
    'ct': 'Cooling Towers',
    'binary_motorized_valve': 'Motorized Valves',
    'mvct': 'Motorized Valves',
    'mvcd': 'Motorized Valves',
    'mvch': 'Motorized Valves',
  };

  // Main render
  return (
    <div className="p-3">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
        {Object.entries(equipmentTypes).map(([type, title]) => {
          const devices = Object.entries(deviceTypes)
            .filter(([deviceType]) => deviceType.toLowerCase().includes(type.toLowerCase()))
            .flatMap(([_, devices]) => devices);
          
          if (devices.length === 0) return null;
          
          return (
            <EquipmentSection key={type} title={title} devices={devices} />
          );
        })}
      </div>

      {/* Maintenance Ticket Dialog */}
      {selectedDeviceId && (
        <MaintenanceTicketDialog
          open={isMaintenanceDialogOpen}
          onClose={closeMaintenanceDialog}
          deviceId={selectedDeviceId}
          isUnderMaintenance={isDeviceUnderMaintenance(selectedDeviceId)}
          maintenanceInfo={getDeviceMaintenanceInfo(selectedDeviceId)}
        />
      )}
    </div>
  );
};

export default Maintenance;