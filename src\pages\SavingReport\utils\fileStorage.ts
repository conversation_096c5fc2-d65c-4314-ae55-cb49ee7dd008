import { DataRow, ReportingDataRow, VariableSelection, DateRange } from '../types';
import { ElectricityRateValues } from '../components/ElectricityRateDialog';

// Interface for saved state data
export interface SavedState {
  baselineData: DataRow[];
  baselineRange: DateRange;
  reportingRange: DateRange;
  variableSelection: VariableSelection;
  reportingData?: ReportingDataRow[];
  weekdayFilterEnabled?: boolean;
  electricityRates?: Array<[string, ElectricityRateValues]>; // Serialized Map entries
  savedAt: string;
}

// Key for storing the main saved state
const STATIC_DATA_KEY = 'savingReport_staticData';

// Save application state to localStorage
export const saveAppState = (state: SavedState): void => {
  try {
    localStorage.setItem(STATIC_DATA_KEY, JSON.stringify(state));
    console.log('Application state saved successfully');
  } catch (error) {
    console.error('Error saving application state:', error);
    throw error;
  }
};

// Load application state from localStorage
export const loadAppState = (): SavedState | null => {
  try {
    const state = localStorage.getItem(STATIC_DATA_KEY);
    if (!state) return null;
    return JSON.parse(state);
  } catch (error) {
    console.error('Error loading application state:', error);
    return null;
  }
};

// Check if there is saved state
export const hasSavedState = (): boolean => {
  return localStorage.getItem(STATIC_DATA_KEY) !== null;
};

// Delete all saved data
export const deleteAllSavedData = (): void => {
  try {
    localStorage.removeItem(STATIC_DATA_KEY);
    console.log('All saved data deleted successfully');
  } catch (error) {
    console.error('Error deleting saved data:', error);
    throw error;
  }
};