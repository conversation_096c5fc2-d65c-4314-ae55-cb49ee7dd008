import React, { useEffect, useState, useRef } from 'react';
import { useRealtime } from '@/contexts/RealtimeContext';
import { useAuth } from '@/contexts/AuthContext';
import { fetchDailyData, fetchDailyWeatherData, fetchEnergyUsage } from '@/services/timescaleService';
import { getSiteId } from '@/services/authService';
import { DateTime } from 'luxon';

const EnergyUsageCompareCard: React.FC = () => {
  const { getValue } = useRealtime();
  const { site } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState({
    yesterday: {
      total: 0,
      plant: 0,
      airSide: 0,
      dbt: null as number | null,
      humidity: null as number | null
    },
    today: {
      total: 0,
      plant: 0,
      airSide: 0,
      dbt: null as number | null,
      humidity: null as number | null
    }
  });

  // Use a ref to store the getValue function to avoid too frequent dependency updates
  const getValueRef = useRef(getValue);
  
  // Update the ref when getValue changes
  useEffect(() => {
    getValueRef.current = getValue;
  }, [getValue]);
  
  // Main data fetching effect without getValue dependency
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get timezone from AuthContext or default to Asia/Bangkok
        const timezone = site?.timezone || 'Asia/Bangkok';
        
        // Get current time in the site's timezone using Luxon
        const now = DateTime.now().setZone(timezone);
        
        // Create today at start of day in the site's timezone
        const today_start = now.startOf('day');
        const today_end = now.endOf('day');
        
        // Create yesterday at start/end of day in the site's timezone
        const yesterday_start = now.minus({ days: 1 }).startOf('day');
        const yesterday_end = now.minus({ days: 1 }).endOf('day');
        const siteId = getSiteId() || '';
        
        // Initialize default values
        let yesterdayTotalEnergy = 0;
        let yesterdayPlantEnergy = 0;
        let yesterdayAirSideEnergy = 0;
        let yesterdayDbt = 0;
        let yesterdayHumidity = 0;
        let todayPlantEnergy = 0;
        let todayAirSideEnergy = 0;
        
        try {
          // Fetch all data in parallel
          const [
            todayPlantResponse,
            todayAirSideResponse,
            yesterdayPlantResponse,
            yesterdayAirSideResponse,
            yesterdayWeatherResponse
          ] = await Promise.all([
            // Fetch today's plant energy
            fetchEnergyUsage({
              site_id: siteId,
              device_id: 'plant',
              start_timestamp: today_start.toString(),
              end_timestamp: today_end.toString()
            }),
            
            // Fetch today's air-side energy
            fetchEnergyUsage({
              site_id: siteId,
              device_id: 'air_distribution_system',
              start_timestamp: today_start.toString(),
              end_timestamp: today_end.toString()
            }),
            
            // Fetch yesterday's plant energy
            fetchDailyData({
              site_id: siteId,
              device_id: 'plant',
              datapoints: ['daily_energy'],
              start_timestamp: yesterday_start.toString(),
              end_timestamp: yesterday_end.toString()
            }),
            
            // Fetch yesterday's air-side energy
            fetchDailyData({
              site_id: siteId,
              device_id: 'air_distribution_system',
              datapoints: ['daily_energy'],
              start_timestamp: yesterday_start.toString(),
              end_timestamp: yesterday_end.toString()
            }),
            
            // Fetch yesterday's weather data
            fetchDailyWeatherData({
              site_id: siteId,
              start_timestamp: yesterday_start.toString(),
              end_timestamp: yesterday_end.toString()
            })
          ]);
          
          // Process today's plant energy
          if (todayPlantResponse.success && todayPlantResponse.data) {
            todayPlantEnergy = todayPlantResponse.data.total_energy_usage;
          }
          
          // Process today's air-side energy
          if (todayAirSideResponse.success && todayAirSideResponse.data) {
            todayAirSideEnergy = todayAirSideResponse.data.total_energy_usage;
          }
          
          // Process yesterday's plant energy
          if (yesterdayPlantResponse.success && yesterdayPlantResponse.data) {
            const energyData = yesterdayPlantResponse.data.find(item => item.datapoint === 'daily_energy');
            if (energyData) {
              yesterdayPlantEnergy = energyData.value;
            }
          }
          
          // Process yesterday's air-side energy
          if (yesterdayAirSideResponse.success && yesterdayAirSideResponse.data) {
            const energyData = yesterdayAirSideResponse.data.find(item => item.datapoint === 'daily_energy');
            if (energyData) {
              yesterdayAirSideEnergy = energyData.value;
            }
          }
          
          // Process yesterday's weather data
          if (yesterdayWeatherResponse.success && yesterdayWeatherResponse.data.length > 0) {
            const weatherData = yesterdayWeatherResponse.data[0];
            yesterdayDbt = weatherData.mean_drybulb_temperature || 0;
            yesterdayHumidity = weatherData.mean_humidity || 0;
          }
          
          // Calculate total energy for yesterday
          yesterdayTotalEnergy = yesterdayAirSideEnergy + yesterdayPlantEnergy;
        } catch (innerErr) {
          console.error('Error in API calls:', innerErr);
          // Continue with default values set above
        }

        // Get today's weather data from realtime context using the ref
        const todayDbt = getValueRef.current('outdoor_weather_station', 'drybulb_temperature') || null;
        const todayHumidity = getValueRef.current('outdoor_weather_station', 'humidity') || null;
        
        // Calculate total energy for today
        const todayTotalEnergy = todayPlantEnergy + todayAirSideEnergy;
        
        setData({
          today: {
            total: todayTotalEnergy,
            plant: todayPlantEnergy,
            airSide: todayAirSideEnergy,
            dbt: todayDbt,
            humidity: todayHumidity
          },
          yesterday: {
            total: yesterdayTotalEnergy,
            plant: yesterdayPlantEnergy,
            airSide: yesterdayAirSideEnergy,
            dbt: yesterdayDbt,
            humidity: yesterdayHumidity
          }
        });
      } catch (err) {
        console.error('Error fetching energy usage data:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      }
    };

    // Fetch data immediately on mount
    fetchData();
    
    // Set up an interval to fetch data every 1 minute
    const intervalId = setInterval(fetchData, 60000);
    
    // Clean up the interval when the component unmounts
    return () => {
      clearInterval(intervalId);
    };
  }, []); // Empty dependency array means this only runs on mount/unmount

  if (error) {
    return (
      <div className="flex self-stretch justify-start items-start flex-col gap-2.5 p-2.5 alto-card h-[150px]">
        <div className="flex self-stretch justify-start items-center flex-row gap-4">
          <span className="text-[#065BA9] text-sm font-semibold">Energy Usage</span>
        </div>
        <div className="flex items-center justify-center h-full text-red-500 text-xs">
          Error loading data: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="flex self-stretch justify-start items-start flex-col gap-2.5 p-2.5 alto-card">
      <div className="flex self-stretch justify-start items-center flex-row gap-4">
        <span className="text-[#065BA9] text-sm font-semibold">Energy Usage</span>
      </div>
      
      <div className="flex self-stretch justify-end items-start flex-row gap-1">
        <div className="flex flex-1 justify-start items-start flex-col gap-1 p-1.5 bg-[#F9FAFF] border-solid border-[#EDEFF9] border rounded-lg">
          <span className="text-[#788796] text-[9px] text-center">Yesterday</span>
          <div className="flex self-stretch justify-start items-start flex-col gap-0.5 py-0.5">
            <div className="flex self-stretch justify-between items-center flex-row">
              <span className="text-[#374151] text-[9px]">Total</span>
              <div className="flex justify-start items-center flex-row gap-1">
                <span className="text-[#065BA9] text-[10px] text-center font-medium">{data?.yesterday?.total?.toLocaleString(undefined, { maximumFractionDigits: 0 })}</span>
                <span className="text-[#788796] text-[9px]">kWh</span>
              </div>
            </div>
            <div className="flex self-stretch justify-between items-center flex-row">
              <span className="text-[#374151] text-[9px]">Plant</span>
              <div className="flex justify-start items-center flex-row gap-1">
                <span className="text-[#065BA9] text-[10px] text-center font-medium">{data?.yesterday?.plant?.toLocaleString(undefined, { maximumFractionDigits: 0 })}</span>
                <span className="text-[#788796] text-[9px]">kWh</span>
              </div>
            </div>
            <div className="flex self-stretch justify-between items-center flex-row">
              <span className="text-[#374151] text-[9px]">Air-Side</span>
              <div className="flex justify-start items-center flex-row gap-1">
                <span className="text-[#065BA9] text-[10px] text-center font-medium">{data?.yesterday?.airSide?.toLocaleString(undefined, { maximumFractionDigits: 0 })}</span>
                <span className="text-[#788796] text-[9px]">kWh</span>
              </div>
            </div>
          </div>
          <div className="w-full flex justify-center items-center">
            <div className="inline-flex justify-center gap-0.5">
              <div className="h-4 px-1 bg-[#EDEFF9] rounded flex items-center gap-1">
                <div className="text-[#5E5E5E] text-[8px] font-normal">DBT</div>
                <div className="text-[#0E7EE4] text-[8px] font-bold">
                  {data.yesterday.dbt != null ? data.yesterday.dbt.toFixed(1) + ' °F' : '-'}
                </div>
              </div>
              <div className="h-4 px-1 bg-[#EDEFF9] rounded flex items-center gap-1">
                <div className="text-[#5E5E5E] text-[8px] font-normal">RH</div>
                <div className="text-[#0E7EE4] text-[8px] font-bold">
                  {data.yesterday.humidity != null ? data.yesterday.humidity.toFixed(1) + ' %' : '-'}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex flex-1 justify-start items-start flex-col gap-1 p-1.5 bg-[#F9FAFF] border-solid border-[#EDEFF9] border rounded-lg">
          <span className="text-[#788796] text-[9px] text-center">Today</span>
          <div className="flex self-stretch justify-start items-start flex-col gap-0.5 py-0.5">
            <div className="flex self-stretch justify-between items-center flex-row">
              <span className="text-[#374151] text-[9px]">Total</span>
              <div className="flex justify-start items-center flex-row gap-1">
                <span className="text-[#065BA9] text-[10px] text-center font-medium">{data?.today?.total?.toLocaleString(undefined, { maximumFractionDigits: 0 })}</span>
                <span className="text-[#788796] text-[9px]">kWh</span>
              </div>
            </div>
            <div className="flex self-stretch justify-between items-center flex-row">
              <span className="text-[#374151] text-[9px]">Plant</span>
              <div className="flex justify-start items-center flex-row gap-1">
                <span className="text-[#065BA9] text-[10px] text-center font-medium">{data?.today?.plant?.toLocaleString(undefined, { maximumFractionDigits: 0 })}</span>
                <span className="text-[#788796] text-[9px]">kWh</span>
              </div>
            </div>
            <div className="flex self-stretch justify-between items-center flex-row">
              <span className="text-[#374151] text-[9px]">Air-Side</span>
              <div className="flex justify-start items-center flex-row gap-1">
                <span className="text-[#065BA9] text-[10px] text-center font-medium">{data?.today?.airSide?.toLocaleString(undefined, { maximumFractionDigits: 0 })}</span>
                <span className="text-[#788796] text-[9px]">kWh</span>
              </div>
            </div>
          </div>
          <div className="w-full flex justify-center items-center">
            <div className="inline-flex justify-center gap-0.5">
              <div className="h-4 px-1 bg-[#EDEFF9] rounded flex items-center gap-1">
                <div className="text-[#5E5E5E] text-[8px] font-normal">DBT</div>
                <div className="text-[#0E7EE4] text-[8px] font-bold">
                  {data.today.dbt != null ? data.today.dbt.toFixed(1) + ' °F' : '-'}
                </div>
              </div>
              <div className="h-4 px-1 bg-[#EDEFF9] rounded flex items-center gap-1">
                <div className="text-[#5E5E5E] text-[8px] font-normal">RH</div>
                <div className="text-[#0E7EE4] text-[8px] font-bold">
                  {data.today.humidity != null ? data.today.humidity.toFixed(1) + ' %' : '-'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnergyUsageCompareCard;