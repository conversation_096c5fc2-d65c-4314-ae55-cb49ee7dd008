import React, { useState } from 'react';
import ChillerImage from "@/assets/chiller_image.png";
import PumpImage from "@/assets/pump_image.png";
import CoolingTowerImage from "@/assets/cooling_tower_image.png";
import PlantEquipmentModal from "./PlantEquipmentModal";
import { RiArrowRightSLine } from "react-icons/ri";
import { useDevice } from '@/contexts/DeviceContext';
import { useRealtime } from '@/contexts/RealtimeContext';

interface EquipmentTypeInfo {
  code: string;
  name: string;
  model: string;
}

const PlantEquipmentCard: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { getDevicesByType, isDeviceUnderMaintenance } = useDevice();
  const { getValue } = useRealtime();

  const equipmentTypes: EquipmentTypeInfo[] = [
    { code: 'CH', name: 'Chiller', model: 'chiller' },
    { code: 'CHP', name: 'Chilled Water Pump', model: 'pchp' },
    { code: 'CDP', name: 'Condenser Water Pump', model: 'cdp' },
    { code: 'CT', name: 'Cooling Tower', model: 'ct' },
  ];

  const devicesByType = getDevicesByType();

  // Function to get status styling
  const getStatusStyling = (deviceId: string) => {
    // Check if device is under maintenance
    if (isDeviceUnderMaintenance(deviceId)) {
      return {
        bg: 'bg-[#FBDFB2]',
        border: 'border-[#F9C36A]',
        text: 'text-[#FF7A00]',
        hasBorder: true
      };
    }

    // Get status from realtime context
    const statusRead = getValue(deviceId, 'status_read');
    const alarm = getValue(deviceId, 'alarm');

    if (alarm) {
      return {
        bg: 'bg-[#F7A19B]',
        border: 'border-[#EF4337]',
        text: 'text-[#FFFFFF]',
        hasBorder: true
      };
    }

    switch (statusRead) {
      case 1:
        return {
          bg: 'bg-success/50',
          border: 'border-success',
          text: 'text-success',
          hasBorder: true
        };
      case 0:
        return {
          bg: 'bg-[#EDEFF9]',
          border: '',
          text: 'text-[#B4B4B4]',
          hasBorder: false
        };
      default:
        return {
          bg: 'bg-[#EDEFF9]',
          border: '',
          text: 'text-[#B4B4B4]',
          hasBorder: false
        };
    }
  };

  // Group equipment by type
  const groupedEquipment = equipmentTypes.map(type => {
    return {
      ...type,
      items: devicesByType[type.model] || []
    };
  });

  return (
    <div className="flex flex-col alto-card p-[10px] h-full bg-background overflow-hidden">
      <div className="flex items-center pb-[8px] gap-1">
        <span className="text-[#065BA9] text-xs font-semibold">System Status</span>
        <button
          className="h-[24px] w-[24px] bg-white4 rounded-[6px] flex justify-center items-center hover:bg-card"
          onClick={() => setIsModalOpen(true)}
        >
          <RiArrowRightSLine
            className="h-[12px] w-[12px]"
            color="#065BA9"
          />
        </button>
      </div>
      
      <div className="flex justify-center items-start flex-col gap-1.5 flex-grow">
        {groupedEquipment.map((group) => (
          <div key={group.code} className="flex justify-center items-start flex-col gap-0.5">
            <div className="flex self-stretch justify-start items-center flex-row gap-2">
              <span className="text-[#5E5E5E] text-xs font-semibold">{group.code}</span>
              <span className="text-[#788796] text-[10px]">{group.name}</span>
            </div>
            <div className="flex self-stretch justify-start items-center flex-wrap gap-1">
              {group.items.map((device, index) => {
                const styling = getStatusStyling(device.deviceId);
                const deviceNumber = device.deviceId.includes('_') ? device.deviceId.split('_').slice(1).join('-') : 
                                    device.deviceId.includes('-') ? device.deviceId.split('-').slice(1).join('-') : '-';
                const isNewRow = index > 0 && index % 8 === 0;
                
                return (
                  <div 
                    key={device.id} 
                    className={`flex justify-center items-center flex-col gap-px pt-0.5 pb-[1px] px-[3px] ${styling.bg} ${styling.hasBorder ? 'border-solid border ' + styling.border : ''} rounded-md w-[33px] ${isNewRow ? 'mt-1' : ''}`}
                  >
                    <img 
                      src={group.model === 'chiller' ? ChillerImage : group.model === 'ct' ? CoolingTowerImage : PumpImage} 
                      className={`w-[28px] h-[21px] object-contain ${styling.text !== 'text-success' && 'grayscale'}`}
                    />
                    <span className={`${styling.text} text-[10px] text-center font-semibold`}>{deviceNumber}</span>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
      
      <div className="flex justify-start items-center flex-row gap-2 mt-[10px]">
        <div className="flex justify-center items-center flex-row gap-1">
          <div className="bg-muted rounded-[100px] w-[6px] h-[6px]" style={{ width: '6px' }}></div>
          <span className="text-muted text-[8px]">Standby</span>
        </div>
        <div className="flex justify-center items-center flex-row gap-1">
          <div className="bg-success rounded-[100px] w-[6px] h-[6px]" style={{ width: '6px' }}></div>
          <span className="text-muted text-[8px]">Running</span>
        </div>
        <div className="flex justify-center items-center flex-row gap-1">
          <div className="bg-warning rounded-[100px] w-[6px] h-[6px]" style={{ width: '6px' }}></div>
          <span className="text-muted text-[8px]">Maintenance</span>
        </div>
        <div className="flex justify-center items-center flex-row gap-1">
          <div className="bg-danger rounded-[100px] w-[6px] h-[6px]" style={{ width: '6px' }}></div>
          <span className="text-muted text-[8px]">Alarm</span>
        </div>
      </div>

      {/* Plant Equipment Modal */}
      <PlantEquipmentModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
};

export default PlantEquipmentCard;
