import React, { useState, useMemo, useEffect } from "react";
import { DeviceData } from "@/services/deviceService";
import { useRealtime } from "@/contexts/RealtimeContext";

interface AHUDetailsPanelProps {
  ahu: DeviceData;
  controlledVavs?: DeviceData[];
}

const AHUDetailsPanel: React.FC<AHUDetailsPanelProps> = ({
  ahu,
  controlledVavs = [],
}) => {
  const { getValue } = useRealtime();
  const [mode, setMode] = useState<string>("");

  // Extract floor number from AHU ID or name
  const floorMatch = ahu.deviceId.match(/(\d+)/);
  const floorNumber = floorMatch ? floorMatch[0] : "";

  // Determine the corrects VSD device ID based on floor
  const vsdDeviceId = `vsd_ahu_${floorNumber}`;

  // Get metrics from realtime data - updated with correct datapoint mappings
  const powerConsumption = getValue(vsdDeviceId, "power");

  // Updated datapoint mappings
  const staticPressure = getValue(ahu.deviceId, "static_pressure");

  // Calculate total air flow as the sum of all VAV flow rates
  const totalAirFlow = useMemo(() => {
    if (!controlledVavs || controlledVavs.length === 0) {
      // Fallback to the AHU's total_air_flow_rate_setpoint if no VAVs are found
      return getValue(ahu.deviceId, "total_air_flow_rate_setpoint_read");
    }

    let sum = 0;
    let validValues = 0;

    controlledVavs.forEach((vav) => {
      const flowRate = getValue(vav.deviceId, "air_flow_rate_setpoint_read");
      if (flowRate !== undefined && flowRate !== null) {
        sum += flowRate;
        validValues++;
      }
    });

    // Return the sum if we have valid values, otherwise return the AHU's value
    return validValues > 0
      ? sum
      : getValue(ahu.deviceId, "total_air_flow_rate_setpoint_read");
  }, [controlledVavs, getValue, ahu.deviceId]);
  const saTemp = getValue(ahu.deviceId, "supply_air_temperature");
  const saHumidity = getValue(ahu.deviceId, "supply_air_humidity");
  const returnAirTemp = getValue(ahu.deviceId, "return_air_temperature");
  // Updated RA humidity and CO2 datapoints
  const raRhPV = getValue(ahu.deviceId, "return_air_humidity");
  const raCO2 = getValue(ahu.deviceId, "co2_level");
  const chilledWaterSupply = getValue(
    ahu.deviceId,
    "chilled_water_supply_temperature"
  );
  const chilledWaterReturn = getValue(
    ahu.deviceId,
    "chilled_water_return_temperature"
  );
  const coolingEnergyValve = getValue(
    ahu.deviceId,
    "cooling_valve_energy"
  );
  const coolingValve = getValue(ahu.deviceId, "cooling_valve_position");
  const waterFlowPV = getValue(ahu.deviceId, "cooling_valve_flow_rate");
  const manualModeValue = Number(getValue(ahu.deviceId, "manual_mode"));
  const bmsModeValue = Number(getValue(ahu.deviceId, "bms_mode"));

  // Updated alarm datapoint mappings
  const motorAlarm = getValue(ahu.deviceId, "motor_alarm") === 1;
  const preFilterAlarm =
    getValue(ahu.deviceId, "pre_filter_pressure_alarm") === 1;
  const medFilterAlarm =
    getValue(ahu.deviceId, "med_filter_pressure_alarm") === 1;

  useEffect(() => {
    if (manualModeValue === 1 && bmsModeValue === 0) {
      setMode("Manual");
    } else if (bmsModeValue === 1 && manualModeValue === 0) {
      setMode("BMS");
    } else {
      setMode("Error");
    }
  }, [manualModeValue, bmsModeValue]);

  return (
    <div className="mb-6 h-fit">
      <div className="flex items-center gap-2 w-full h-full">
        {/* Alarm */}
        <div className="border border-[#EDEFF9] bg-[#F9FAFF] rounded-[8px] p-[6px] shrink-0 h-full">
          <div className="text-[10px] text-[#065BA9] font-medium">Alarm</div>
          <div className="flex items-center gap-1 mt-1">
            <div className="flex items-center justify-between gap-2 rounded-[4px] bg-[#EDEFF9] px-[6px] py-[4px] shrink-0">
              <span className="ml-1 text-xs text-slate-600">Fault</span>
              <div
                className={`w-2 h-2 rounded-full ${
                  motorAlarm ? "bg-red-500" : "bg-[#14B8B4]"
                }`}
              ></div>
            </div>
            <div className="flex items-center justify-between gap-2 rounded-[4px] bg-[#EDEFF9] px-[6px] py-[4px] shrink-0">
              <span className="ml-1 text-xs text-slate-600">Pre. Filter</span>
              <div
                className={`w-2 h-2 rounded-full ${
                  preFilterAlarm ? "bg-red-500" : "bg-[#14B8B4]"
                }`}
              ></div>
            </div>
            <div className="flex items-center justify-between gap-2 rounded-[4px] bg-[#EDEFF9] px-[6px] py-[4px] shrink-0">
              <span className="ml-1 text-xs text-slate-600">Med. Filter</span>
              <div
                className={`w-2 h-2 rounded-full ${
                  medFilterAlarm ? "bg-red-500" : "bg-[#14B8B4]"
                }`}
              ></div>
            </div>
          </div>
        </div>

        {/* Mode */}
        <div className="border border-[#EDEFF9] bg-[#F9FAFF] rounded-[8px] p-[6px] w-full h-full">
          <div className="text-[10px] text-[#065BA9] font-medium">Mode</div>
          <div className="text-[#0E7EE4] text-[16px] font-semibold mt-1">
            {mode}
          </div>
        </div>

        {/* Power Consumption */}
        <div className="border border-[#EDEFF9] bg-[#F9FAFF] rounded-[8px] p-[6px] w-full">
          <div className="text-[10px] text-[#065BA9] font-medium">
            Power Consumption
          </div>
          <div className="flex items-center justify-between mt-1">
            <div className="text-[#0E7EE4] text-[16px] font-semibold">
              {powerConsumption !== undefined
                ? powerConsumption.toFixed(1)
                : "--"}
            </div>
            <span className="text-[#788796] text-[13px]">kW</span>
          </div>
        </div>
      </div>
      <div className="mt-[10px]">
        <div className="bg-white overflow-hidden rounded-[6px]">
          <table className="w-full border border-[#DBE4FF]">
            <thead>
              <tr className="bg-[#F9FAFF]">
                <th className="border border-[#E5E7EB] text-[10px] text-[#788796] text-left py-1 px-2">
                  Parameter
                </th>
                <th className="border border-[#E5E7EB] text-[10px] text-[#788796] text-left py-1 px-2">
                  Value
                </th>
                <th className="border border-[#E5E7EB] text-[10px] text-[#788796] text-left py-1 px-2">
                  Unit
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Static Pressure
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {staticPressure !== undefined
                    ? staticPressure.toFixed(1)
                    : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  Pa
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Total Air Flow
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {totalAirFlow !== undefined
                    ? totalAirFlow.toFixed(1)
                    : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  L/s
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Supply Air Temperature
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {saTemp !== undefined ? saTemp.toFixed(1) : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  °C
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Supply Air Humidity
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {saHumidity !== undefined ? saHumidity.toFixed(1) : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  %RH
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Return Air Temperature
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {returnAirTemp !== undefined
                    ? returnAirTemp.toFixed(1)
                    : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  °C
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Return Air Humidity
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {raRhPV !== undefined ? raRhPV.toFixed(1) : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  %RH
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Return CO₂
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {raCO2 !== undefined ? raCO2.toFixed(1) : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  ppm
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Cooling Valve Energy
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {coolingEnergyValve !== undefined
                    ? coolingEnergyValve.toFixed(1)
                    : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  MJ/h
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Chilled Water Supply
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {chilledWaterSupply !== undefined
                    ? chilledWaterSupply.toFixed(1)
                    : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  °C
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Chilled Water Return
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {chilledWaterReturn !== undefined
                    ? chilledWaterReturn.toFixed(1)
                    : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  °C
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Water Flow
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {waterFlowPV !== undefined ? waterFlowPV.toFixed(1) : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  L/s
                </td>
              </tr>
              <tr className="border-t border-[#E5E7EB]">
                <td className="py-1 px-2 text-[10px] text-[#212529]">
                  Cooling Valve%
                </td>
                <td className="border border-[#E5E7EB] text-right py-1 px-4 text-[10px] text-[#212529]">
                  {coolingValve !== undefined
                    ? coolingValve.toFixed(1)
                    : "##.#"}
                </td>
                <td className="text-left py-1 px-2 text-[10px] text-[#212529]">
                  %
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AHUDetailsPanel;
