// Schedule Settings Modal Component
// Handles the creation and management of chiller schedules with a visual timeline interface
// Supports drag-and-drop functionality for creating and modifying schedule events

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Settings2, GripVertical, Info, Trash2 } from 'lucide-react';
import { toast } from "@/components/ui/use-toast";
import { useRealtime } from '@/contexts/RealtimeContext';
import { ApiSchedule, WeeklySchedule, TimeRange } from '../types';
import { Input } from "@/components/ui/input";
import { format, parse } from "date-fns";

const DAYS = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
const DAYS_SHORT = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
const MINUTES_PER_HOUR = 60;
const MINUTES_PER_DAY = 24 * MINUTES_PER_HOUR;
const MIN_EVENT_DURATION = 15; // Minimum duration in minutes
const TIME_SLOTS = Array.from({ length: 24 }, (_, hour) => hour);

// Internal representation of an event for grid rendering
interface ScheduleEvent {
  id: string;
  day: string; // lowercase day name (monday, tuesday, etc.)
  startMinute: number;
  endMinute: number;
  value: boolean | number | string;
}

interface ScheduleSettingsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  schedule: ApiSchedule | null;
  onSave: (weeklySchedule: WeeklySchedule) => void;
}

export function ScheduleSettingsModal({ 
  open, 
  onOpenChange, 
  schedule,
  onSave 
}: ScheduleSettingsModalProps) {
  const { realtimeData } = useRealtime();
  
  // Internal state (grid-friendly format)
  const [events, setEvents] = useState<ScheduleEvent[]>([]);
  
  // Grid interaction state
  const [isDragging, setIsDragging] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<ScheduleEvent | null>(null);
  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [dragStart, setDragStart] = useState<{ day: string; minute: number } | null>(null);
  const [dragEnd, setDragEnd] = useState<{ day: string; minute: number } | null>(null);
  const [hoveredDay, setHoveredDay] = useState<string | null>(null);
  const [draggedEvent, setDraggedEvent] = useState<ScheduleEvent | null>(null);
  const [dragOffset, setDragOffset] = useState<number>(0);
  const [resizeType, setResizeType] = useState<'top' | 'bottom' | null>(null);
  const [isCreatingEvent, setIsCreatingEvent] = useState(false);
  const [editingValue, setEditingValue] = useState<boolean | string | number>(true); // Default value for new events
  
  // Refs for interaction handling
  const dragTimeoutRef = React.useRef<number | null>(null);
  const initialPosRef = React.useRef<{ x: number; y: number } | null>(null);
  const isDraggingRef = React.useRef(false);
  const mouseDownRef = React.useRef(false);
  const cleanupRef = React.useRef<() => void>();

  // Add these helper functions after the existing helper functions
  const parseTimeInput = (timeStr: string) => {
    try {
      const parsed = parse(timeStr, 'HH:mm', new Date());
      const hours = parsed.getHours();
      const minutes = parsed.getMinutes();
      return hours * 60 + minutes;
    } catch (e) {
      return null;
    }
  };

  const formatTimeInput = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return format(new Date().setHours(hours, mins), 'HH:mm');
  };

  // Add these state variables after the existing state declarations
  const [startTimeInput, setStartTimeInput] = useState('');
  const [endTimeInput, setEndTimeInput] = useState('');
  const [timeError, setTimeError] = useState<string | null>(null);

  // Add this effect to update input fields when selected event changes
  useEffect(() => {
    if (selectedEvent) {
      setStartTimeInput(formatTimeInput(selectedEvent.startMinute));
      setEndTimeInput(formatTimeInput(selectedEvent.endMinute));
      setTimeError(null);
    }
  }, [selectedEvent]);

  // Add this function to handle time input changes
  const handleTimeChange = (type: 'start' | 'end', value: string) => {
    if (type === 'start') {
      setStartTimeInput(value);
    } else {
      setEndTimeInput(value);
    }

    const startMinutes = parseTimeInput(value);

    if (startMinutes === null) {
      setTimeError('Invalid time format. Use HH:mm (24-hour format)');
      return;
    }

    setTimeError(null);
  };

  // Update the updateEventValue function to handle time changes
  const updateEventValue = (eventId: string, newValue: boolean | string | number) => {
    const startMinutes = parseTimeInput(startTimeInput);
    const endMinutes = parseTimeInput(endTimeInput);

    if (startMinutes === null || endMinutes === null || startMinutes >= endMinutes) {
      setTimeError('Invalid time range');
      return;
    }

    setEvents(events.map(event => 
      event.id === eventId 
        ? { 
            ...event, 
            value: newValue,
            startMinute: startMinutes,
            endMinute: endMinutes
          } 
        : event
    ));
    setSelectedEvent(null);
    setConfigModalOpen(false);
    setTimeError(null);
  };

  // Convert from API weekly_schedule format to internal events array
  const convertWeeklyScheduleToEvents = (weeklySchedule: WeeklySchedule): ScheduleEvent[] => {
    const result: ScheduleEvent[] = [];
    
    // Process each day's time ranges
    Object.entries(weeklySchedule).forEach(([day, timeRanges]) => {
      timeRanges.forEach((range: TimeRange, index: number) => {
        // Convert HH:MM time to minutes
        const startParts = range.start_time.split(':').map(Number);
        const endParts = range.end_time.split(':').map(Number);
        
        const startMinute = startParts[0] * 60 + startParts[1];
        const endMinute = endParts[0] * 60 + endParts[1];
        
        result.push({
          id: `${day}-${index}`, // Generate an ID for the event
          day,
          startMinute,
          endMinute,
          value: range.value
        });
      });
    });
    
    return result;
  };

  // Convert from internal events array back to API weekly_schedule format
  const convertEventsToWeeklySchedule = (eventList: ScheduleEvent[]): WeeklySchedule => {
    // Initialize an empty week
    const result: WeeklySchedule = {
      monday: [],
      tuesday: [],
      wednesday: [],
      thursday: [],
      friday: [],
      saturday: [],
      sunday: []
    };
    
    // Group events by day and convert to TimeRange format
    eventList.forEach(event => {
      const startHour = Math.floor(event.startMinute / 60);
      const startMin = event.startMinute % 60;
      const endHour = Math.floor(event.endMinute / 60);
      const endMin = event.endMinute % 60;
      
      const startTime = `${startHour.toString().padStart(2, '0')}:${startMin.toString().padStart(2, '0')}`;
      const endTime = `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;
      
      const dayKey = event.day as keyof WeeklySchedule;
      result[dayKey].push({
        start_time: startTime,
        end_time: endTime,
        value: event.value
      });
    });
    
    // Sort each day's events by start time
    for (const day of DAYS) {
      const dayKey = day as keyof WeeklySchedule;
      result[dayKey].sort((a, b) => {
        return a.start_time.localeCompare(b.start_time);
      });
    }
    
    return result;
  };

  // Load events when schedule prop changes
  useEffect(() => {
    if (schedule) {
      console.log("Loading schedule settings:", schedule.name);
      const convertedEvents = convertWeeklyScheduleToEvents(schedule.weekly_schedule);
      setEvents(convertedEvents);
    } else {
      setEvents([]);
    }
  }, [schedule]);

  // Add window-level mouse up handler
  React.useEffect(() => {
    const cleanup = () => {
      mouseDownRef.current = false;
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
        dragTimeoutRef.current = null;
      }
      setDraggedEvent(null);
      setResizeType(null);
      setDragOffset(0);
      setIsCreatingEvent(false);
      setIsDragging(false);
      isDraggingRef.current = false;
      initialPosRef.current = null;
      setDragStart(null);
      setDragEnd(null);
    };

    const handleGlobalMouseUp = () => cleanup();
    window.addEventListener('mouseup', handleGlobalMouseUp);
    cleanupRef.current = cleanup;

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
      cleanup();
    };
  }, []);

  // Helper functions for time conversion and formatting
  const snapToGrid = (minutes: number) => Math.round(minutes / 15) * 15;

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // Mouse event handlers for grid interaction
  const getMinuteFromMouseEvent = (e: React.MouseEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    if (!target) return 0;
    const rect = target.getBoundingClientRect();
    const relativeY = e.clientY - rect.top;
    const totalMinutes = snapToGrid(Math.floor((relativeY / rect.height) * MINUTES_PER_DAY));
    return Math.max(0, Math.min(totalMinutes, MINUTES_PER_DAY - 1));
  };

  const getMinuteFromGridPosition = (e: React.MouseEvent<HTMLDivElement>, gridElement: HTMLDivElement) => {
    const rect = gridElement.getBoundingClientRect();
    const relativeY = e.clientY - rect.top;
    const totalMinutes = snapToGrid(Math.floor((relativeY / rect.height) * MINUTES_PER_DAY));
    return Math.max(0, Math.min(totalMinutes, MINUTES_PER_DAY - 1));
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>, day: string, existingEvent?: ScheduleEvent) => {
    e.stopPropagation();
    e.preventDefault();
    mouseDownRef.current = true;
    initialPosRef.current = { x: e.clientX, y: e.clientY };
    isDraggingRef.current = false;
    
    if (existingEvent) {
      const target = e.currentTarget;
      const rect = target.getBoundingClientRect();
      const relativeY = e.clientY - rect.top;
      setDragOffset(relativeY);
    } else {
      setIsCreatingEvent(true);
      const minute = getMinuteFromMouseEvent(e);
      setDragStart({ day, minute });
      setDragEnd({ day, minute });
    }
  };

  const handleClick = (e: React.MouseEvent<HTMLDivElement>, existingEvent?: ScheduleEvent) => {
    e.stopPropagation();
    e.preventDefault();
    
    // Only handle click if we haven't moved significantly
    if (isDraggingRef.current || draggedEvent || isDragging) return;
    
    const target = e.currentTarget;
    if (!target) return;
    
    if (existingEvent) {
      setSelectedEvent(existingEvent);
      setEditingValue(existingEvent.value);
      setConfigModalOpen(true);
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>, day: string, height: number) => {
    e.stopPropagation();
    e.preventDefault();

    if (!mouseDownRef.current) return;

    // Find the grid container
    const gridContainer = e.currentTarget.closest('.grid-container') as HTMLDivElement;
    if (!gridContainer) return;
    
    // Only process if we have an initial position
    if (initialPosRef.current) {
      const dx = Math.abs(e.clientX - initialPosRef.current.x);
      const dy = Math.abs(e.clientY - initialPosRef.current.y);
      
      // Start dragging only if moved significantly
      if ((dx > 3 || dy > 3) && !isDraggingRef.current) {
        isDraggingRef.current = true;
        setIsDragging(true);
      }
    }
    
    const minute = getMinuteFromMouseEvent(e);
    
    if (draggedEvent) {
      if (resizeType) {
        const minute = getMinuteFromGridPosition(e, gridContainer);
        const existingEvents = events.filter(e => e.day === day && e.id !== draggedEvent.id);
        setEvents(events.map(event => {
          if (event.id === draggedEvent.id) {
            let newStart = event.startMinute;
            let newEnd = event.endMinute;
            
            if (resizeType === 'top') {
              newStart = snapToGrid(Math.min(minute, event.endMinute - MIN_EVENT_DURATION));
            } else {
              newEnd = snapToGrid(Math.max(minute, event.startMinute + MIN_EVENT_DURATION));
            }
            
            // Check for overlaps
            const hasOverlap = existingEvents.some(e => 
              (newStart < e.endMinute && newEnd > e.startMinute)
            );
            
            if (!hasOverlap) {
              return { ...event, startMinute: newStart, endMinute: newEnd };
            }
          }
          return event;
        }));
      } else {
        const duration = draggedEvent.endMinute - draggedEvent.startMinute;
        const newStartMinute = snapToGrid(Math.max(0, Math.min(minute - dragOffset / (height / MINUTES_PER_DAY), MINUTES_PER_DAY - duration)));
        const newEndMinute = newStartMinute + duration;
        
        // Check for overlaps with other events
        const existingEvents = events.filter(e => e.day === day && e.id !== draggedEvent.id);
        const hasOverlap = existingEvents.some(e => 
          (newStartMinute < e.endMinute && newEndMinute > e.startMinute)
        );
        
        if (!hasOverlap) {
          setEvents(events.map(event => 
            event.id === draggedEvent.id 
              ? {
                  ...event,
                  day,
                  startMinute: newStartMinute,
                  endMinute: newEndMinute
                }
              : event
          ));
        }
      }
      return;
    }

    if (isDragging) {
      const minute = getMinuteFromMouseEvent(e);
      setDragEnd({ day, minute });
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    e.preventDefault();

    const wasDragging = isDraggingRef.current;
    setIsCreatingEvent(false);
    cleanupRef.current?.();
    
    // If we haven't started dragging, this was a click
    if (!wasDragging) {
      return;
    }
    
    if (isDragging && dragStart && dragEnd) {
      const duration = Math.abs(dragEnd.minute - dragStart.minute);
      // Always create an event with at least MIN_EVENT_DURATION
      const startMinute = snapToGrid(Math.min(dragStart.minute, dragEnd.minute));
      const endMinute = Math.max(startMinute + MIN_EVENT_DURATION, startMinute + duration);
      
      // Check for overlaps
      const existingEvents = events.filter(e => e.day === dragStart.day);
      const hasOverlap = existingEvents.some(e => 
        (startMinute < e.endMinute && endMinute > e.startMinute)
      );
      
      if (!hasOverlap) {
        const newEvent: ScheduleEvent = {
          id: `${dragStart.day}-${Date.now()}`,
          day: dragStart.day,
          startMinute,
          endMinute,
          value: editingValue // Use the current editing value
        };
        setEvents(prev => [...prev, newEvent]);
      }
    }
  };

  // Event management functions
  const copySchedule = (fromDay: string, toDay: string) => {
    const sourceDayEvents = events.filter(event => event.day === fromDay);
    const newEvents = sourceDayEvents.map(event => ({
      id: `${toDay}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      day: toDay,
      startMinute: event.startMinute,
      endMinute: event.endMinute,
      value: event.value
    }));
    setEvents(prev => [...prev.filter(e => e.day !== toDay), ...newEvents]);
  };

  const applyToAllDays = (fromDay: string) => {
    const otherDays = DAYS.filter(day => day !== fromDay);
    otherDays.forEach(day => copySchedule(fromDay, day));
  };

  const clearDaySchedule = (day: string) => {
    setEvents(prev => prev.filter(event => event.day !== day));
  };

  const deleteEvent = (eventId: string) => {
    setEvents(events.filter(event => event.id !== eventId));
  };

  // Save changes handler
  const handleSaveChanges = () => {
    if (!schedule) return;
    
    // Convert internal events to API weekly_schedule format
    const weeklySchedule = convertEventsToWeeklySchedule(events);
    
    // Call the onSave callback with the updated weekly_schedule
    onSave(weeklySchedule);
    
    toast({ 
      title: "Schedule Saved", 
      description: "Schedule configuration saved successfully.", 
      variant: "default" 
    });
  };

  // Day name conversion helpers
  const getDayShortName = (dayName: string) => {
    const index = DAYS.indexOf(dayName);
    return index >= 0 ? DAYS_SHORT[index] : dayName.substring(0, 3).toUpperCase();
  };

  return (<>
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[1400px] w-full h-[95vh] flex flex-col bg-white border border-[#DBE4FF] rounded-lg shadow-[1px_3px_20px_0px_rgba(57,124,221,0.30)]">
        <DialogHeader className="px-6 py-1 border-b border-[#DBE4FF]">
          <DialogTitle className="text-[#065BA9] text-base font-semibold">
            Schedule Settings: {schedule ? schedule.name : 'Loading...'}
          </DialogTitle>
        </DialogHeader>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full px-6 pt-1 pb-0">
            <div className="grid grid-cols-[auto_1fr] gap-4">
              {/* Time labels */}
              <div className="pt-6">
                {TIME_SLOTS.map(hour => (
                  <div 
                    key={hour} 
                    className="h-8 text-sm text-[#5E5E5E] pr-2 text-right"
                  > 
                    {hour.toString().padStart(2, '0')}:00
                  </div>
                ))}
              </div>

              {/* Calendar grid */}
              <div className="relative">
                {/* Day headers with copy buttons - This stays fixed */}
                <div className="grid grid-cols-7 gap-[1px] mb-1 sticky top-0 bg-white z-10">
                  {DAYS.map((day, index) => (
                    <div 
                      key={day} 
                      className="relative group"
                      onMouseEnter={() => setHoveredDay(day)}
                      onMouseLeave={() => setHoveredDay(null)}
                    >
                      <div className="flex items-center justify-between px-2 py-0.5">
                        <span className="font-medium text-[#065BA9]">{DAYS_SHORT[index]}</span>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Time grid */}
                <div 
                  className="grid-container grid grid-cols-7 gap-[1px] bg-[#F9FAFF] relative"
                  style={{ height: `${TIME_SLOTS.length * 32}px` }}
                >
                  {DAYS.map(day => (
                    <div 
                      key={day}
                      className="relative h-full"
                      onMouseLeave={(e) => handleMouseUp(e)}
                      onMouseDown={(e) => handleMouseDown(e, day)}
                      onClick={(e) => handleClick(e)}
                      onMouseMove={(e) => handleMouseMove(e, day, TIME_SLOTS.length * 32)}
                      onMouseUp={(e) => handleMouseUp(e)}
                    >
                      {TIME_SLOTS.map(hour => (
                        <div key={`${day}-${hour}`} className="h-8 border-b border-muted-foreground/20 relative" />
                      ))}
                    </div>
                  ))}

                  {/* Events */}
                  {events.map(event => (
                    <div
                      key={event.id}
                      className={`absolute bg-primary/20 border border-primary rounded-md ${isDragging || draggedEvent ? 'pointer-events-none' : ''}`}
                      style={{
                        cursor: draggedEvent?.id === event.id 
                          ? (resizeType ? (resizeType === 'top' ? 'n-resize' : 's-resize') : 'move')
                          : 'pointer',
                        left: `${(DAYS.indexOf(event.day) / 7) * 100}%`,
                        top: `${(event.startMinute / MINUTES_PER_HOUR) * 32}px`,
                        width: `${100 / 7}%`,
                        height: `${((event.endMinute - event.startMinute) / MINUTES_PER_HOUR) * 32}px`,
                        backgroundColor: typeof event.value === 'boolean' && event.value 
                          ? 'rgba(14, 126, 228, 0.2)' // Active (true) events
                          : 'rgba(229, 62, 62, 0.2)',  // Inactive (false) events
                        borderColor: typeof event.value === 'boolean' && event.value
                          ? 'rgb(14, 126, 228)'       // Active border
                          : 'rgb(229, 62, 62)',        // Inactive border
                      }}
                      onMouseEnter={(e) => {
                        if (isDragging || draggedEvent) {
                          e.stopPropagation();
                          e.preventDefault();
                        }
                      }}
                      onMouseDown={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        mouseDownRef.current = true;
                        handleMouseDown(e, event.day, event);
                        dragTimeoutRef.current = window.setTimeout(() => {
                          if (initialPosRef.current && mouseDownRef.current) {
                            setDraggedEvent(event);
                          }
                        }, 150);
                      }}
                      onClick={(e) => {
                        if (!isDraggingRef.current && !isDragging) {
                          handleClick(e, event);
                        }
                      }}
                    >
                      <div 
                        className="absolute inset-x-0 -top-2 h-4 cursor-n-resize hover:bg-primary/30 z-10"
                        onMouseDown={(e) => {
                          if (isDragging || draggedEvent) return;
                          e.stopPropagation();
                          e.preventDefault();
                          mouseDownRef.current = true;
                          isDraggingRef.current = true;
                          setDraggedEvent(event);
                          setResizeType('top');
                        }}
                      />
                      <div 
                        className="absolute inset-x-0 -bottom-2 h-4 cursor-s-resize hover:bg-primary/30 z-10"
                        onMouseDown={(e) => {
                          if (isDragging || draggedEvent) return;
                          e.stopPropagation();
                          e.preventDefault();
                          mouseDownRef.current = true;
                          isDraggingRef.current = true;
                          setDraggedEvent(event);
                          setResizeType('bottom');
                        }}
                      />
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
                        <GripVertical className="w-4 h-4 pointer-events-none" />
                      </div>
                      <div 
                        className="p-1 text-[10px] leading-tight space-y-0.5 pointer-events-none overflow-hidden"
                      >
                        <div className="font-medium">{`${formatTime(event.startMinute)}-${formatTime(event.endMinute)}`}</div>
                        <div className="flex items-center gap-1">
                          <span className={`font-semibold ${typeof event.value === 'boolean' && event.value ? 'text-green-600' : 'text-red-600'}`}>
                            {typeof event.value === 'boolean' 
                              ? (event.value ? 'ON' : 'OFF') 
                              : String(event.value)}
                          </span>
                          {schedule && schedule.controls && schedule.controls.length > 0 && (
                            <span className="text-xs text-gray-500">
                              ({schedule.controls.length} control{schedule.controls.length > 1 ? 's' : ''})
                            </span>
                          )}
                        </div>
                      </div>
                      <button
                        className="absolute top-1 right-1 p-0.5 rounded-full bg-white/80 hover:bg-white text-red-500 hover:text-red-600 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteEvent(event.id);
                        }}
                        title="Delete event"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  ))}

                  {/* Drag preview */}
                  {isDragging && dragStart && dragEnd && (
                    <div
                      className="absolute bg-primary/30 border border-primary rounded-md pointer-events-none z-50"
                      style={{
                        left: `${(DAYS.indexOf(dragStart.day) / 7) * 100}%`,
                        top: `${(Math.min(dragStart.minute, dragEnd.minute) / MINUTES_PER_HOUR) * 32}px`,
                        width: `${100 / 7}%`,
                        height: `${(Math.abs(dragEnd.minute - dragStart.minute) / MINUTES_PER_HOUR) * 32}px`,
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Fixed Footer */}
        <DialogFooter className="px-6 py-1 border-t border-[#DBE4FF] flex-shrink-0">
          <div className="mr-auto flex items-center gap-1 text-[10px] text-gray-500">
            <Info className="h-3 w-3" />
            <span>Click and drag to create a new time period, or click an existing one to edit</span>
          </div>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="text-[#065BA9] border-[#DBE4FF] hover:bg-[#F9FAFF] hover:border-[#0E7EE4] h-7 text-xs"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSaveChanges}
            className="bg-[#0E7EE4] text-white hover:bg-[#0E7EE4]/90 h-7 text-xs"
            disabled={!schedule}
          >
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Event Configuration Modal */}
    <Dialog 
      open={configModalOpen} 
      onOpenChange={(open) => {
        setConfigModalOpen(open);
        if (!open) {
          cleanupRef.current?.();
          setSelectedEvent(null);
        }
      }}
    >
      <DialogContent className="max-w-[400px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings2 className="w-5 h-5" />
            Configure Time Period
          </DialogTitle>
        </DialogHeader>

        {selectedEvent && (
          <div className="space-y-4 py-4">
            <div>
              <div className="text-sm text-muted-foreground mb-1">Time</div>
              <div className="font-medium mb-2">
                {getDayShortName(selectedEvent.day)}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="start-time">Start Time</Label>
                  <Input
                    id="start-time"
                    type="time"
                    value={startTimeInput}
                    onChange={(e) => handleTimeChange('start', e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="end-time">End Time</Label>
                  <Input
                    id="end-time"
                    type="time"
                    value={endTimeInput}
                    onChange={(e) => handleTimeChange('end', e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>
              {timeError && (
                <div className="text-red-500 text-xs mt-1">
                  {timeError}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>Value</Label>
              <div className="flex gap-2">
                <Button 
                  variant={editingValue === true ? "default" : "outline"} 
                  size="sm" 
                  className={editingValue === true ? "bg-green-600 hover:bg-green-700" : ""}
                  onClick={() => setEditingValue(true)}
                >
                  ON
                </Button>
                <Button 
                  variant={editingValue === false ? "default" : "outline"} 
                  size="sm"
                  className={editingValue === false ? "bg-red-600 hover:bg-red-700" : ""}
                  onClick={() => setEditingValue(false)}
                >
                  OFF
                </Button>
              </div>
            </div>

            {schedule && schedule.controls && schedule.controls.length > 0 && (
              <div className="mt-4">
                <Label className="mb-2 block">Affects {schedule.controls.length} control{schedule.controls.length > 1 ? 's' : ''}</Label>
                <div className="text-xs space-y-1 max-h-40 overflow-y-auto bg-slate-50 p-2 rounded-md">
                  {schedule.controls.map((control, i) => (
                    <div key={i} className="flex items-center gap-1">
                      <span className="font-medium">{control.device_id}</span>
                      <span className="text-gray-500">:</span>
                      <span>{control.datapoint}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" size="sm" onClick={() => setConfigModalOpen(false)}>
                Cancel
              </Button>
              <Button 
                variant="default" 
                size="sm" 
                onClick={() => updateEventValue(selectedEvent.id, editingValue)}
              >
                Save
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  </>);
}