import { api } from './api';

export interface MaintenanceTicket {
  id?: string;
  device_id: string;
  description: string;
  technician?: string;
  notes?: string;
  status: 'under_maintenance' | 'completed';
  ticket_started_at?: string;
  ticket_closed_at?: string | null;
  ticket_started_by?: string;
  ticket_closed_by?: string | null;
}

/**
 * Creates a new maintenance ticket for a device
 * @param deviceId The ID of the device to put under maintenance
 * @param description Description of the maintenance
 * @param ticket_started_by The name of the person starting the ticket
 * @returns Promise that resolves with the created ticket
 */
export const createMaintenanceTicket = async (
  deviceId: string,
  description: string,
  ticket_started_by: string,
): Promise<MaintenanceTicket> => {
  try {
    const response = await api.post<MaintenanceTicket>('/maintenance/set_maintenance/', {
      device_id: deviceId,
      description,
      ticket_started_by
    });

    return response.data;
  } catch (error) {
    console.error('Error in createMaintenanceTicket:', error);
    throw error;
  }
};

/**
 * Resolves a maintenance ticket for a device
 * @param deviceId The ID of the maintenance ticket to resolve
 * @param ticket_closed_by The name of the person resolving the ticket
 * @returns Promise that resolves when the ticket is closed
 */
export const resolveMaintenanceTicket = async (
  deviceId: string,
  ticket_closed_by: string
): Promise<void> => {
  try {
    await api.post('/maintenance/resolve_maintenance/', {
      device_id: deviceId,
      ticket_closed_by
    });

    return;
  } catch (error) {
    console.error('Error in resolveMaintenanceTicket:', error);
    throw error;
  }
};
