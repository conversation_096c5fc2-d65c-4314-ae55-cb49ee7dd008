import { useRealtime } from "@/contexts/RealtimeContext";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { InfoIcon } from "@/pages/AltoChatBot/components/icons";

export function CoolingLoadCard() {
  // Mock data waiting for real-time API
  const { getValue } = useRealtime();
  const coolingLoad = getValue('plant', 'cooling_rate');
  const unit = 'RT';
  const runningCapacityPercentage = getValue('plant', 'running_capacity_percentage');

  return (
    <div className="w-full h-full p-2 alto-card flex justify-between items-start overflow-hidden">
      {/* Left Section */}
      <div className="flex-1 h-full flex flex-col justify-between items-start pr-2 overflow-hidden">
        <div className="w-full flex justify-start items-center overflow-x-auto">
          <div className="flex-shrink-0">
            <div className="text-[#065BA9] text-sm font-semibold tracking-[0.01em] whitespace-nowrap">
              Cooling Load
            </div>
          </div>
        </div>

        <div className="w-full flex justify-start items-start overflow-x-auto">
          <div className="flex-shrink-0">
            <div className="text-[#0E7EE4] text-[28px] sm:text-[32px] font-semibold whitespace-nowrap">
              {coolingLoad !== null ? coolingLoad?.toFixed(0).toLocaleString() : '-'}
            </div>
            <div className="text-[#788796] text-[13px] font-normal whitespace-nowrap">
              {unit}
            </div>
          </div>
        </div>
      </div>

      {/* Right Section */}
      <div className="flex-1 h-full py-2 px-2 bg-[#F9FAFF] rounded-md border border-[#EDEFF9] flex flex-col justify-between items-start overflow-hidden">
        <div className="w-full flex justify-start items-center overflow-x-auto">
          <div className="flex-shrink-0 text-[#788796] text-[10px] font-semibold whitespace-nowrap flex items-center gap-1">
            Part-Load
            <TooltipProvider>
              <Tooltip delayDuration={300}>
                <TooltipTrigger asChild>
                  <span className="cursor-help text-[#788796]">
                    <InfoIcon size={12} />
                  </span>
                </TooltipTrigger>
                <TooltipContent side="bottom" align="start" className="bg-white/95 backdrop-blur-sm border border-gray-100 shadow-lg rounded-lg p-3 w-[320px] max-w-[90vw]">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900">Part-Load Percentage</h4>

                    <div className="bg-blue-50 p-2 rounded-md border border-blue-100">
                      <div className="flex items-center justify-center text-center gap-1 flex-wrap">
                        <div className="text-xs font-medium text-blue-800 px-1.5 py-0.5 rounded bg-blue-100">Current Cooling Load</div>
                        <div className="text-gray-500 text-sm">÷</div>
                        <div className="text-xs font-medium text-blue-800 px-1.5 py-0.5 rounded bg-blue-100">Max Capacity</div>
                        <div className="text-gray-500 text-sm">=</div>
                        <div className="text-xs font-medium text-blue-800 px-1.5 py-0.5 rounded bg-blue-100">Part-Load %</div>
                      </div>
                    </div>

                    <p className="text-xs text-gray-600 whitespace-normal leading-relaxed">
                      This indicates how much of the available cooling capacity is currently being utilized.
                    </p>

                    <div className="bg-gray-50 p-2 rounded-md border border-gray-200">
                      <h5 className="text-xs font-medium text-gray-700 mb-1">Example:</h5>
                      <div className="text-xs text-gray-600 text-center">
                        <span className="font-medium text-blue-700">800 RT</span> ÷ <span className="font-medium text-blue-700">1,000 RT</span> = <span className="font-medium text-blue-700">80%</span>
                      </div>
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="w-full flex justify-start items-center gap-1 overflow-x-auto">
          <div className="text-[#0E7EE4] text-[18px] sm:text-[22px] font-semibold whitespace-nowrap">
            {runningCapacityPercentage !== null ? runningCapacityPercentage?.toFixed(1) : '-'}
          </div>
          <div className="text-[#788796] text-[13px] font-normal flex-shrink-0">
            %
          </div>
        </div>

        <div className="w-full h-1.5">
          <div className="w-full h-full bg-[#EDEFF9] rounded-sm overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] rounded-sm"
              style={{ width: `${runningCapacityPercentage}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

