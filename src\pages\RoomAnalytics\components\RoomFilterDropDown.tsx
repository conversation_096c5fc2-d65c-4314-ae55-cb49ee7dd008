import React from 'react';
import {DropdownMenu,DropdownMenuContent,DropdownMenuItem,DropdownMenuTrigger,} from '@/components/ui/dropdown-menu';

interface RoomFilterDropdownProps {
  selectedRoomType: string;
  onSelectRoomType: (type: string) => void;
}

const ROOM_TYPES = ['All', 'Standard', 'Deluxe', 'Suite'];

const RoomFilterDropdown: React.FC<RoomFilterDropdownProps> = ({
  selectedRoomType,
  onSelectRoomType,
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="btn-outline btn-sm px-3 rounded border text-sm">
          {selectedRoomType} ▼
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {ROOM_TYPES.map((type) => (
          <DropdownMenuItem
            key={type}
            onClick={() => onSelectRoomType(type)}
            className="text-sm"
          >
            {type} {selectedRoomType === type && '✓'}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default RoomFilterDropdown;
