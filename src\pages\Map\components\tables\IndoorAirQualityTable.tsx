import React from 'react';
import { DataTable, TableColumn } from '@/components/ui/data-table';
import { useRealtime } from '@/contexts/RealtimeContext';

interface IndoorAirQualityTableProps {
  className?: string;
  floors: Array<{
    id: number;
    displayName: string;
    deviceId: string;
  }>;
  datapoints?: {
    temperature: string;
    humidity: string;
    co2: string;
  };
}

export const IndoorAirQualityTable: React.FC<IndoorAirQualityTableProps> = ({
  className,
  floors,
  datapoints = {
    temperature: 'temperature',
    humidity: 'humidity',
    co2: 'co2'
  }
}) => {
  const { getValue } = useRealtime();

  const columns: TableColumn[] = [
    { key: 'floor', header: 'Floor' },
    { key: 'temperature', header: 'Temperature, °C' },
    { key: 'humidity', header: 'Humidity, %' },
    { 
      key: 'co2', 
      header: (
        <div className="flex items-baseline">
          <span>CO</span>
          <sub>2</sub>
          <span>, ppm</span>
        </div>
      ) 
    },
  ];

  const data = floors.map(floor => ({
    floor: floor.displayName,
    temperature: getValue(floor.deviceId, datapoints.temperature)?.toFixed(1) ?? '-',
    humidity: getValue(floor.deviceId, datapoints.humidity)?.toFixed(0) ?? '-',
    co2: getValue(floor.deviceId, datapoints.co2)?.toFixed(0) ?? '-',
  }));

  return (
    <div className="flex flex-col gap-2">
      <div className="text-[#212529] text-[13px] font-medium">
        Indoor Air Quality
      </div>
      <DataTable
        columns={columns}
        data={data}
        className={className}
      />
    </div>
  );
}; 