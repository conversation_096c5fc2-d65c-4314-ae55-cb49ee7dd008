import React, { useState } from 'react';
import { Settings, Save } from 'lucide-react';

interface SettingItem {
  name: string;
  label: string;
  description: string;
  value: string;
  type: 'text' | 'number' | 'select';
  options?: Array<{ value: string; label: string }>;
}

const SettingsTab: React.FC = () => {
  const [settings, setSettings] = useState<SettingItem[]>([
    {
      name: 'model_criteria_r2',
      label: 'R² Threshold',
      description: 'Minimum R² value for model validation',
      value: '0.75',
      type: 'number'
    },
    {
      name: 'model_criteria_cvrmse',
      label: 'CV(RMSE) Threshold',
      description: 'Maximum CV(RMSE) value for model validation (in %)',
      value: '20',
      type: 'number'
    },
    {
      name: 'model_criteria_nmbe',
      label: 'NMBE Threshold',
      description: 'Maximum NMBE value for model validation (in %)',
      value: '5',
      type: 'number'
    },
    {
      name: 'date_format',
      label: 'Date Format',
      description: 'Format of dates in input data',
      value: 'YYYY-MM-DD',
      type: 'select',
      options: [
        { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
        { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
        { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' }
      ]
    },
    {
      name: 'confidence_level',
      label: 'Confidence Level',
      description: 'Statistical confidence level for uncertainty calculations',
      value: '95',
      type: 'number'
    }
  ]);

  const handleSettingChange = (index: number, value: string) => {
    const newSettings = [...settings];
    newSettings[index].value = value;
    setSettings(newSettings);
  };

  const handleSaveSettings = () => {
    // In a real app, this would save to backend or localStorage
    alert('Settings saved successfully!');
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center gap-2 mb-6">
        <Settings className="h-6 w-6 text-blue-600" />
        <h2 className="text-xl font-semibold">Dashboard Settings</h2>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="space-y-6">
          {settings.map((setting, index) => (
            <div key={setting.name} className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label 
                  htmlFor={setting.name}
                  className="block text-sm font-medium text-gray-700"
                >
                  {setting.label}
                </label>
                <p className="text-sm text-gray-500">{setting.description}</p>
              </div>
              <div className="md:col-span-2">
                {setting.type === 'select' ? (
                  <select
                    id={setting.name}
                    value={setting.value}
                    onChange={(e) => handleSettingChange(index, e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  >
                    {setting.options?.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : (
                  <input
                    type={setting.type}
                    id={setting.name}
                    value={setting.value}
                    onChange={(e) => handleSettingChange(index, e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 flex justify-end">
          <button
            type="button"
            onClick={handleSaveSettings}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Settings
          </button>
        </div>
      </div>

      <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">Data Management</h3>
        
        <div className="space-y-4">
          <div>
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Clear All Data
            </button>
            <p className="mt-1 text-sm text-gray-500">
              Removes all imported data and analysis results
            </p>
          </div>
          
          <div>
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Export Configuration
            </button>
            <p className="mt-1 text-sm text-gray-500">
              Exports the current dashboard configuration as JSON
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsTab;