import React, { useState } from 'react';
import { Info } from 'lucide-react';
import { Button } from '@/components/ui/button';

const UncertaintyDescription: React.FC = () => {
  const [showDescription, setShowDescription] = useState(false);

  return (
    <div>
      <Button
        variant="ghost"
        size="sm"
        className="flex items-center text-blue-600 hover:text-[#065BA9] hover:bg-blue-50 mt-3 px-2 py-1 h-7"
        onClick={() => setShowDescription(!showDescription)}
      >
        <Info className="h-3 w-3 mr-1" />
        <span className="text-xs">About uncertainty calculation</span>
      </Button>

      {showDescription && (
        <div className="mt-2 p-3 bg-blue-50 border border-blue-100 rounded-md">
          <h4 className="text-xs font-medium mb-1.5">Understanding Uncertainty in Energy Savings</h4>
          
          <div className="text-xs space-y-2 text-gray-700">
            <p>
              <strong>Baseline Model Uncertainty (Sm)</strong>: Calculated as Model Standard Error 
              divided by the mean daily consumption (coefficient of variation). For example, with a 
              Standard Error of 1050.8 kWh and mean daily consumption of ~22,000 kWh, the model 
              uncertainty is approximately 4.7%.
            </p>
            
            <p>
              <strong>Meter Uncertainty (Smet)</strong>: The measurement uncertainty of your 
              metering equipment, representing the accuracy of energy measurements. Typically 
              1-2% for most commercial meters.
            </p>
            
            <p>
              <strong>Combined Uncertainty</strong>: Following error propagation principles, calculated 
              as SE = √(Sm² + Smet²). With a 4.7% baseline model uncertainty and 1.25% meter uncertainty, 
              the combined uncertainty is approximately 4.9%.
            </p>
            
            <p>
              <strong>IPMVP Method</strong>: This follows Section 8.3 of the International Performance 
              Measurement and Verification Protocol. The uncertainty of the baseline model is represented 
              by the Standard Error of the regression divided by the mean of the dependent variable.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default UncertaintyDescription; 