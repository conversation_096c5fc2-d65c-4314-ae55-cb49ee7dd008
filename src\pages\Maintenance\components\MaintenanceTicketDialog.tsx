import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { format } from 'date-fns';
import { createMaintenanceTicket, resolveMaintenanceTicket } from '@/services/maintenanceService';
import { useToast } from '@/components/ui/use-toast';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

interface MaintenanceTicketDialogProps {
  open: boolean;
  onClose: () => void;
  deviceId: string;
  isUnderMaintenance: boolean;
  maintenanceInfo: {
    status: 'under_maintenance' | 'completed';
    ticket_started_at: string;
    ticket_closed_at: string | null;
    description: string;
    ticket_started_by: string;
    ticket_closed_by: string | null;
    notes?: string;
  } | null;
}

const MaintenanceTicketDialog: React.FC<MaintenanceTicketDialogProps> = ({
  open,
  onClose,
  deviceId,
  isUnderMaintenance,
  maintenanceInfo,
}) => {
  const [formData, setFormData] = useState({
    description: '',
    ticket_started_by: '',
  });
  const [resolveData, setResolveData] = useState({
    ticket_closed_by: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleResolveInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setResolveData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      if (isUnderMaintenance) {
        // Resolve maintenance
        await resolveMaintenanceTicket(deviceId, resolveData.ticket_closed_by);
        toast({
          title: "Maintenance Resolved",
          description: "The equipment has been marked as operational.",
          variant: "default",
        });
      } else {
        // Create maintenance
        await createMaintenanceTicket(
          deviceId,
          formData.description,
          formData.ticket_started_by
        );
        toast({
          title: "Maintenance Started",
          description: "The equipment has been placed under maintenance.",
          variant: "default",
        });
      }
      onClose();
    } catch (error) {
      console.error('Error handling maintenance:', error);
      toast({
        title: "Error",
        description: "Failed to update maintenance status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isUnderMaintenance ? (
              <>
                <CheckCircle2 className="h-5 w-5 text-success" />
                <span>Resolve Maintenance</span>
              </>
            ) : (
              <>
                <AlertCircle className="h-5 w-5 text-warning" />
                <span>Schedule Maintenance</span>
              </>
            )}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="mt-2">
          {isUnderMaintenance ? (
            // Resolve maintenance form
            <div className="space-y-4">
              {maintenanceInfo && (
                <div className="bg-warning/10 p-3 rounded-md text-sm space-y-1.5">
                  <p className="font-medium text-md text-warning mb-2">Currently Under Maintenance</p>
                  <p><span className="font-medium">Started:</span> {format(new Date(maintenanceInfo.ticket_started_at), 'PPp')}</p>
                  <p><span className="font-medium">By:</span> {maintenanceInfo.ticket_started_by}</p>
                  <p><span className="font-medium">Issue:</span> {maintenanceInfo.description}</p>
                  {maintenanceInfo.notes && <p><span className="font-medium">Notes:</span> {maintenanceInfo.notes}</p>}
                </div>
              )}
              
              <div>
                <Label htmlFor="ticket_closed_by" className="text-sm">Technician Name (Resolving)</Label>
                <Input
                  id="ticket_closed_by"
                  name="ticket_closed_by"
                  value={resolveData.ticket_closed_by}
                  onChange={handleResolveInputChange}
                  placeholder="Enter your name"
                  className="mt-1.5"
                  required
                />
              </div>
            </div>
          ) : (
            // Create maintenance form
            <div className="space-y-4">
              <div>
                <Label htmlFor="description" className="text-sm">Issue Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the issue"
                  className="mt-1.5 resize-none"
                  rows={3}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="ticket_started_by" className="text-sm">Reporter Name</Label>
                <Input
                  id="ticket_started_by"
                  name="ticket_started_by"
                  value={formData.ticket_started_by}
                  onChange={handleInputChange}
                  placeholder="Enter your name"
                  className="mt-1.5"
                  required
                />
              </div>
            </div>
          )}
          
          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose} size="sm">
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              size="sm"
              variant="default"
            >
              {isSubmitting ? 'Processing...' : isUnderMaintenance ? 'Resolve Maintenance' : 'Start Maintenance'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default MaintenanceTicketDialog;
