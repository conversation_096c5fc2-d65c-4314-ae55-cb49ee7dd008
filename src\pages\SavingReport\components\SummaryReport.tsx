import React, { useState, useEffect } from 'react';
import { SummaryStats } from '../types';
import { AlertTriangle } from 'lucide-react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { Button } from '@/components/ui/button';
import UncertaintyCalculator from './UncertaintyCalculator';
import UncertaintyDescription from './UncertaintyDescription';

interface SummaryReportProps {
  stats: SummaryStats;
}

const SummaryReport: React.FC<SummaryReportProps> = ({ stats }) => {
  const { reportingRange, regressionResult, variableSelection, filteredData, weekdayFilterEnabled } = useSavingDashboard();
  const [showUncertainty, setShowUncertainty] = useState(false);
  const [meterUncertainty, setMeterUncertainty] = useState(0.0125); // 1.25% default
  const [confidenceLevel, setConfidenceLevel] = useState(1.96); // 95% confidence level
  const [viewMode, setViewMode] = useState<'sum'|'average'>('sum'); // Add viewMode state
  
  // Calculate baseline model uncertainty from Standard Error / Mean Daily Consumption
  const modelSE = regressionResult?.statistics?.rse || 0;
  const observations = regressionResult?.statistics?.observations || 0;
  // Get the mean daily consumption from baseline data, not reporting data
  const meanDailyConsumption = filteredData.length > 0 
    ? filteredData.reduce((sum, row) => sum + row[variableSelection.dependent], 0) / filteredData.length 
    : 0;
  const baselineUncertainty = meanDailyConsumption ? (modelSE / meanDailyConsumption) : 0.0469;

  // Get the unit label based on the dependent variable
  const getUnitLabel = () => {
    if (variableSelection.dependent === 'plant_energy' || variableSelection.dependent === 'total_energy') {
      return 'kWh';
    } else if (variableSelection.dependent === 'plant_efficiency' || variableSelection.dependent === 'total_efficiency') {
      return 'kW/Ton';
    } else if (variableSelection.dependent === 'temperature') {
      return '°C';
    } else if (variableSelection.dependent === 'humidity') {
      return '%';
    } else if (variableSelection.dependent === 'cooling_load' || variableSelection.dependent === 'airside_energy') {
      return 'kWh';
    } else if (variableSelection.dependent === 'cdd') {
      return 'degree days';
    } else {
      return 'units';
    }
  };

  const unitLabel = getUnitLabel();

  const toggleUncertainty = () => {
    setShowUncertainty(!showUncertainty);
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };

  const handleUncertaintyChange = (meterUncertainty: number, confidenceLevel: number) => {
    setMeterUncertainty(meterUncertainty);
    setConfidenceLevel(confidenceLevel);
  };

  // Function to get monthly value based on view mode
  const getMonthlyValue = (month: any, field: string) => {
    if (viewMode === 'average' && month.days > 0) {
      return month[field] / month.days;
    }
    return month[field];
  };

  // Get the summary values based on view mode
  const getSummaryValue = (value: number) => {
    if (viewMode === 'average' && stats.reportingDays > 0) {
      return value / stats.reportingDays;
    }
    return value;
  };

  return (
    <div className="text-sm">
      <div className="grid grid-cols-2 gap-x-6 mb-4">
        <div className="col-span-2 sm:col-span-1 flex flex-col sm:flex-row gap-4 items-start justify-between w-full">
          <div className="flex flex-row gap-4">
            <div className="min-w-[180px]">
              <span className="text-xs text-[#64748B]">Number of Reporting Days</span>
              <p className="text-sm font-medium">
                {stats.reportingDays} days{weekdayFilterEnabled ? ' (Weekdays Only)' : ''}
              </p>
            </div>
            
            <div className="min-w-[240px]">
              <span className="text-xs text-[#64748B]">Reporting Period</span>
              <p className="text-sm font-medium">
                {new Date(reportingRange.start || stats.reportingPeriod.start).toLocaleDateString('en-US', {year: 'numeric', month: '2-digit', day: '2-digit'}).replace(/(\d+)\/(\d+)\/(\d+)/, '$3/$1/$2')} - {new Date(reportingRange.end || stats.reportingPeriod.end).toLocaleDateString('en-US', {year: 'numeric', month: '2-digit', day: '2-digit'}).replace(/(\d+)\/(\d+)\/(\d+)/, '$3/$1/$2')}
              </p>
            </div>
          </div>
          
          <div className="flex space-x-1 items-start">
            <button 
              className={`text-xs px-2 py-0.5 h-6 rounded-sm ${viewMode === 'sum' ? 'bg-[#E0F2FE] text-[#0369A1]' : 'bg-card hover:bg-card/80'}`}
              onClick={() => setViewMode('sum')}
            >
              Sum
            </button>
            <button 
              className={`text-xs px-2 py-0.5 h-6 rounded-sm ${viewMode === 'average' ? 'bg-[#E0F2FE] text-[#0369A1]' : 'bg-card hover:bg-card/80'}`}
              onClick={() => setViewMode('average')}
            >
              Average
            </button>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="p-3 bg-[#F8FAFC] rounded-md">
          <span className="text-xs text-[#64748B]">{viewMode === 'average' ? 'Average' : 'Total'} Baseline Consumption</span>
          <p className="text-sm font-medium">{formatNumber(getSummaryValue(stats.totalBaselineConsumption))} {unitLabel}{viewMode === 'average' ? '/day' : ''}</p>
        </div>
        
        <div className="p-3 bg-[#F8FAFC] rounded-md">
          <span className="text-xs text-[#64748B]">{viewMode === 'average' ? 'Average' : 'Total'} Actual Consumption</span>
          <p className="text-sm font-medium">{formatNumber(getSummaryValue(stats.totalActualConsumption))} {unitLabel}{viewMode === 'average' ? '/day' : ''}</p>
        </div>
        
        <div className="p-3 bg-[#F8FAFC] rounded-md">
          <span className="text-xs text-[#64748B]">{viewMode === 'average' ? 'Average' : 'Total'} Saving</span>
          <p className={`text-sm font-medium ${stats.totalSaving >= 0 ? 'text-[#16A34A]' : 'text-[#DC2626]'}`}>
            {formatNumber(getSummaryValue(stats.totalSaving))} {unitLabel}{viewMode === 'average' ? '/day' : ''}
          </p>
        </div>
        
        <div className="p-3 bg-[#F8FAFC] rounded-md">
          <span className="text-xs text-[#64748B]">Saving Percentage</span>
          <p className={`text-sm font-medium ${stats.savingPercentage >= 0 ? 'text-[#16A34A]' : 'text-[#DC2626]'}`}>
            {stats.savingPercentage.toFixed(2)}%
          </p>
        </div>
      </div>

      {showUncertainty && (
        <div className="mt-3 mb-6">
          <UncertaintyCalculator 
            baselineUncertainty={baselineUncertainty}
            onUncertaintyChange={handleUncertaintyChange}
            regressionResult={regressionResult}
            totalBaselineConsumption={stats.totalBaselineConsumption}
            reportingDays={stats.reportingDays}
          />
        </div>
      )}
      
      <div>
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-sm font-medium">Monthly Breakdown</h3>
          <Button
            onClick={toggleUncertainty}
            variant="outline"
            size="sm"
            className="text-[#1E40AF] h-8"
          >
            Uncertainty
          </Button>
        </div>
        
        <div className="overflow-x-auto border rounded-md">
          <table className="min-w-full divide-y divide-[#E2E8F0]">
            <thead className="bg-[#F8FAFC]">
              <tr>
                <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-[#64748B] uppercase tracking-wider">
                  Month
                </th>
                <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-[#64748B] uppercase tracking-wider">
                  Days
                </th>
                <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-[#64748B] uppercase tracking-wider" title="Calculated using Model Equation from section 3">
                  Baseline ({unitLabel}{viewMode === 'average' ? '/day' : ''})
                </th>
                <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-[#64748B] uppercase tracking-wider">
                  Actual ({unitLabel}{viewMode === 'average' ? '/day' : ''})
                </th>
                <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-[#64748B] uppercase tracking-wider">
                  Savings ({unitLabel}{viewMode === 'average' ? '/day' : ''})
                </th>
                {showUncertainty && (
                  <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-[#64748B] uppercase tracking-wider">
                    Uncertainty ({unitLabel}{viewMode === 'average' ? '/day' : ''})
                  </th>
                )}
                <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-[#64748B] uppercase tracking-wider">
                  Savings (%)
                </th>
                {showUncertainty && (
                  <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-[#64748B] uppercase tracking-wider">
                    Uncertainty (%)
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-[#E2E8F0]">
              {stats.monthlyBreakdown.map((month, index) => {
                const calculatedBaselineUncertainty = meanDailyConsumption ? (modelSE / meanDailyConsumption) : baselineUncertainty;
                const combinedUncertainty = Math.sqrt(Math.pow(calculatedBaselineUncertainty, 2) + Math.pow(meterUncertainty, 2));
                const avgDailyBaseline = month.baselineKwh / month.days;
                const errorKwhPerDay = avgDailyBaseline * combinedUncertainty;
                const dailyUncertainty = errorKwhPerDay * confidenceLevel;
                const monthlyUncertaintyKwh = dailyUncertainty * Math.sqrt(month.days);
                const monthlyUncertaintyPercent = (monthlyUncertaintyKwh / month.baselineKwh) * 100;
                
                // Get values according to current view mode
                const baselineDisplay = getMonthlyValue(month, 'baselineKwh');
                const actualDisplay = getMonthlyValue(month, 'actualKwh');
                const savingsDisplay = getMonthlyValue(month, 'savingsKwh');
                const uncertaintyDisplay = viewMode === 'average' && month.days > 0 
                  ? monthlyUncertaintyKwh / month.days 
                  : monthlyUncertaintyKwh;
                
                return (
                  <tr key={index} className="hover:bg-[#F8FAFC]">
                    <td className="px-3 py-2 whitespace-nowrap text-xs font-medium text-[#0F172A]">
                      {month.month}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-[#64748B]">
                      {month.days}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-[#64748B]">
                      {formatNumber(baselineDisplay)}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-[#64748B]">
                      {formatNumber(actualDisplay)}
                    </td>
                    <td className={`px-3 py-2 whitespace-nowrap text-xs font-medium ${savingsDisplay >= 0 ? 'text-[#16A34A]' : 'text-[#DC2626]'}`}>
                      {formatNumber(savingsDisplay)}
                    </td>
                    {showUncertainty && (
                      <td className="px-3 py-2 whitespace-nowrap text-xs text-[#64748B]">
                        ±{formatNumber(uncertaintyDisplay)}
                      </td>
                    )}
                    <td className={`px-3 py-2 whitespace-nowrap text-xs font-medium ${month.savingsPercent >= 0 ? 'text-[#16A34A]' : 'text-[#DC2626]'}`}>
                      {month.savingsPercent >= 0 ? '+' : ''}{month.savingsPercent.toFixed(2)}%
                    </td>
                    {showUncertainty && (
                      <td className="px-3 py-2 whitespace-nowrap text-xs text-[#64748B]">
                        ±{monthlyUncertaintyPercent.toFixed(2)}%
                      </td>
                    )}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        
        {showUncertainty && (
          <div className="mt-4 p-3 bg-[#FEFCE8] border border-[#FEF9C3] rounded-md flex items-start space-x-3">
            <AlertTriangle className="h-4 w-4 text-[#CA8A04] flex-shrink-0 mt-0.5" />
            <div className="text-xs text-[#854D0E]">
              <p className="font-medium">Uncertainty Information</p>
              <p>Uncertainty values represent the statistical margin of error at your selected confidence level, calculated using Model SE from the regression analysis.</p>
            </div>
          </div>
        )}
      </div>
      
      {showUncertainty && <UncertaintyDescription />}
    </div>
  );
};

export default SummaryReport;