import React, { useState, useCallback, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { DataTable, TableColumn } from '@/components/ui/data-table';
import { useRealtime } from '@/contexts/RealtimeContext';
import { cn } from '@/lib/utils';
import { Switch } from '@/components/ui/switch';

const STATUS_COLORS = {
  on: 'bg-[#14B8B4]',
  off: 'bg-[#788796]',
  alarm: 'bg-[#EF4337]',
  maintenance: 'bg-[#FEBE54]',
} as const;

type MachineStatusType = keyof typeof STATUS_COLORS;

interface RiserTableProps {
  className?: string;
  risers: Array<{
    id: string;
    displayName: string;
    deviceId: string;
    controlDeviceId: string;
  }>;
  datapoints?: {
    flow: string;
    pressure: string;
    temperature: string;
  };
}

interface PopupMenuProps {
  deviceId: string;
  riserName: string;
  onClose: () => void;
}

const AutoManualSwitch: React.FC<{
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
}> = ({ checked, onCheckedChange, disabled }) => {
  return (
    <div 
      className="relative inline-flex items-center" 
      onClick={(e) => e.stopPropagation()}
    >
      <Switch
        checked={checked}
        onCheckedChange={onCheckedChange}
        disabled={disabled}
        size="sm"
        className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-[#0E7EE4] data-[state=checked]:to-[#14B8B4]"
      />
      <div className="absolute inset-0 flex justify-between items-center px-[6px] pointer-events-none">
        <span className="text-[8px] font-medium text-white">A</span>
        <span className="text-[8px] font-medium text-white">M</span>
      </div>
    </div>
  );
};

const PopupMenu: React.FC<PopupMenuProps> = ({ deviceId, riserName, onClose }) => {
  const { getValue, setValue } = useRealtime();
  const statusRead = getValue(deviceId, 'status_read');
  const alarm = getValue(deviceId, 'alarm');
  const isManualControl = getValue(deviceId, 'is_manual') || false;

  const handleManualControl = async (checked: boolean) => {
    await setValue(deviceId, 'is_manual', checked);
  };

  const handleStartStop = async (action: 'start' | 'stop') => {
    await setValue(deviceId, 'status_write', action === 'start');
    onClose();
  };

  return (
    <div 
      className="absolute bg-white shadow-[4px_8px_20px_rgba(112,144,176,0.24)] rounded-[7px] p-[6px] w-[120px] z-50"
      onClick={(e) => e.stopPropagation()}
    >
      <div className="flex flex-col gap-2">
        {/* Status Section */}
        <div className="flex justify-between items-start">
          <div className="flex flex-col">
            <div className="text-[#5E5E5E] text-[8px] font-normal">{riserName}</div>
            <div className="flex items-center gap-2">
              <div className="w-[6px] h-[6px] rounded-full" 
                style={{ 
                  backgroundColor: alarm ? STATUS_COLORS.alarm : 
                                 statusRead ? STATUS_COLORS.on : 
                                 STATUS_COLORS.off 
                }} 
              />
              <div className="text-[#2E8285] text-[8px] font-normal">
                {alarm ? 'Alarm' : statusRead ? 'Running' : 'Stopped'}
              </div>
            </div>
          </div>
          <AutoManualSwitch
            checked={isManualControl}
            onCheckedChange={handleManualControl}
            disabled={alarm}
          />
        </div>

        <div className="w-full h-[1px] bg-[#EDEFF9]" />

        {/* Start/Stop Buttons */}
        <div className="flex gap-1 mt-1">
          <button
            className={cn(
              "flex-1 h-6 rounded-md text-[10px] font-medium transition-colors text-center",
              "bg-success text-white hover:bg-success/90"
            )}
            onClick={() => handleStartStop('start')}
          >
            Start
          </button>
          <button
            className={cn(
              "flex-1 h-6 rounded-md text-[10px] font-medium transition-colors text-center", 
              "bg-destructive text-destructive-foreground hover:bg-destructive/90"
            )}
            onClick={() => handleStartStop('stop')}
          >
            Stop
          </button>
        </div>
      </div>
    </div>
  );
};

const RiserIndicator: React.FC<{
  deviceId: string;
  riserName: string;
  onClick: (e: React.MouseEvent) => void;
}> = ({ deviceId, riserName, onClick }) => {
  const { getValue } = useRealtime();
  const statusRead = getValue(deviceId, 'status_read');
  const alarm = getValue(deviceId, 'alarm');
  const status: MachineStatusType = alarm ? 'alarm' : statusRead ? 'on' : 'off';

  return (
    <div className="flex items-center gap-2">
      <div 
        className={cn("riser-indicator w-[8px] h-[8px] rounded-full cursor-pointer hover:ring-2 hover:ring-ring hover:ring-offset-1", STATUS_COLORS[status])}
        onClick={onClick}
      />
      <div className="text-[#212529] text-[11px] font-normal">{riserName}</div>
    </div>
  );
};

export const RiserTable: React.FC<RiserTableProps> = ({
  className,
  risers,
  datapoints = {
    flow: 'flow',
    pressure: 'pressure',
    temperature: 'temperature'
  },
}) => {
  const { getValue } = useRealtime();
  const [selectedRiser, setSelectedRiser] = useState<string | null>(null);
  const [popupPosition, setPopupPosition] = useState<{ x: number; y: number } | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      // Check if click is outside both the popup and the indicator
      if (!target.closest('.popup-menu') && !target.closest('.riser-indicator')) {
        handleClosePopup();
      }
    };

    if (selectedRiser) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [selectedRiser]);

  const handleIndicatorClick = useCallback((e: React.MouseEvent, riser: string) => {
    e.stopPropagation();
    const target = e.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    setPopupPosition({
      x: rect.right + 8,
      y: rect.top - 4,
    });
    setSelectedRiser(riser);
  }, []);

  const handleClosePopup = useCallback(() => {
    setSelectedRiser(null);
    setPopupPosition(null);
  }, []);

  const columns: TableColumn[] = [
    { 
      key: 'riser',
      header: 'Riser',
    },
    { key: 'flow', header: 'Flow, GPM' },
    { key: 'pressure', header: 'Pressure, PSI' },
    { key: 'temperature', header: 'Temperature, °F' },
  ];

  const data = risers.map(riser => ({
    riser: (
      <RiserIndicator
        deviceId={riser.controlDeviceId}
        riserName={riser.displayName}
        onClick={(e) => handleIndicatorClick(e, riser.id)}
      />
    ),
    flow: getValue(riser.deviceId, datapoints.flow)?.toFixed(1) ?? '-',
    pressure: getValue(riser.deviceId, datapoints.pressure)?.toFixed(1) ?? '-',
    temperature: getValue(riser.deviceId, datapoints.temperature)?.toFixed(1) ?? '-',
  }));

  return (
    <div className="flex flex-col gap-2">
      <div className="text-[#212529] text-[13px] font-medium">
        Riser Status
      </div>
      <DataTable
        columns={columns}
        data={data}
        className={className}
      />
      {selectedRiser && popupPosition && createPortal(
        <div 
          className="popup-menu"
          style={{ 
            position: 'fixed',
            left: `${popupPosition.x}px`,
            top: `${popupPosition.y}px`,
          }}
        >
          <PopupMenu
            deviceId={risers.find(r => r.id === selectedRiser)?.controlDeviceId || ''}
            riserName={risers.find(r => r.id === selectedRiser)?.displayName || ''}
            onClose={handleClosePopup}
          />
        </div>,
        document.body
      )}
    </div>
  );
}; 