import React from 'react';
import { useWeatherData, getNextHoursForecast, getCurrentTemperature } from '../services/weatherService';
import { getWeatherIcon } from '../utils/weatherIcons';
import { Spinner } from './ui/spinner';
import { WeatherForecast } from '../types/weather';

const Weather = () => {
  const { weatherData, loading, error } = useWeatherData();

  if (loading) {
    return (
      <div className="flex items-center gap-3 mr-4">
        <Spinner size="sm" />
        <span className="text-muted-foreground text-xs">Loading weather...</span>
      </div>
    );
  }

  if (error || !weatherData) {
    return (
      <div className="flex items-center gap-3 mr-4 text-xs text-muted-foreground">
        Weather data unavailable
      </div>
    );
  }

  const currentTemp = getCurrentTemperature(weatherData);
  const nextHours = getNextHoursForecast(weatherData, 4);
  const currentTime = new Date(weatherData.timestamp);
  
  // Get location name from coordinates
  const locationName = "Bangkok"; // Hardcoded for now, could be derived from coordinates

  return (
    <div className="flex items-center gap-3 mr-4">
      <div className="flex items-center gap-2">
        <div className="relative">
          <div className="text-primary text-xl font-semibold">
            {currentTemp ? currentTemp.toFixed(1) : '--'}°
          </div>
        </div>
        <div className="flex flex-col">
          <div className="text-foreground text-xs font-semibold">
            {locationName}
          </div>
          <div className="text-muted-foreground text-[8px]">
            Source: TMD
          </div>
        </div>
      </div>

      {nextHours.map((forecast, index) => (
        <div key={index} className="flex flex-col items-center gap-0.2">
          <div className="w-[30px] h-5 flex justify-center items-center">
            <img 
              src={getWeatherIcon(forecast.weather_condition)} 
              alt={forecast.weather_condition_text}
              className="w-5 h-5"
            />
          </div>
          <div className="text-muted-foreground text-[8px] leading-tight">
            {forecast.drybulb_temperature.toFixed(1)}°
          </div>
          <div className="text-muted-foreground text-[8px] leading-tight">
            {new Date(forecast.forecast_time).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default Weather;
