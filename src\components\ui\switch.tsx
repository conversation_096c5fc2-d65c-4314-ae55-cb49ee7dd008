import * as React from "react";
import * as SwitchPrimitives from "@radix-ui/react-switch";
import { cn } from "@/lib/utils";

const switchVariants = {
  size: {
    default: {
      root: "h-6 w-11",
      thumb: "h-5 w-5 translate-x-0.5 data-[state=checked]:translate-x-[22px]"
    },
    sm: {
      root: "h-4 w-[34px]",
      thumb: "h-3 w-3 translate-x-[2px] data-[state=checked]:translate-x-[19px]"
    }
  }
} as const;

type SwitchProps = React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> & {
  size?: keyof typeof switchVariants.size;
};

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  SwitchProps
>(({ className, size = "default", ...props }, ref) => {
  const variants = switchVariants.size[size as keyof typeof switchVariants.size];

  return (
    <SwitchPrimitives.Root
      className={cn(
        "peer inline-flex shrink-0 cursor-pointer items-center rounded-full border-none transition-colors",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary",
        "disabled:cursor-not-allowed disabled:opacity-50",
        "bg-muted",
        "data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-[#0E7EE4] data-[state=checked]:to-[#14B8B4]",
        variants.root,
        className
      )}
      {...props}
      ref={ref}
    >
      <SwitchPrimitives.Thumb
        className={cn(
          "pointer-events-none block rounded-full bg-white",
          "transition-transform duration-200 will-change-transform",
          variants.thumb
        )}
      />
    </SwitchPrimitives.Root>
  );
});
Switch.displayName = SwitchPrimitives.Root.displayName;

export { Switch }; 