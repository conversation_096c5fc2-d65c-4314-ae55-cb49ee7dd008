import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getDashAppUrl } from '@/utils/dash';

const HistoricalData: React.FC = () => {
  const { site } = useAuth();
  const dashboardAppUrl = getDashAppUrl('historical-data', site?.id);
  
  return (
    <div className="w-full h-[calc(100vh-71px)] overflow-hidden bg-background">
      <iframe 
        src={dashboardAppUrl}
        className="w-full h-full border-none"
        title="Alto Dashboardx"
        sandbox="allow-scripts allow-forms allow-same-origin allow-downloads"
        allow="download *"
      />
    </div>
  );
};

export default HistoricalData;