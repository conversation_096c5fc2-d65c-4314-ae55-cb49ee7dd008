## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- <PERSON>er and <PERSON>er Compose
- Git

### Development Environment Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/AltoTechTH/alto-cero-interface.git
   cd alto-cero-interface
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```
   The application will be available at `http://localhost:3000`

### Production Deployment

1. **Build and run with Docker**
   ```bash
   docker-compose up --build -d
   ```
   This will:
   - Build the production-ready container
   - Start the application on port 3000
   - Run in detached mode

2. **View logs**
   ```bash
   docker-compose logs -f
   ```

3. **Stop the application**
   ```bash
   docker-compose down
   ```

## 🛠 Project Structure

```
alto-cero-interface/
├── src/
│   ├── assets/          # Static assets (images, icons)
│   ├── components/      # Reusable UI components
│   ├── contexts/        # React context providers including ActionContext
│   ├── lib/             # Utility functions and shared logic
│   ├── pages/           # Page components and routes
│   └── styles/          # Global styles and themes
├── public/              # Public static files
├── .env.example         # Example environment variables
├── docker-compose.yml   # Docker compose configuration
├── package.json         # Project dependencies and scripts
└── vite.config.ts       # Vite configuration
```

## 🔧 Configuration

### Environment Variables

- `VITE_API_URL`: Backend API URL
- `VITE_WS_URL`: WebSocket server URL
- `VITE_SUPABASE_URL`: Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Supabase anonymous key

### Build Configuration

The project uses Vite for building and development. Key configurations:

- TypeScript support
- SVG imports as React components
- Path aliases (@/ points to src/)
- Hot Module Replacement (HMR)

## 📚 Technology Stack

- **Frontend Framework**: React 18
- **Build Tool**: Vite
- **UI Components**: shadcn/ui
- **Styling**: Tailwind CSS
- **State Management**: React Context + Hooks
- **Database & Realtime**: Supabase
- **Type Safety**: TypeScript
- **Containerization**: Docker

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [shadcn/ui](https://ui.shadcn.com/) for the beautiful UI components
- [Vite](https://vitejs.dev/) for the excellent build tool
- [Supabase](https://supabase.com/) for real-time database capabilities
- [Docker](https://www.docker.com/) for containerization support
