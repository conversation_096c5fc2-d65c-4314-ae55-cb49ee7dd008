import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

interface DataSelectorProps {
  devices: { id: string; name: string }[];
  selectedDeviceId: string;
  onDeviceChange: (deviceId: string) => void;
  
  datapoints: { id: string; name: string }[];
  selectedDatapoints: string[];
  onDatapointsChange: (datapoints: string[]) => void;
  
  secondaryYAxisDatapoints: string[];
  onSecondaryYAxisChange: (datapoints: string[]) => void;
  
  compareMode: boolean;
  onCompareModeChange: (enabled: boolean) => void;
  
  chartType: 'line' | 'scatter';
  onChartType<PERSON>hange: (type: 'line' | 'scatter') => void;
  
  dateRange: [Date, Date];
  onDateRangeChange: (range: [Date, Date]) => void;
  
  // Only for scatter plot
  xAxisDatapoint: string;
  onXAxisDatapointChange: (datapoint: string) => void;
  
  yAxisDatapoint: string;
  onYAxisDatapointChange: (datapoint: string) => void;
}

const DataSelector: React.FC<DataSelectorProps> = ({
  devices,
  selectedDeviceId,
  onDeviceChange,
  datapoints,
  selectedDatapoints,
  onDatapointsChange,
  secondaryYAxisDatapoints,
  onSecondaryYAxisChange,
  compareMode,
  onCompareModeChange,
  chartType,
  onChartTypeChange,
  dateRange,
  onDateRangeChange,
  xAxisDatapoint,
  onXAxisDatapointChange,
  yAxisDatapoint,
  onYAxisDatapointChange
}) => {
  const handleDeviceChange = (value: string) => {
    onDeviceChange(value);
  };
  
  const handleDatapointChange = (datapointId: string) => {
    if (selectedDatapoints.includes(datapointId)) {
      onDatapointsChange(selectedDatapoints.filter(id => id !== datapointId));
    } else {
      onDatapointsChange([...selectedDatapoints, datapointId]);
    }
  };
  
  const handleSecondaryYAxisChange = (datapointId: string) => {
    if (!selectedDatapoints.includes(datapointId)) {
      return; // Can't add to secondary axis if not selected
    }
    
    if (secondaryYAxisDatapoints.includes(datapointId)) {
      onSecondaryYAxisChange(secondaryYAxisDatapoints.filter(id => id !== datapointId));
    } else {
      onSecondaryYAxisChange([...secondaryYAxisDatapoints, datapointId]);
    }
  };
  
  const handleDateChange = (index: 0 | 1, value: string) => {
    const newDate = new Date(value);
    const newRange: [Date, Date] = [...dateRange];
    newRange[index] = newDate;
    onDateRangeChange(newRange);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Data Selection</CardTitle>
        <CardDescription>Configure visualization parameters</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Chart Type Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Chart Type</Label>
            <RadioGroup
              defaultValue={chartType}
              onValueChange={(value: string) => onChartTypeChange(value as 'line' | 'scatter')}
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="line" id="chart-type-line" />
                <Label htmlFor="chart-type-line" className="text-sm">Line Chart</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="scatter" id="chart-type-scatter" />
                <Label htmlFor="chart-type-scatter" className="text-sm">Scatter Plot</Label>
              </div>
            </RadioGroup>
          </div>
          
          {/* Device Selection */}
          <div className="space-y-2">
            <Label htmlFor="device-select" className="text-sm font-medium">Device</Label>
            <Select
              value={selectedDeviceId}
              onValueChange={handleDeviceChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a device" />
              </SelectTrigger>
              <SelectContent>
                {devices.map(device => (
                  <SelectItem key={device.id} value={device.id}>
                    {device.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Date Range Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="start-date" className="text-sm font-medium">Start Date</Label>
            <Input
              id="start-date"
              type="datetime-local"
              value={dateRange[0].toISOString().slice(0, 16)}
              onChange={e => handleDateChange(0, e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="end-date" className="text-sm font-medium">End Date</Label>
            <Input
              id="end-date"
              type="datetime-local"
              value={dateRange[1].toISOString().slice(0, 16)}
              onChange={e => handleDateChange(1, e.target.value)}
            />
          </div>
        </div>
        
        {chartType === 'line' ? (
          <>
            {/* Compare Mode Toggle (Line Chart Only) */}
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="compare-mode"
                  checked={compareMode}
                  onCheckedChange={(checked: boolean | "indeterminate") => onCompareModeChange(checked === true)}
                />
                <Label htmlFor="compare-mode" className="text-sm">
                  Compare Mode (overlay by time of day)
                </Label>
              </div>
            </div>
            
            {/* Datapoint Selection (Line Chart) */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Datapoints</Label>
              <div className="max-h-40 overflow-y-auto border rounded-md p-2">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-1 text-muted-foreground">Select</th>
                      <th className="text-left p-1 text-muted-foreground">Datapoint</th>
                      <th className="text-left p-1 text-muted-foreground">Secondary Y-Axis</th>
                    </tr>
                  </thead>
                  <tbody>
                    {datapoints.map(datapoint => (
                      <tr key={datapoint.id} className="border-b last:border-b-0">
                        <td className="p-1">
                          <Checkbox
                            checked={selectedDatapoints.includes(datapoint.id)}
                            onCheckedChange={() => handleDatapointChange(datapoint.id)}
                          />
                        </td>
                        <td className="p-1 text-card-foreground">{datapoint.name}</td>
                        <td className="p-1">
                          <Checkbox
                            checked={secondaryYAxisDatapoints.includes(datapoint.id)}
                            disabled={!selectedDatapoints.includes(datapoint.id)}
                            onCheckedChange={() => handleSecondaryYAxisChange(datapoint.id)}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </>
        ) : (
          /* Scatter Plot Axis Selection */
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="x-axis-select" className="text-sm font-medium">X-Axis Datapoint</Label>
              <Select
                value={xAxisDatapoint}
                onValueChange={onXAxisDatapointChange}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select X-Axis datapoint" />
                </SelectTrigger>
                <SelectContent>
                  {datapoints.map(datapoint => (
                    <SelectItem key={datapoint.id} value={datapoint.id}>
                      {datapoint.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="y-axis-select" className="text-sm font-medium">Y-Axis Datapoint</Label>
              <Select
                value={yAxisDatapoint}
                onValueChange={onYAxisDatapointChange}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Y-Axis datapoint" />
                </SelectTrigger>
                <SelectContent>
                  {datapoints.map(datapoint => (
                    <SelectItem key={datapoint.id} value={datapoint.id}>
                      {datapoint.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DataSelector;