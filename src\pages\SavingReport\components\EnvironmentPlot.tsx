import React, { useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { EChartsOption } from 'echarts';

interface EnvironmentPlotProps {
  period: 'daily' | 'monthly';
  isFullscreenComponent?: boolean;
}

const EnvironmentPlot: React.FC<EnvironmentPlotProps> = ({ period, isFullscreenComponent }) => {
  const { reportingData, reportingRange, variableSelection, weekdayFilterEnabled } = useSavingDashboard();

  // Filter data based on reporting range and weekday filter
  const filteredData = useMemo(() => {
    return reportingData.filter(row => {
      if (!reportingRange.start || !reportingRange.end) return true;
      
      const rowDate = new Date(row.date);
      const startDate = new Date(reportingRange.start);
      const endDate = new Date(reportingRange.end);
      
      // First check date range
      const inDateRange = rowDate >= startDate && rowDate <= endDate;
      
      // Then apply weekday filter if enabled
      if (weekdayFilterEnabled) {
        const dayOfWeek = rowDate.getDay();
        // 0 is Sunday, 6 is Saturday - so 1-5 are weekdays
        return inDateRange && dayOfWeek >= 1 && dayOfWeek <= 5;
      }
      
      return inDateRange;
    });
  }, [reportingData, reportingRange, weekdayFilterEnabled]);

  if (filteredData.length === 0) {
    return (
      <div className="p-3 text-center text-gray-500 text-sm">
        No reporting data available for the selected date range{weekdayFilterEnabled ? ' and weekday filter' : ''}.
      </div>
    );
  }

  // The variables we want to plot are the X variables (independent variables)
  const variables = variableSelection.independent;

  if (!variables || variables.length === 0) {
    return (
      <div className="p-3 text-center text-gray-500">
        Please select at least one variable to display environment plots.
      </div>
    );
  }

  // Color palette based on parameter type using WCAG compliant colors
  const getColorForVariable = (variable: string): string => {
    const lowerVar = variable.toLowerCase();
    if (lowerVar.includes('temp')) return '#FF8C1A';  // Mid Orange
    if (lowerVar.includes('hum')) return '#3399FF';  // Mid Blue
    if (lowerVar.includes('rh')) return '#3399FF';  // Mid Blue
    if (lowerVar.includes('solar') || lowerVar.includes('rad')) return '#FFB366';  // Light Orange
    if (lowerVar.includes('wind')) return '#66B3FF';  // Light Blue
    if (lowerVar.includes('press')) return '#FF66B2';  // FF66B2
    if (lowerVar.includes('cdd')) return '#9966FF';  // Pink
    if (lowerVar.includes('load')) return '#FFB266';  // Light Orange
    if (lowerVar.includes('energy')) return '#66FFB2';  // Light Green
    if (lowerVar.includes('efficiency')) return '#B266FF';  // Light Purple
    if (lowerVar.includes('weekend')) return '#FF6666';  // Light Red
    if (lowerVar.includes('is')) return '#808080';  // Gray
    return '#808080';  // Mid Green for other parameters
  };

  // Create a bar plot for each variable
  const createBarPlotOptions = (variable: string): EChartsOption => {
    // Sort data by date
    const sortedData = [...filteredData].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    let data;
    if (period === 'daily') {
      // Use daily data directly
      data = {
        dates: sortedData.map(row => row.date.substring(0, 10)),
        values: sortedData.map(row => row[variable] || 0)
      };
    } else {
      // Aggregate by month
      const monthlyData: { [key: string]: { sum: number; count: number } } = {};
      
      sortedData.forEach(row => {
        const date = new Date(row.date);
        const monthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
        
        if (!monthlyData[monthYear]) {
          monthlyData[monthYear] = { sum: 0, count: 0 };
        }
        
        monthlyData[monthYear].sum += row[variable] || 0;
        monthlyData[monthYear].count += 1;
      });
      
      const months = Object.keys(monthlyData).sort();
      data = {
        dates: months,
        values: months.map(month => monthlyData[month].sum / monthlyData[month].count)
      };
    }

    const color = getColorForVariable(variable);

    return {
      title: {
        text: `Bar Plot: ${variable} vs Time`,
        left: 'center',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 14,
          color: '#001F33' // Dark Blue for text
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          return `${params[0].name}<br/>${variable}: ${params[0].value.toFixed(2)}`;
        }
      },
      xAxis: {
        type: 'category',
        data: data.dates,
        axisLabel: {
          rotate: 45,
          fontSize: 10,
          color: '#001F33',
          formatter: function(value: string) {
            return period === 'daily' ? value.substring(5, 10) : value;
          }
        }
      },
      yAxis: {
        type: 'value',
        name: variable,
        nameTextStyle: {
          fontSize: 12,
          color: '#001F33'
        },
        axisLabel: {
          fontSize: 10,
          color: '#001F33'
        }
      },
      series: [
        {
          name: variable,
          type: 'bar',
          data: data.values,
          itemStyle: {
            color: color,
            borderRadius: [4, 4, 0, 0]
          }
        }
      ],
      grid: {
        left: '10%',
        right: '5%',
        top: '20%',
        bottom: '20%'
      }
    };
  };

  // Create plots for each variable
  const variablePlots = variables.map(variable => createBarPlotOptions(variable));

  return (
    <div className="space-y-4">
      {variablePlots.map((options, index) => (
        <div key={index} className="bg-white p-2 rounded-lg shadow-sm">
          <ReactECharts
            option={options}
            style={{ height: '170px' }}
            opts={{ renderer: 'canvas' }}
          />
        </div>
      ))}
    </div>
  );
};

export default EnvironmentPlot;