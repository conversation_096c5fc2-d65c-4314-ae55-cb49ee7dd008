import { ApiSchedule, WeeklySchedule } from '@/pages/Settings/types';
import { api } from './api';

const SCHEDULES_ENDPOINT = '/schedules';

/**
 * Fetches all schedules from the API
 * @returns Promise containing an array of ApiSchedule objects
 */
export const getSchedules = async (): Promise<ApiSchedule[]> => {
  try {
    const response = await api.get(`${SCHEDULES_ENDPOINT}/`);
    const responseData = response.data;
    
    // Check if the response is already an array (direct format)
    if (Array.isArray(responseData)) {
      return responseData as ApiSchedule[];
    }
    
    // Otherwise, check if it's in the wrapped API response format
    if (responseData && 
        typeof responseData === 'object' && 
        'success' in responseData && 
        responseData.success === true && 
        'data' in responseData && 
        Array.isArray(responseData.data)) {
      return responseData.data as ApiSchedule[];
    }
    
    console.error('Invalid API response format:', responseData);
    throw new Error('Invalid data format received from API');
  } catch (error) {
    console.error('Error fetching schedules:', error);
    throw error;
  }
};

/**
 * Fetches a specific schedule by ID
 * @param id The ID of the schedule to fetch
 * @returns Promise containing the ApiSchedule object
 */
export const getScheduleById = async (id: string): Promise<ApiSchedule> => {
  try {
    const response = await api.get(`${SCHEDULES_ENDPOINT}/${id}/`);
    const responseData = response.data;
    
    // Check if the response is already a schedule object (direct format)
    if (responseData && typeof responseData === 'object' && 'id' in responseData) {
      return responseData as ApiSchedule;
    }
    
    // Otherwise, check if it's in the wrapped API response format
    if (responseData && 
        typeof responseData === 'object' && 
        'success' in responseData && 
        responseData.success === true && 
        'data' in responseData) {
      return responseData.data as ApiSchedule;
    }
    
    console.error(`Invalid API response format for schedule ${id}:`, responseData);
    throw new Error(`Failed to fetch schedule ${id}: Invalid response format`);
  } catch (error) {
    console.error(`Error fetching schedule ${id}:`, error);
    throw error;
  }
};

/**
 * Creates a new schedule
 * @param schedule The schedule to create, including an ID
 * @returns Promise containing the created ApiSchedule object
 */
export const createSchedule = async (schedule: ApiSchedule): Promise<ApiSchedule> => {
  try {
    // Make sure schedule has the required fields
    const schedulePayload = {
      id: schedule.id,
      name: schedule.name,
      description: schedule.description || '',
      active: schedule.active,
      controls: schedule.controls || [],
      weekly_schedule: schedule.weekly_schedule,
      special_days: schedule.special_days || []
    };
    
    const response = await api.post(`${SCHEDULES_ENDPOINT}/`, schedulePayload);
    const responseData = response.data;
    
    // Check if the response is already a schedule object (direct format)
    if (responseData && typeof responseData === 'object' && 'id' in responseData) {
      return responseData as ApiSchedule;
    }
    
    // Otherwise, check if it's in the wrapped API response format
    if (responseData && 
        typeof responseData === 'object' && 
        'success' in responseData && 
        responseData.success === true && 
        'data' in responseData) {
      return responseData.data as ApiSchedule;
    }
    
    console.error('Invalid API response format from create schedule:', responseData);
    throw new Error('Failed to create schedule: Invalid response format');
  } catch (error) {
    console.error('Error creating schedule:', error);
    throw error;
  }
};

/**
 * Updates an existing schedule
 * @param id The ID of the schedule to update
 * @param schedule The updated schedule data
 * @returns Promise containing the updated ApiSchedule object
 */
export const updateSchedule = async (id: string, schedule: Partial<ApiSchedule>): Promise<ApiSchedule> => {
  try {
    const response = await api.put(`${SCHEDULES_ENDPOINT}/${id}/`, schedule);
    const responseData = response.data;
    
    // Check if the response is already a schedule object (direct format)
    if (responseData && typeof responseData === 'object' && 'id' in responseData) {
      return responseData as ApiSchedule;
    }
    
    // Otherwise, check if it's in the wrapped API response format
    if (responseData && 
        typeof responseData === 'object' && 
        'success' in responseData && 
        responseData.success === true && 
        'data' in responseData) {
      return responseData.data as ApiSchedule;
    }
    
    console.error(`Invalid API response format for update schedule ${id}:`, responseData);
    throw new Error(`Failed to update schedule ${id}: Invalid response format`);
  } catch (error) {
    console.error(`Error updating schedule ${id}:`, error);
    throw error;
  }
};

/**
 * Updates only the weekly schedule portion of a schedule
 * @param id The ID of the schedule to update
 * @param weeklySchedule The updated weekly schedule data
 * @returns Promise containing the updated ApiSchedule object
 */
export const updateWeeklySchedule = async (id: string, weeklySchedule: WeeklySchedule): Promise<ApiSchedule> => {
  return updateSchedule(id, { weekly_schedule: weeklySchedule });
};

/**
 * Deletes a schedule
 * @param id The ID of the schedule to delete
 */
export const deleteSchedule = async (id: string): Promise<void> => {
  try {
    const response = await api.delete(`${SCHEDULES_ENDPOINT}/${id}/`);
    const responseData = response.data;
    
    // For delete, success can be empty response, true response, or wrapped success response
    if (
      // Empty/null/undefined response (success with no content)
      !responseData ||
      // Simple boolean response
      responseData === true ||
      // Object with success property
      (typeof responseData === 'object' && 
       'success' in responseData && 
       responseData.success === true)
    ) {
      return;
    }
    
    console.error(`Invalid API response format for delete schedule ${id}:`, responseData);
    throw new Error(`Failed to delete schedule ${id}: Invalid response format`);
  } catch (error) {
    console.error(`Error deleting schedule ${id}:`, error);
    throw error;
  }
}; 