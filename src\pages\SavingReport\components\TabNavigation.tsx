import React from 'react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { BarChart2, <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

const TabNavigation: React.FC = () => {
  const { activeTab, setActiveTab } = useSavingDashboard();

  // Cast the activeTab to string and use string type for consistency with the Tabs component
  const handleTabChange = (value: string) => {

    setActiveTab(value as 'baseline' | 'reporting' | 'summary');
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
      <TabsList className="w-fit h-8 shadow-sm">
        <TabsTrigger value="baseline" className="text-xs px-2 py-1 flex items-center gap-1">
          <LineChart size={14} />
          Baseline
        </TabsTrigger>
        <TabsTrigger value="reporting" className="text-xs px-2 py-1 flex items-center gap-1">
          <BarChart2 size={14} />
          Reporting
        </TabsTrigger>
        <TabsTrigger value="summary" className="text-xs px-2 py-1 flex items-center gap-1">
          <PieChart size={14} />
          Summary
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

export default TabNavigation;