import React, { useMemo, useRef, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { WeatherDataItem, TemperatureData } from '@/types/summaryTypes';
import { DateTime } from 'luxon';
import { useAuth } from '@/contexts/AuthContext';

interface BoxPlotProps {
  weatherData: WeatherDataItem[];
  yAxisLabel?: string;
  temperatureUnit?: string;
  startDate?: Date | null; // Add start date for filtering
  endDate?: Date | null; // Add end date for filtering
}

const BoxPlot: React.FC<BoxPlotProps> = ({
  weatherData,
  yAxisLabel = 'Outdoor Drybulb Temperature (°F)',
  temperatureUnit = '°F',
  startDate = null,
  endDate = null,
}) => {
  // Get timezone from auth context
  const { site } = useAuth();
  const timezone = site?.timezone || 'Asia/Bangkok';

  // Add chart reference to properly handle disposal
  const chartRef = useRef<ReactECharts>(null);

  // Ensure chart is properly disposed on unmount
  useEffect(() => {
    return () => {
      try {
        const instance = chartRef.current?.getEchartsInstance();
        if (instance && typeof instance.dispose === 'function') {
          instance.dispose();
        }
      } catch (e) {
        console.warn('Failed to dispose chart:', e);
      }
    };
  }, []);

  // Filter data based on date range
  const filteredWeatherData = useMemo(() => {
    // Ensure weatherData is an array before using filter
    if (!Array.isArray(weatherData)) return [];
    if (!startDate || !endDate || weatherData.length === 0) return weatherData;
    
    // Convert start and end dates to Luxon DateTime objects with timezone
    const start = DateTime.fromJSDate(startDate).setZone(timezone).startOf('day');
    const end = DateTime.fromJSDate(endDate).setZone(timezone).endOf('day');
    
    return weatherData.filter(item => {
      const itemDate = DateTime.fromISO(item.timestamp).setZone(timezone);
      return itemDate >= start && itemDate <= end;
    });
  }, [weatherData, startDate, endDate, timezone]);
  
  // Process the API data into the format needed for the boxplot
  const processData = (): { data: TemperatureData[], dates: string[] } => {
    // Default empty data if no weather data is available
    if (!Array.isArray(filteredWeatherData) || filteredWeatherData.length === 0) {
      return { data: [], dates: [] };
    }
    
    // Sort data by timestamp to ensure chronological order
    const sortedData = [...filteredWeatherData].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    const boxplotData: TemperatureData[] = [];
    const dates: string[] = [];

    sortedData.forEach(item => {
      // Format the date for display using Luxon
      const date = DateTime.fromISO(item.timestamp).setZone(timezone);
      const formattedDate = `${date.toLocaleString({ month: 'short' })} ${date.day}`;
      dates.push(formattedDate);

      // Calculate Q1 and Q3 values from min, median, and max
      const min = item.min_drybulb_temperature;
      const max = item.max_drybulb_temperature;
      const median = item.median_drybulb_temperature;
      
      // Ensure min ≤ median ≤ max (handle inconsistent data)
      const sortedValues = [min, median, max].sort((a, b) => a - b);
      const validMin = sortedValues[0];
      const validMedian = sortedValues[1];
      const validMax = sortedValues[2];
      
      // Calculate quartiles using the corrected values
      const q1 = (validMin + validMedian) / 2;
      const q3 = (validMedian + validMax) / 2;

      boxplotData.push({
        min: validMin,
        q1: q1,
        median: validMedian,
        q3: q3,
        max: validMax
      });
    });

    return { data: boxplotData, dates };
  };

  const { data, dates } = processData();

  const getOption = (): EChartsOption => {
    // If no data, return default option with empty data
    if (data.length === 0 || dates.length === 0) {
      return {
        backgroundColor: '#ffffff',
        grid: {
          left: '10%',
          right: '5%',
          bottom: '15%',
          top: '30px',
          containLabel: true
        },
        xAxis: { 
          type: 'category',
          data: [],
          boundaryGap: true,
        },
        yAxis: { 
          type: 'value',
          name: yAxisLabel
        },
        series: []
      };
    }
    
    // ECharts expects boxplot data to be prepared in a specific way
    // Each data item is [min, q1, median, q3, max]
    const boxplotData = data.map(item => [item.min, item.q1, item.median, item.q3, item.max]);
    
    // Calculate y-axis min and max values with some padding
    const allValues = data.flatMap(item => [item.min, item.max]);
    const minVal = Math.floor(Math.min(...allValues)) - 1;
    const maxVal = Math.ceil(Math.max(...allValues)) + 1;

    // Calculate marks for y-axis
    const range = maxVal - minVal;
    const markStep = Math.ceil(range / 5); // Aim for about 5 marks
    const marks = [];
    for (let i = minVal; i <= maxVal; i += markStep) {
      marks.push(i);
    }
    
    return {
      backgroundColor: '#ffffff',
      tooltip: {
        trigger: 'item',
        axisPointer: {
          type: 'shadow'
        },
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderWidth: 0,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 10,
        textStyle: {
          color: '#333',
          fontSize: 12
        },
        formatter: function(params: any) {
          // Return early if it's a legend item or we don't have data for this index
          if (!params.value || !data[params.dataIndex]) {
            return '';
          }
          
          const boxData = data[params.dataIndex];
          return `
            <div style="padding: 3px; font-family: 'Helvetica Neue', sans-serif;">
              <div style="font-weight: 600; margin-bottom: 6px;">${dates[params.dataIndex]}</div>
              <div style="display: flex; justify-content: space-between;">
                <span style="font-weight: 500; color: #666;">Maximum:</span>
                <span style="font-weight: 600;">${boxData.max.toFixed(1)}${temperatureUnit}</span>
              </div>
              <div style="display: flex; justify-content: space-between;">
                <span style="font-weight: 500; color: #666;">Upper Quartile:</span>
                <span style="font-weight: 600;">${boxData.q3.toFixed(1)}${temperatureUnit}</span>
              </div>
              <div style="display: flex; justify-content: space-between;">
                <span style="font-weight: 500; color: #666;">Median:</span>
                <span style="font-weight: 600;">${boxData.median.toFixed(1)}${temperatureUnit}</span>
              </div>
              <div style="display: flex; justify-content: space-between;">
                <span style="font-weight: 500; color: #666;">Lower Quartile:</span>
                <span style="font-weight: 600;">${boxData.q1.toFixed(1)}${temperatureUnit}</span>
              </div>
              <div style="display: flex; justify-content: space-between;">
                <span style="font-weight: 500; color: #666;">Minimum:</span>
                <span style="font-weight: 600;">${boxData.min.toFixed(1)}${temperatureUnit}</span>
              </div>
            </div>
          `;
        }
      },
      legend: {
        show: false
      },
      grid: {
        left: '8%',
        right: '5%',
        bottom: '15%', 
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        boundaryGap: true,
        axisLine: {
          lineStyle: {
            color: '#E0E0E0',
            width: 1
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#666',
          fontFamily: "'Helvetica Neue', Arial, sans-serif",
          fontSize: 11,
          rotate: dates.length > 5 ? 45 : 0,
          margin: 12
        }
      },
      yAxis: {
        type: 'value',
        name: yAxisLabel,
        min: minVal,
        max: maxVal,
        nameLocation: 'middle',
        nameGap: 45,
        nameTextStyle: {
          fontSize: 12,
          color: '#666',
          fontFamily: "'Helvetica Neue', Arial, sans-serif",
          padding: [0, 0, 5, 0],
          fontWeight: 500
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#EEEEEE',
            width: 1
          }
        },
        axisLabel: {
          color: '#666',
          fontFamily: "'Helvetica Neue', Arial, sans-serif",
          fontSize: 11,
          formatter: `{value} ${temperatureUnit}`
        }
      },
      series: [
        {
          name: 'Temperature Range',
          type: 'boxplot',
          data: boxplotData,
          itemStyle: {
            borderWidth: 1.5,
            borderColor: '#1E88E5',
            color: 'rgba(33, 150, 243, 0.7)'
          },
          emphasis: {
            itemStyle: {
              borderWidth: 2,
              shadowBlur: 10,
              shadowColor: 'rgba(0,0,0,0.1)'
            }
          }
        }
      ]
    };
  };

  return (
    <CardContent className="bg-white rounded-md p-0">
      {data.length > 0 ? (
        <ReactECharts
          ref={chartRef}
          option={getOption()}
          style={{ height: '300px', width: '100%' }}
          notMerge={true}
          opts={{ renderer: 'canvas' }}
        />
      ) : (
        <div className="h-[300px] flex items-center justify-center text-muted-foreground">
          No temperature data available
        </div>
      )}
    </CardContent>
  );
};

export default BoxPlot; 