import React, { createContext, useContext, useState, ReactNode, useCallback, useEffect, useMemo } from 'react';
import { DataRow, ReportingDataRow, RegressionResult, VariableSelection, DateRange, SummaryStats, TabType } from '../types';
import { parseCSV } from '../utils/dataProcessing';
import { performLinearRegression } from '../utils/statisticalUtils';
import { generateReportingData, calculateSummaryStats } from '../utils/reportGenerator';
import { saveAppState, loadAppState, deleteAllSavedData, hasSavedState, SavedState } from '../utils/fileStorage';
import { DateRange as DayPickerRange } from 'react-day-picker';

import { ElectricityRateValues } from '../components/ElectricityRateDialog';

// Map to store electricity rate values by year and month
export type ElectricityRateMap = Map<string, ElectricityRateValues>;

// Function to generate a key for the electricity rate map
export const getElectricityRateKey = (year: number, month: number): string => {
  return `${year}-${month}`;
};

interface SavingDashboardContextType {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  baselineData: DataRow[];
  setBaselineData: (data: DataRow[]) => void;
  filteredData: DataRow[];
  reportingData: ReportingDataRow[];
  variableSelection: VariableSelection;
  setVariableSelection: (selection: VariableSelection) => void;
  baselineRange: DateRange;
  setBaselineRange: (range: DateRange) => void;
  reportingRange: DateRange;
  setReportingRange: (range: DateRange) => void;
  weekdayFilterEnabled: boolean;
  setWeekdayFilterEnabled: (enabled: boolean) => void;
  regressionResult: RegressionResult | null;
  summaryStats: SummaryStats | null;
  importData: (files: File[]) => Promise<void>;
  runRegression: () => void;
  generateReport: (switchToReportingTab?: boolean) => void;
  saveCurrentState: () => void;
  loadSavedState: () => boolean;
  deleteSavedData: () => void;
  hasSavedData: boolean;
  excludedBaselineRowIds: Set<number>;
  setExcludedBaselineRowIds: React.Dispatch<React.SetStateAction<Set<number>>>;
  excludedReportingRowIds: Set<number>;
  setExcludedReportingRowIds: React.Dispatch<React.SetStateAction<Set<number>>>;
  // Electricity rate values
  thbPerKwh: number;
  setThbPerKwh: (value: number) => void;
  electricityRateMap: ElectricityRateMap;
  getElectricityRate: (year: number, month: number) => ElectricityRateValues | undefined;
  setElectricityRate: (values: ElectricityRateValues) => void;
  getCurrentElectricityRate: (selectedYear: number, selectedMonth: number) => ElectricityRateValues;
}

const defaultDateRange: DateRange = {
  start: '',
  end: ''
};

const SavingDashboardContext = createContext<SavingDashboardContextType | undefined>(undefined);

// Default electricity rate values
const createDefaultElectricityRateValues = (year: number, month: number): ElectricityRateValues => ({
  totalConsumption: 2501976,
  energyCharge: 8767734.83,
  ftCharge: 918725.59,
  year,
  month,
  altoTechSharePercentage: 70, // Default 70% for AltoTech
  customerSharePercentage: 30, // Default 30% for customer
  customerName: 'Customer' // Default customer name
});

// Create a default electricity rate map
const createDefaultElectricityRateMap = (): ElectricityRateMap => {
  const map = new Map<string, ElectricityRateValues>();
  const currentYear = new Date().getFullYear();

  // Add default values for current year, all months
  map.set(getElectricityRateKey(currentYear, 0), createDefaultElectricityRateValues(currentYear, 0));

  return map;
};

export const SavingDashboardProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [activeTab, setActiveTab] = useState<TabType>('baseline');
  const [baselineData, setBaselineData] = useState<DataRow[]>([]);
  const [reportingData, setReportingData] = useState<ReportingDataRow[]>([]);
  const [variableSelection, setVariableSelection] = useState<VariableSelection>({
    dependent: '',
    independent: []
  });
  const [baselineRange, setBaselineRange] = useState<DateRange>(defaultDateRange);
  const [reportingRange, setReportingRange] = useState<DateRange>(defaultDateRange);
  const [weekdayFilterEnabled, setWeekdayFilterEnabled] = useState<boolean>(false);
  const [regressionResult, setRegressionResult] = useState<RegressionResult | null>(null);
  const [summaryStats, setSummaryStats] = useState<SummaryStats | null>(null);
  const [hasSavedData, setHasSavedData] = useState<boolean>(hasSavedState());
  const [excludedBaselineRowIds, setExcludedBaselineRowIds] = useState<Set<number>>(new Set());
  const [excludedReportingRowIds, setExcludedReportingRowIds] = useState<Set<number>>(new Set());

  // Electricity rate values
  const [thbPerKwh, setThbPerKwh] = useState<number>(4.37);
  const [electricityRateMap, setElectricityRateMap] = useState<ElectricityRateMap>(createDefaultElectricityRateMap());

  // Function to get electricity rate values for a specific year and month
  const getElectricityRate = useCallback((year: number, month: number): ElectricityRateValues | undefined => {
    // First try to get exact match for year and month
    const exactKey = getElectricityRateKey(year, month);
    if (electricityRateMap.has(exactKey)) {
      return electricityRateMap.get(exactKey);
    }

    // If not found, try to get the rate for all months in that year
    const yearKey = getElectricityRateKey(year, 0);
    if (electricityRateMap.has(yearKey)) {
      return electricityRateMap.get(yearKey);
    }

    // If still not found, return undefined
    return undefined;
  }, [electricityRateMap]);

  // Function to set electricity rate values
  const setElectricityRate = useCallback((values: ElectricityRateValues) => {
    setElectricityRateMap(prevMap => {
      const newMap = new Map(prevMap);
      const key = getElectricityRateKey(values.year, values.month);
      newMap.set(key, values);
      return newMap;
    });
  }, []);

  // Function to get the current electricity rate based on selected year and month
  const getCurrentElectricityRate = useCallback((selectedYear: number, selectedMonth: number): ElectricityRateValues => {
    // Try to get the rate for the specific month
    const monthRate = getElectricityRate(selectedYear, selectedMonth);
    if (monthRate) return monthRate;

    // Try to get the rate for all months in the year
    const yearRate = getElectricityRate(selectedYear, 0);
    if (yearRate) return yearRate;

    // If no rate is found, create a default one
    return createDefaultElectricityRateValues(selectedYear, selectedMonth);
  }, [getElectricityRate]);

  // Load saved state on component mount
  useEffect(() => {
    const savedState = loadAppState();
    if (savedState) {
      setBaselineData(savedState.baselineData);
      setBaselineRange(savedState.baselineRange);
      setReportingRange(savedState.reportingRange);
      setVariableSelection(savedState.variableSelection);

      if (savedState.reportingData && savedState.reportingData.length > 0) {
        setReportingData(savedState.reportingData);
      }
    }
  }, []);

  const importData = async (files: File[]): Promise<void> => {
    if (files.length === 0) return;

    try {
      const data = await parseCSV(files[0]);

      // Add ID if not present
      const dataWithIds = data.map((row, index) => ({
        ...row,
        id: row.id || index + 1
      }));

      setBaselineData(dataWithIds);

      // Auto-detect date ranges if data is available
      if (dataWithIds.length > 0) {
        const sortedDates = [...dataWithIds].sort((a, b) =>
          new Date(a.date).getTime() - new Date(b.date).getTime()
        );

        setBaselineRange({
          start: sortedDates[0].date,
          end: sortedDates[Math.floor(sortedDates.length / 2)].date
        });

        setReportingRange({
          start: sortedDates[Math.floor(sortedDates.length / 2) + 1].date,
          end: sortedDates[sortedDates.length - 1].date
        });
      }
    } catch (error) {
      console.error('Error importing data:', error);
      throw error; // Re-throw the error so it can be caught by the UI
    }
  };

  const runRegression = useCallback(() => {
    if (baselineData.length === 0) return;

    // Filter data based on baseline range and weekday filter if enabled
    const filteredData = baselineData.filter(row => {
      const rowDate = new Date(row.date);
      const startDate = baselineRange.start ? new Date(baselineRange.start) : new Date(0);
      const endDate = baselineRange.end ? new Date(baselineRange.end) : new Date(9999, 11, 31);

      // First check date range
      const inDateRange = rowDate >= startDate && rowDate <= endDate;

      // Then apply weekday filter if enabled
      if (weekdayFilterEnabled) {
        const dayOfWeek = rowDate.getDay();
        // 0 is Sunday, 6 is Saturday - so 1-5 are weekdays
        return inDateRange && dayOfWeek >= 1 && dayOfWeek <= 5;
      }

      return inDateRange;
    });

    // ALSO filter by excluded baseline rows
    const finalFilteredData = filteredData.filter(
      row => row.id !== undefined && !excludedBaselineRowIds.has(row.id)
    );

    if (finalFilteredData.length === 0) {
      console.warn('No baseline data points remaining after filtering for regression.');
      setRegressionResult(null); // Clear previous result if no data
      return;
    }

    try {
      // Verify all selected independent variables exist in the data
      const columnsInData = Object.keys(finalFilteredData[0]);
      const validIndependentVars = variableSelection.independent.filter(
        v => columnsInData.includes(v)
      );

      // Only proceed if dependent var exists and at least one independent var
      if (!columnsInData.includes(variableSelection.dependent) || validIndependentVars.length === 0) {
        console.warn('Cannot run regression: Missing dependent or independent variables in data');
        return;
      }

      const result = performLinearRegression(
        finalFilteredData,
        variableSelection.dependent,
        validIndependentVars
      );

      setRegressionResult(result);
    } catch (error) {
      console.error('Error running regression:', error);
      // Could display an error message here if needed
    }
  }, [baselineData, baselineRange, variableSelection, weekdayFilterEnabled, excludedBaselineRowIds]);

  // Filter baseline data based on date range and weekday filter
  const filteredData = useMemo(() => {
    return baselineData.filter(row => {
      const rowDate = new Date(row.date);
      const startDate = baselineRange.start ? new Date(baselineRange.start) : new Date(0);
      const endDate = baselineRange.end ? new Date(baselineRange.end) : new Date(9999, 11, 31);

      // First check date range
      const inDateRange = rowDate >= startDate && rowDate <= endDate;

      // Then apply weekday filter if enabled
      if (weekdayFilterEnabled) {
        const dayOfWeek = rowDate.getDay();
        // 0 is Sunday, 6 is Saturday - so 1-5 are weekdays
        return inDateRange && dayOfWeek >= 1 && dayOfWeek <= 5;
      }

      return inDateRange;
    });
  }, [baselineData, baselineRange, weekdayFilterEnabled]);

  const generateReport = useCallback((switchToReportingTab = true) => {
    if (!regressionResult || baselineData.length === 0) return;

    // Filter baseline data first (date, weekday, exclusion) to pass to generateReportingData if needed
    const filteredBaselineForReport = baselineData.filter(row => {
        const rowDate = new Date(row.date);
        const startDate = baselineRange.start ? new Date(baselineRange.start) : new Date(0);
        const endDate = baselineRange.end ? new Date(baselineRange.end) : new Date(9999, 11, 31);
        const inDateRange = rowDate >= startDate && rowDate <= endDate;
        let passesWeekdayFilter = true;
        if (weekdayFilterEnabled) {
          const dayOfWeek = rowDate.getDay();
          passesWeekdayFilter = dayOfWeek >= 1 && dayOfWeek <= 5;
        }
        const isExcluded = row.id !== undefined && excludedBaselineRowIds.has(row.id);
        return inDateRange && passesWeekdayFilter && !isExcluded;
    });

    // Generate reporting data from the ENTIRE baseline dataset (as per original logic)
    // reportGenerator itself doesn't filter by date/weekday/exclusion
    const allReportData = generateReportingData(baselineData, regressionResult, variableSelection);
    setReportingData(allReportData);

    // Filter the generated reporting data based on the reporting range, weekday filter, AND exclusion
    const filteredReportData = allReportData.filter(row => {
      if (!reportingRange.start || !reportingRange.end) return true;

      const rowDate = new Date(row.date);
      const startDate = new Date(reportingRange.start);
      const endDate = new Date(reportingRange.end);
      const inDateRange = rowDate >= startDate && rowDate <= endDate;

      let passesWeekdayFilter = true;
      if (weekdayFilterEnabled) {
        const dayOfWeek = rowDate.getDay();
        passesWeekdayFilter = dayOfWeek >= 1 && dayOfWeek <= 5;
      }

      const isExcluded = row.id !== undefined && excludedReportingRowIds.has(row.id);

      return inDateRange && passesWeekdayFilter && !isExcluded;
    });

    // Calculate summary statistics using the filtered reporting data
    const summary = calculateSummaryStats(
      filteredReportData.length > 0 ? filteredReportData : [], // Use filtered, or empty if none pass filters
      reportingRange,
      regressionResult,
      filteredBaselineForReport, // Pass filtered baseline data
      variableSelection
    );
    setSummaryStats(summary);

    // Switch to reporting tab only if requested
    if (switchToReportingTab) {
      setActiveTab('reporting');
    }
  }, [
    baselineData, // Keep dependency
    baselineRange, // Keep dependency
    reportingRange,
    regressionResult,
    setActiveTab,
    variableSelection,
    weekdayFilterEnabled,
    excludedBaselineRowIds, // Add dependency
    excludedReportingRowIds // Add dependency
  ]);

  // Update summary stats when reporting range, weekday filter, or exclusions change
  useEffect(() => {
    if (reportingData.length > 0 && regressionResult) {
      // Filter baseline data (date, weekday, exclusion)
      const filteredBaselineForStats = baselineData.filter(row => {
          const rowDate = new Date(row.date);
          const startDate = baselineRange.start ? new Date(baselineRange.start) : new Date(0);
          const endDate = baselineRange.end ? new Date(baselineRange.end) : new Date(9999, 11, 31);
          const inDateRange = rowDate >= startDate && rowDate <= endDate;
          let passesWeekdayFilter = true;
          if (weekdayFilterEnabled) {
            const dayOfWeek = rowDate.getDay();
            passesWeekdayFilter = dayOfWeek >= 1 && dayOfWeek <= 5;
          }
          const isExcluded = row.id !== undefined && excludedBaselineRowIds.has(row.id);
          return inDateRange && passesWeekdayFilter && !isExcluded;
      });

      // Filter reporting data (reporting range, weekday, exclusion)
      const filteredReportingData = reportingData.filter(row => {
        if (!reportingRange.start || !reportingRange.end) {
          return !(row.id !== undefined && excludedReportingRowIds.has(row.id)); // Apply exclusion even if no range
        }

        const rowDate = new Date(row.date);
        const startDate = new Date(reportingRange.start);
        const endDate = new Date(reportingRange.end);
        const inDateRange = rowDate >= startDate && rowDate <= endDate;

        let passesWeekdayFilter = true;
        if (weekdayFilterEnabled) {
          const dayOfWeek = rowDate.getDay();
          passesWeekdayFilter = dayOfWeek >= 1 && dayOfWeek <= 5;
        }

        const isExcluded = row.id !== undefined && excludedReportingRowIds.has(row.id);
        return inDateRange && passesWeekdayFilter && !isExcluded;
      });

      // Recalculate summary stats using fully filtered data
      const summary = calculateSummaryStats(
        filteredReportingData, // Use the filtered reporting data
        reportingRange,
        regressionResult,
        filteredBaselineForStats, // Use filtered baseline data
        variableSelection
      );
      setSummaryStats(summary);
    }
  }, [
    baselineData, // Keep dependency
    baselineRange, // Keep dependency
    reportingRange,
    reportingData,
    regressionResult,
    variableSelection,
    weekdayFilterEnabled,
    excludedBaselineRowIds, // Add dependency
    excludedReportingRowIds // Add dependency
  ]);

  // Automatically generate report when switching to reporting tab
  useEffect(() => {
    // Only generate a report automatically if we have a regression result but no reporting data
    if (activeTab === 'reporting' && reportingData.length === 0 && regressionResult) {
      // Add a slight delay to prevent possible race conditions
      const timer = setTimeout(() => {
        generateReport(true);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [activeTab, reportingData.length, regressionResult, generateReport]);

  // Save current application state
  const saveCurrentState = useCallback(() => {
    const timestamp = new Date().toISOString();

    // Convert Map to array for serialization
    const electricityRates = Array.from(electricityRateMap.entries());

    const stateToSave: SavedState = {
      baselineData,
      baselineRange,
      reportingRange,
      variableSelection,
      reportingData,
      weekdayFilterEnabled,
      electricityRates,
      savedAt: timestamp
    };

    saveAppState(stateToSave);
    setHasSavedData(true);
  }, [baselineData, baselineRange, reportingRange, variableSelection, reportingData, weekdayFilterEnabled, electricityRateMap]);

  // Load saved application state
  const loadSavedState = useCallback(() => {
    const state = loadAppState();
    if (!state) return false;

    setBaselineData(state.baselineData);
    setBaselineRange(state.baselineRange);
    setReportingRange(state.reportingRange);
    setVariableSelection(state.variableSelection);

    if (state.reportingData && state.reportingData.length > 0) {
      setReportingData(state.reportingData);
    }

    if (typeof state.weekdayFilterEnabled === 'boolean') {
      setWeekdayFilterEnabled(state.weekdayFilterEnabled);
    }

    // Load electricity rate values if available
    if (state.electricityRates && Array.isArray(state.electricityRates)) {
      const newMap = new Map<string, ElectricityRateValues>();
      state.electricityRates.forEach(([key, value]) => {
        newMap.set(key, value);
      });
      setElectricityRateMap(newMap);
    }

    // Reset exclusions on load
    setExcludedBaselineRowIds(new Set());
    setExcludedReportingRowIds(new Set());

    return true;
  }, []);

  // Delete all saved data (also reset exclusions)
  const deleteSavedData = useCallback(() => {
    deleteAllSavedData();
    setHasSavedData(false);
    // Reset exclusions state as well
    setExcludedBaselineRowIds(new Set());
    setExcludedReportingRowIds(new Set());
  }, []);

  // Calculate thbPerKwh based on the current year and month
  useEffect(() => {
    // Get the current date for default values
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

    // Get the rate for the current month/year
    const currentRate = getCurrentElectricityRate(currentYear, currentMonth);

    // Calculate the rate
    if (currentRate && currentRate.totalConsumption > 0) {
      const calculatedRate = (currentRate.energyCharge + currentRate.ftCharge) / currentRate.totalConsumption;
      setThbPerKwh(calculatedRate);
    }
  }, [electricityRateMap, getCurrentElectricityRate]);

  return (
    <SavingDashboardContext.Provider
      value={{
        activeTab,
        setActiveTab,
        baselineData,
        setBaselineData,
        filteredData,
        reportingData,
        variableSelection,
        setVariableSelection,
        baselineRange,
        setBaselineRange,
        reportingRange,
        setReportingRange,
        weekdayFilterEnabled,
        setWeekdayFilterEnabled,
        regressionResult,
        summaryStats,
        importData,
        runRegression,
        generateReport,
        saveCurrentState,
        loadSavedState,
        deleteSavedData,
        hasSavedData,
        excludedBaselineRowIds,
        setExcludedBaselineRowIds,
        excludedReportingRowIds,
        setExcludedReportingRowIds,
        // Electricity rate values
        thbPerKwh,
        setThbPerKwh,
        electricityRateMap,
        getElectricityRate,
        setElectricityRate,
        getCurrentElectricityRate
      }}
    >
      {children}
    </SavingDashboardContext.Provider>
  );
};

export const useSavingDashboard = () => {
  const context = useContext(SavingDashboardContext);
  if (context === undefined) {
    throw new Error('useSavingDashboard must be used within a SavingDashboardProvider');
  }
  return context;
};