import React, { useEffect, useState } from 'react';
import { GifConfig } from '@/pages/Map/components/types';
import { ImageIcon } from 'lucide-react';
import { useRealtime } from '@/contexts/RealtimeContext';
import { getMedia } from '@/services/mapService';

interface GifProps {
  config: GifConfig;
  isEditMode?: boolean;
  onClick?: (e: React.MouseEvent) => void;
}

export const Gif: React.FC<GifProps> = ({ config, isEditMode, onClick }) => {
  const { getValue } = useRealtime();
  const datapointValue = getValue(config.properties.deviceId, config.properties.datapoint);
  const [mediaUrl, setMediaUrl] = useState<string | null>(null);
  const [mediaType, setMediaType] = useState<string>('image/gif');

  useEffect(() => {
    const loadMedia = async () => {
      if (config.properties.gifName) {
        try {
          const mediaBlob = await getMedia(config.properties.gifName);
          const type = mediaBlob.type || 'image/gif';
          setMediaType(type);
          const url = URL.createObjectURL(mediaBlob);
          setMediaUrl(url);
        } catch (error) {
          console.error('Error loading media:', error);
        }
      }
    };

    loadMedia();

    return () => {
      if (mediaUrl) {
        URL.revokeObjectURL(mediaUrl);
      }
    };
  }, [config.properties.gifName]);

  // Default to 1 if zoomLevel is not set
  const zoomLevel = config.properties.zoomLevel || 1;
  const baseHeight = 200; // Increased base height for better scaling

  const renderContent = () => {
    // In edit mode, show image if exists, otherwise show placeholder
    if (isEditMode) {
      if (mediaUrl) {
        return (
          <img 
            src={mediaUrl} 
            alt="Media content"
            className="w-auto object-contain transition-opacity duration-200"
            style={{ 
              position: 'absolute',
              left: '50%',
              top: '50%',
              transform: `translate(-50%, -50%) scale(${zoomLevel})`,
              opacity: 0.5,
              height: `${baseHeight}px`,
              maxWidth: 'none'
            }}
          />
        );
      }
      return (
        <div className="flex items-center justify-center bg-black/20 rounded-lg" 
             style={{ 
               width: `${baseHeight}px`, 
               height: `${baseHeight}px`,
               transform: `scale(${zoomLevel})`
             }}>
          <ImageIcon className="w-1/2 h-1/2 text-white/30" />
        </div>
      );
    }

    // Not in edit mode, only show image if device is running
    if (datapointValue && mediaUrl) {
      return (
        <img 
          src={mediaUrl} 
          alt="Media content"
          className="w-auto object-contain transition-opacity duration-200"
          style={{ 
            position: 'absolute',
            left: '50%',
            top: '50%',
            transform: `translate(-50%, -50%) scale(${zoomLevel})`,
            height: `${baseHeight}px`,
            maxWidth: 'none'
          }}
        />
      );
    }

    // Not in edit mode and device is off - show nothing
    return null;
  };

  return (
    <div
      onClick={onClick}
      className={`relative ${isEditMode ? 'ring-1 ring-primary cursor-move' : ''}`}
      style={{ 
        minWidth: `${baseHeight * zoomLevel}px`, 
        minHeight: `${baseHeight * zoomLevel}px`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      {renderContent()}
    </div>
  );
}; 