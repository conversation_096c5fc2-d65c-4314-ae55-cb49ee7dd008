import { api } from './api';

// Simple encryption key (in a real app, this would be more secure)
const ROLE_ENCRYPTION_KEY = 'alto-cero-auth-key';

interface ApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: T;
}

interface LoginResponse<T> extends ApiResponse<T> {}

interface UserSite {
  site_id: string;
  site_name: string;
  role: string;
  is_primary: boolean;
}

interface UserInfo {
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  primary_site?: string;
  primary_site_role?: 'admin' | 'operator' | 'viewer';
  sites?: UserSite[];
  is_superuser?: boolean;
  timezone?: string;
}

interface UserInfoResponse {
  user: UserInfo;
}

interface LoginData {
  access_token: string;
  token_type: string;
  user: {
    id: number;
    username: string;
    email: string;
    primary_site?: string;
  };
}

// Simple encryption/decryption functions
const encryptRole = (role: string): string => {
  // This is a very simple encryption - in production you'd want something more secure
  return btoa(`${role}:${ROLE_ENCRYPTION_KEY}:${Date.now()}`);
};

const decryptRole = (encrypted: string | null): string | null => {
  if (!encrypted) return null;
  
  try {
    const decrypted = atob(encrypted);
    const parts = decrypted.split(':');
    
    if (parts.length !== 3 || parts[1] !== ROLE_ENCRYPTION_KEY) {
      // Invalid format or wrong key
      return null;
    }
    
    return parts[0];
  } catch (e) {
    console.error('Error decrypting role:', e);
    return null;
  }
};

let lastUserInfoFetch = 0;
const MIN_FETCH_INTERVAL = 30000; // 30 seconds minimum between fetches

export const login = async (username: string, password: string): Promise<LoginData> => {
  console.log('Logging in with username:', username, 'and password:', password);
  const response = await api.post<LoginResponse<LoginData>>('/auth/login/', { username, password });
  const { data: loginData } = response.data;
  console.log('Response payload:', response.data);
  localStorage.setItem('auth_token', loginData.access_token);
  console.log('Token set in localStorage:', loginData.access_token);
  
  // Store site_id in localStorage if it exists
  if (loginData.user.primary_site) {
    localStorage.setItem('site_id', loginData.user.primary_site);
    console.log('Site ID set in localStorage:', loginData.user.primary_site);
  }
  
  return loginData;
};

export const getUserInfo = async (): Promise<UserInfo> => {
  const now = Date.now();
  
  // Check if we've fetched recently - if so, return the cached data from localStorage
  if (now - lastUserInfoFetch < MIN_FETCH_INTERVAL) {
    // Try to get data from localStorage if available
    const cachedUser = localStorage.getItem('cached_user_info');
    if (cachedUser) {
      return JSON.parse(cachedUser);
    }
  }
  
  // Update the timestamp
  lastUserInfoFetch = now;
  
  const response = await api.get<ApiResponse<UserInfoResponse>>('/auth/me/');
  const userInfo = response.data.data.user;
  
  // Store superuser status in localStorage if present
  if (userInfo.is_superuser !== undefined) {
    localStorage.setItem('is_superuser', userInfo.is_superuser ? 'true' : 'false');
  }
  
  // Cache the user info
  localStorage.setItem('cached_user_info', JSON.stringify(userInfo));
  
  return userInfo;
};

export const logout = (): void => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('site_id');
  localStorage.removeItem('user_role');
  localStorage.removeItem('is_superuser');
  window.location.href = '/login';
};

export const isAuthenticated = (): boolean => {
  return !!localStorage.getItem('auth_token');
};

export const getSiteId = (): string | null => {
  return localStorage.getItem('site_id');
};

export const getUserRole = (): string | null => {
  return decryptRole(localStorage.getItem('user_role'));
};

export const setUserRole = (role: string): void => {
  localStorage.setItem('user_role', encryptRole(role));
};

export const isSuperUser = (): boolean => {
  return localStorage.getItem('is_superuser') === 'true';
};
