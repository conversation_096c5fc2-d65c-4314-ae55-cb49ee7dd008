import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getDashAppUrl } from '@/utils/dash';

const AFDD: React.FC = () => {
  const { site } = useAuth();
  const dashboardAppUrl = getDashAppUrl('afdd', site?.id);

  return (
    <div className="w-full h-[calc(100vh-71px)] overflow-hidden bg-background">
      <iframe 
        src={dashboardAppUrl}
        className="w-full h-full border-none"
        title="AFDD Dashboard"
        sandbox="allow-same-origin allow-scripts allow-forms"
      />
    </div>
  );
};

export default AFDD; 