import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/utils/supabase';

interface AutopilotData {
  [deviceId: string]: {
    status: string;
    level: string;
    current_automation_stage: string;
    current_automation_id: number;
    site_id: string;
    updated_at: string;
    updated_by_id: number;
    current_gear_id: number;
  };
}

interface AutopilotContextType {
  isEnabled: boolean;
  setIsEnabled: (enabled: boolean) => void;
  autopilotData: AutopilotData;
  isDeviceInAutopilotMode: (deviceId: string) => boolean;
}

const AutopilotContext = createContext<AutopilotContextType | undefined>(undefined);

export function AutopilotProvider({ children }: { children: React.ReactNode }) {
  const [isEnabled, setIsEnabled] = useState(false);
  const [autopilotData, setAutopilotData] = useState<AutopilotData>({});

  useEffect(() => {
    // Initial data fetch
    const fetchInitialData = async () => {
      const { data, error } = await supabase
        .from('autopilot')
        .select('*');

      if (error) {
        console.error('Error fetching autopilot data:', error);
        return;
      }

      const initialData: AutopilotData = {};
      data.forEach((row) => {
        initialData[row.device_id] = {
          status: row.autopilot_status,
          level: row.level,
          current_automation_stage: row.current_automation_stage,
          current_automation_id: row.current_automation_id,
          site_id: row.site_id,
          updated_at: row.updated_at,
          updated_by_id: row.updated_by_id,
          current_gear_id: row.current_gear_id
        };
      });

      setAutopilotData(initialData);
    };

    fetchInitialData();

    // Subscribe to real-time updates
    const channel = supabase
      .channel('autopilot_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'autopilot',
        },
        (payload) => {
          if (payload.eventType === 'DELETE') {
            // Handle deletion by removing the device from autopilotData
            setAutopilotData((prev) => {
              const newData = { ...prev };
              if (payload.old && payload.old.device_id) {
                delete newData[payload.old.device_id];
              }
              return newData;
            });
            return;
          }

          // Handle insert or update
          if (payload.new && payload.new.device_id) {
            setAutopilotData((prev) => ({
              ...prev,
              [payload.new.device_id]: {
                status: payload.new.autopilot_status,
                level: payload.new.level,
                current_automation_stage: payload.new.current_automation_stage,
                current_automation_id: payload.new.current_automation_id,
                site_id: payload.new.site_id,
                updated_at: payload.new.updated_at,
                updated_by_id: payload.new.updated_by_id,
                current_gear_id: payload.new.current_gear_id
              },
            }));
          }
        }
      )
      .subscribe((status) => {
        console.log('Autopilot subscription status:', status);
      });

    return () => {
      console.log('Unsubscribing from autopilot updates');
      channel.unsubscribe();
    };
  }, []);

  // Function to determine if a device should show manual icon
  // Returns true if the device is in autopilot mode (should not show manual icon)
  const isDeviceInAutopilotMode = (deviceId: string): boolean => {
    // If device is not in autopilot table OR status is "active", it's NOT in manual mode
    if (!autopilotData[deviceId] || autopilotData[deviceId].status === 'active') {
      return true;
    }
    else {
      return false;
    }
  };

  return (
    <AutopilotContext.Provider value={{ 
      isEnabled, 
      setIsEnabled, 
      autopilotData, 
      isDeviceInAutopilotMode 
    }}>
      {children}
    </AutopilotContext.Provider>
  );
}

export function useAutopilot() {
  const context = useContext(AutopilotContext);
  if (context === undefined) {
    throw new Error('useAutopilot must be used within an AutopilotProvider');
  }
  return context;
} 