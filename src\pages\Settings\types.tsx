// API Schedule interfaces for automation components

export interface ApiSchedule {
  id: string;
  name: string;
  description: string;
  active: boolean;
  controls: ControlPair[];
  weekly_schedule: WeeklySchedule;
  special_days: any[]; // Type can be refined if needed
  created_at?: string;
  updated_at?: string;
}

export interface WeeklySchedule {
  monday: TimeRange[];
  tuesday: TimeRange[];
  wednesday: TimeRange[];
  thursday: TimeRange[];
  friday: TimeRange[];
  saturday: TimeRange[];
  sunday: TimeRange[];
}

export interface TimeRange {
  start_time: string; // "HH:MM" format
  end_time: string;   // "HH:MM" format
  value: boolean | number | string;
}

export interface ControlPair {
  device_id: string;
  datapoint: string;
}

export interface Schedule {
  id: string;
  name: string;
  description?: string;
  active: boolean;
  events?: ScheduleEvent[];
}

// Define the structure for actions within an event
export interface EventAction {
  controlPairIndex: number; // Index referencing Schedule.controls
  value: boolean | string | number; // Value to set
}

// Define the structure for a time block in the schedule
export interface ScheduleEvent {
  id: string;
  day: string; // e.g., 'MON', 'TUE'
  startMinute: number; // Minute of the day (0-1439)
  endMinute: number; // Minute of the day (0-1439)
  actions: EventAction[]; // Actions to perform during this block
}
