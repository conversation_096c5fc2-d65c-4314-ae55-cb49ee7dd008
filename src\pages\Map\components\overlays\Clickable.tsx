import React, { useState, useEffect, useRef } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ClickableConfig } from '../types';
import { cn } from '@/lib/utils';
import { useRealtime } from '@/contexts/RealtimeContext';
import chillerImage from '@/assets/chiller.png';

interface ClickableProps {
  config: ClickableConfig;
  isEditMode?: boolean;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
}

// ChillerPopupContent component to display realtime data
interface ChillerPopupContentProps {
  deviceId: string;
}

const ChillerPopupContent: React.FC<ChillerPopupContentProps> = ({ deviceId }) => {
  const { getValue } = useRealtime();

  // Define datapoint type
  type Datapoint = {
    label: string;
    key: string;
    unit?: string;
    precision?: number;
    format?: (val: any) => string;
    group?: string;
  };

  // Organize datapoints by groups
  const datapoints: Datapoint[] = [
    // General
    { label: 'Status', key: 'status_read', format: (val: any) => val === 1 ? 'Running' : 'Stopped', group: 'general' },
    { label: 'Chilled Water Setpoint', key: 'setpoint_read', unit: '°F', precision: 1, group: 'general' },
    { label: 'Demand Limit Setpoint', key: 'demand_limit_setpoint_read', unit: '%', precision: 0, group: 'general' },
    { label: 'Efficiency', key: 'efficiency', unit: 'kW/RT', precision: 3, group: 'general' },
    { label: 'Running Capacity', key: 'running_capacity_percentage', unit: '%', precision: 2, format: (val: any) => ((Number(val) * 100)).toFixed(1) + ' %', group: 'general' },
    { label: 'Alarm', key: 'alarm', format: (val: any) => val === 1 ? 'Active' : 'None', group: 'general' },
    { label: 'Cooling Rate', key: 'cooling_rate', unit: 'RT', precision: 0, group: 'general' },
    { label: 'Power', key: 'power', unit: 'kW', precision: 1, group: 'general' },

    // Evaporator
    { label: 'Leaving Water Temp', key: 'evap_leaving_water_temperature', unit: '°F', precision: 1, group: 'evaporator' },
    { label: 'Entering Water Temp', key: 'evap_entering_water_temperature', unit: '°F', precision: 1, group: 'evaporator' },
    { label: 'Sat. Refrig. Temp', key: 'evap_sat_refrig_temperature', unit: '°F', precision: 1, group: 'evaporator' },
    { label: 'Sat. Refrig. Pressure', key: 'evap_sat_refrig_pressure', unit: 'kPa', precision: 0, group: 'evaporator' },
    { label: 'Water Flow Rate', key: 'evap_water_flow_rate', unit: 'GPM', precision: 1, group: 'evaporator' },
    { label: 'Water Flow Status', key: 'evap_water_flow_status', format: (val: any) => val === 1 ? 'Flow' : 'No Flow', group: 'evaporator' },
    { label: 'Approach Temp', key: 'evap_approach_temperature', unit: '°F', precision: 1, group: 'evaporator' },

    // Condenser
    { label: 'Leaving Water Temp', key: 'cond_leaving_water_temperature', unit: '°F', precision: 1, group: 'condenser' },
    { label: 'Entering Water Temp', key: 'cond_entering_water_temperature', unit: '°F', precision: 1, group: 'condenser' },
    { label: 'Sat. Refrig. Temp', key: 'cond_sat_refrig_temperature', unit: '°F', precision: 1, group: 'condenser' },
    { label: 'Sat. Refrig. Pressure', key: 'cond_sat_refrig_pressure', unit: 'kPa', precision: 0, group: 'condenser' },
    { label: 'Water Flow Rate', key: 'cond_water_flow_rate', unit: 'GPM', precision: 1, group: 'condenser' },
    { label: 'Water Flow Status', key: 'cond_water_flow_status', format: (val: any) => val === 1 ? 'Flow' : 'No Flow', group: 'condenser' },
    { label: 'Approach Temp', key: 'cond_approach_temperature', unit: '°F', precision: 1, group: 'condenser' },

    // Compressor
    { label: 'Compressor Starts', key: 'compressor_starts', format: (val: any) => {
      return Number(val).toLocaleString();
    }, group: 'compressor' },
    { label: 'Compressor Running time', key: 'compressor_runtime', format: (val: any) => {
      const hours = Math.floor(Number(val) / 3600);
      return hours.toLocaleString() + ' hrs';
    }, group: 'compressor' },
    { label: 'Oil Tank Pressure', key: 'oil_tank_pressure', unit: 'kPa', precision: 0, group: 'compressor' },
    { label: 'Oil Pump Disc. Pressure', key: 'oil_pump_disc_pressure', unit: 'kPa', precision: 0, group: 'compressor' },
    { label: 'Oil Diff. Pressure', key: 'oil_diff_pressure', unit: 'kPa', precision: 0, group: 'compressor' },
    { label: 'Oil Tank Temp', key: 'oil_tank_temperature', unit: '°F', precision: 1, group: 'compressor' },

    // Motor
    { label: 'Avg Current % RLA', key: 'percentage_rla', unit: '%', precision: 1, group: 'motor' },
    { label: 'Current L1 % RLA', key: 'percentage_rla_l1', unit: '%', precision: 1, group: 'motor' },
    { label: 'Current L2 % RLA', key: 'percentage_rla_l2', unit: '%', precision: 1, group: 'motor' },
    { label: 'Current L3 % RLA', key: 'percentage_rla_l3', unit: '%', precision: 1, group: 'motor' },
    { label: 'Current L1', key: 'current_l1', unit: 'A', precision: 1, group: 'motor' },
    { label: 'Current L2', key: 'current_l2', unit: 'A', precision: 1, group: 'motor' },
    { label: 'Current L3', key: 'current_l3', unit: 'A', precision: 1, group: 'motor' },
    { label: 'Voltage AB', key: 'voltage_l1l2', unit: 'V', precision: 0, group: 'motor' },
    { label: 'Voltage BC', key: 'voltage_l2l3', unit: 'V', precision: 0, group: 'motor' },
    { label: 'Voltage CA', key: 'voltage_l3l1', unit: 'V', precision: 0, group: 'motor' },
    { label: 'Motor Winding Temp 1', key: 'motor_winding_temp_1', unit: '°F', precision: 1, group: 'motor' },
    { label: 'Motor Winding Temp 2', key: 'motor_winding_temp_2', unit: '°F', precision: 1, group: 'motor' },
    { label: 'Motor Winding Temp 3', key: 'motor_winding_temp_3', unit: '°F', precision: 1, group: 'motor' },
    { label: 'Input Power Consumption', key: 'power', unit: 'kW', precision: 1, group: 'motor' },

    // Purge
    { label: 'Purge Liquid Temp', key: 'purge_liquid_temperature', unit: '°F', precision: 1, group: 'purge' },
    { label: 'Purge Refrig. Cprsr. Suction Temp', key: 'purge_refrig_cprsr_suction_temperature', unit: '°F', precision: 1, group: 'purge' },
    { label: 'Carbon Tank Temp', key: 'carbon_tank_temperature', unit: '°F', precision: 1, group: 'purge' }
  ];

  // Get status to determine color
  const status = getValue(deviceId, 'status_read');
  const isRunning = status === 1;

  // Group datapoints by category
  const generalPoints = datapoints.filter(d => d.group === 'general');
  const evaporatorPoints = datapoints.filter(d => d.group === 'evaporator');
  const condenserPoints = datapoints.filter(d => d.group === 'condenser');
  const compressorPoints = datapoints.filter(d => d.group === 'compressor');
  const motorPoints = datapoints.filter(d => d.group === 'motor');
  const purgePoints = datapoints.filter(d => d.group === 'purge');

  // Helper function to render a datapoint
  const renderDatapoint = (datapoint: Datapoint) => {
    // Skip status since we already show it above
    if (datapoint.key === 'status_read') return null;

    const value = getValue(deviceId, datapoint.key);
    let displayValue = '--';

    if (value !== null && value !== undefined) {
      if (datapoint.format) {
        displayValue = datapoint.format(value);
      } else if (datapoint.precision !== undefined) {
        displayValue = `${Number(value).toFixed(datapoint.precision)}${datapoint.unit ? ' ' + datapoint.unit : ''}`;
      } else {
        displayValue = `${value}${datapoint.unit ? ' ' + datapoint.unit : ''}`;
      }
    }

    // Special handling for alarm datapoint
    if (datapoint.key === 'alarm') {
      return (
        <div key={datapoint.key} className="bg-white p-1 rounded-md">
          <div className="text-xs text-gray-500 leading-tight">{datapoint.label}</div>
          <div className="text-xs font-semibold leading-tight">
            <span className={value === 1 ? 'text-red-500' : 'text-green-500'}>
              {value === 1 ? 'Alarm' : 'None'}
            </span>
          </div>
        </div>
      );
    }

    return (
      <div key={datapoint.key} className="bg-white p-1 rounded-md">
        <div className="text-xs text-gray-500 leading-tight">{datapoint.label}</div>
        <div className="text-xs font-semibold leading-tight">{displayValue}</div>
      </div>
    );
  };

  return (
    <div className="mt-1">
      {/* Main content grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {/* Left column: Chiller image and status summary */}
        <div className="space-y-2">
          {/* Chiller image */}
          <div className="relative w-full h-[500px] bg-white rounded-lg overflow-hidden flex items-center justify-center">
            <img
              src={chillerImage}
              alt="Chiller"
              className="object-contain max-h-[450px] max-w-full"
              onError={(e) => {
                // Fallback if image fails to load
                const target = e.target as HTMLImageElement;
                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE2MCIgZmlsbD0iI2YzZjRmNiIvPjx0ZXh0IHg9IjEwMCIgeT0iODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzZiNzI4MCI+Q2hpbGxlciBJbWFnZTwvdGV4dD48L3N2Zz4=';
              }}
            />
            <div className="absolute top-3 right-3 flex items-center gap-1">
              <div className={`w-5 h-5 rounded-full ${isRunning ? 'bg-[#14B8B4]' : 'bg-gray-400'}`} />
              <span className={`text-xs font-medium ${isRunning ? 'text-[#14B8B4]' : 'text-gray-500'}`}>
                {isRunning ? 'ON' : 'OFF'}
              </span>
            </div>
          </div>

          {/* System Summary and General Information */}
          <div className="border rounded-lg p-2 space-y-2">
            <div className="flex items-center justify-between gap-1 flex-wrap">
              <div className="flex items-center gap-1">
                <span className="font-medium text-sm">
                  {isRunning ? 'Running' : 'Stopped'}
                </span>
              </div>
              <div className="text-primary font-medium text-xs">
                System Summary & General Information
              </div>
            </div>

            {/* General Information and Key metrics */}
            <div className="grid grid-cols-2 gap-1">
              {/* Display general datapoints */}
              {generalPoints.map(renderDatapoint)}
            </div>
          </div>
        </div>

        {/* Right column: All data categories */}
        <div className="space-y-2">
          {/* Row 1: Evaporator and Condenser */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {/* Evaporator */}
            <div className="border rounded-lg p-1">
              <div className="flex items-center gap-1 mb-0.5 text-primary font-medium text-xs">
                <h3>Evaporator</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                {evaporatorPoints.map(renderDatapoint)}
              </div>
            </div>

            {/* Condenser */}
            <div className="border rounded-lg p-1">
              <div className="flex items-center gap-1 mb-0.5 text-primary font-medium text-xs">
                <h3>Condenser</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                {condenserPoints.map(renderDatapoint)}
              </div>
            </div>
          </div>

          {/* Row 2: Motor (full width) */}
          <div className="border rounded-lg p-1">
            <div className="flex items-center gap-1 mb-0.5 text-primary font-medium text-xs">
              <h3>Motor</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
              {motorPoints.map(renderDatapoint)}
            </div>
          </div>

          {/* Row 3: Compressor and Purge */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {/* Compressor */}
            <div className="border rounded-lg p-1">
              <div className="flex items-center gap-1 mb-0.5 text-primary font-medium text-xs">
                <h3>Compressor</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                {compressorPoints.map(renderDatapoint)}
              </div>
            </div>

            {/* Purge */}
            <div className="border rounded-lg p-1">
              <div className="flex items-center gap-1 mb-0.5 text-primary font-medium text-xs">
                <h3>Purge</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                {purgePoints.map(renderDatapoint)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const Clickable: React.FC<ClickableProps> = ({
  config,
  isEditMode,
  onClick,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { popup_type, payload, zoomLevel = 1 } = config.properties;
  const baseHeight = 200; // Same as in Gif component

  const handleClick = (e: React.MouseEvent) => {
    if (isEditMode) {
      onClick?.(e);
      return;
    }

    // Handle different popup types or actions directly using the RealtimeContext
    switch (popup_type) {
      case 'chiller_popup':
        // Simply open the dialog - data will be fetched by the ChillerPopupContent component
        setIsOpen(true);
        break;
      case 'page_redirect':
        // For page redirects, use the payload directly
        if (payload.redirect_url) {
          window.location.href = payload.redirect_url;
        } else {
          console.warn('No redirect URL specified in payload');
        }
        break;
      // Add more cases as needed
      default:
        console.warn('Unknown popup type:', popup_type);
    }
  };

  return (
    <>
      <div
        className={cn(
          "clickable-trigger cursor-pointer",
          isEditMode ? "ring-1 ring-primary" : "",
          className
        )}
        onClick={handleClick}
        style={{
          minWidth: `${baseHeight * zoomLevel}px`,
          minHeight: `${baseHeight * zoomLevel}px`,
          opacity: isEditMode ? 0.3 : 0.0, // Invisible in view mode, semi-transparent in edit mode
          backgroundColor: isEditMode ? 'rgba(2, 116, 189, 0.2)' : 'transparent',
          border: isEditMode ? '1px dashed #0274BD' : 'none',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {isEditMode && (
          <div className="text-xs text-center font-medium text-primary">
            📌
          </div>
        )}
      </div>

      {popup_type === 'chiller_popup' && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="sm:max-w-[1400px] max-h-[90vh] overflow-y-auto p-6 pt-10">
            <DialogHeader className="sticky top-0 bg-background z-10 pb-2 mb-2 border-b">
              <DialogTitle className="text-xl font-bold text-primary flex items-center gap-2">
                {payload?.device_id ? payload.device_id.replace('chiller_', 'CH-') : 'Chiller'}
              </DialogTitle>
            </DialogHeader>

            <ChillerPopupContent deviceId={payload?.device_id || 'chiller_default'} />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};