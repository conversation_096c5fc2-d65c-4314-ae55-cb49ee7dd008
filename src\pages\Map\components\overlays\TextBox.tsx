import React from 'react';
import { TextBoxConfig } from '@/pages/Map/components/types';

interface TextBoxProps {
  config: TextBoxConfig;
  isEditMode?: boolean;
  onClick?: (e: React.MouseEvent) => void;
}

export const TextBox: React.FC<TextBoxProps> = ({ config, isEditMode, onClick }) => {
  return (
    <div
      className="overflow-hidden shadow-sm"
      style={{ 
        width: '12vmin',
        minHeight: '3vmin',
        borderRadius: '0.3vmin',
        backgroundColor: config.properties.backgroundColor,
      }}
    >
      <span 
        className="text-[1vmin] font-medium whitespace-pre-wrap break-words block overflow-hidden px-[0.8vmin] py-[0.6vmin]" 
        style={{ color: config.properties.textColor }}
      >
        {config.properties.text}
      </span>
    </div>
  );
}; 