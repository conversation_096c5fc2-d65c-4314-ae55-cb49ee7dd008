// Chiller Settings Modal Component
// Manages the configuration of chiller-specific settings including capacity,
// load limits, and control features like VFD and free cooling

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { X, ArrowUpDown, ArrowDown, Settings2 } from 'lucide-react';

// Interface for component props
export interface ChillerSettingsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  // Add other props specific to chiller settings if required
}

export function ChillerSettingsModal({
  open,
  onOpenChange,
}: ChillerSettingsModalProps) {
  // State management for chiller settings
  const [chillerType, setChillerType] = useState("air-cooled");
  const [capacity, setCapacity] = useState("");
  const [minLoad, setMinLoad] = useState("");
  const [maxLoad, setMaxLoad] = useState("");
  const [vfdEnabled, setVfdEnabled] = useState(false);
  const [freeCoolingEnabled, setFreeCoolingEnabled] = useState(false);

  // Handler for saving changes
  const handleSave = () => {
    // TODO: Implement save logic for chiller settings
    console.log('Saving chiller settings...');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[500px] w-full bg-white border border-[#DBE4FF] rounded-lg shadow-[1px_3px_20px_0px_rgba(57,124,221,0.30)]">
        <DialogHeader>
          <DialogTitle className="text-[#065BA9] text-lg font-semibold">
            Chiller Settings
          </DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Chiller Type Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right text-[#065BA9]">Type</Label>
            <div className="col-span-3">
              <Select value={chillerType} onValueChange={setChillerType}>
                <SelectTrigger className="h-9 border-[#DBE4FF] focus:border-[#0E7EE4]">
                  <SelectValue placeholder="Select chiller type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="air-cooled">Air Cooled</SelectItem>
                  <SelectItem value="water-cooled">Water Cooled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Capacity Input */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right text-[#065BA9]">Capacity</Label>
            <div className="col-span-3">
              <Input
                type="number"
                value={capacity}
                onChange={(e) => setCapacity(e.target.value)}
                className="h-9 border-[#DBE4FF] focus:border-[#0E7EE4]"
                placeholder="Enter capacity in tons"
              />
            </div>
          </div>

          {/* Minimum Load Input */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right text-[#065BA9]">Min Load</Label>
            <div className="col-span-3">
              <Input
                type="number"
                value={minLoad}
                onChange={(e) => setMinLoad(e.target.value)}
                className="h-9 border-[#DBE4FF] focus:border-[#0E7EE4]"
                placeholder="Enter minimum load percentage"
              />
            </div>
          </div>

          {/* Maximum Load Input */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right text-[#065BA9]">Max Load</Label>
            <div className="col-span-3">
              <Input
                type="number"
                value={maxLoad}
                onChange={(e) => setMaxLoad(e.target.value)}
                className="h-9 border-[#DBE4FF] focus:border-[#0E7EE4]"
                placeholder="Enter maximum load percentage"
              />
            </div>
          </div>

          {/* VFD Control Toggle */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right text-[#065BA9]">VFD Control</Label>
            <div className="col-span-3">
              <Switch
                checked={vfdEnabled}
                onCheckedChange={setVfdEnabled}
                className="data-[state=checked]:bg-[#0E7EE4]"
              />
            </div>
          </div>

          {/* Free Cooling Toggle */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right text-[#065BA9]">Free Cooling</Label>
            <div className="col-span-3">
              <Switch
                checked={freeCoolingEnabled}
                onCheckedChange={setFreeCoolingEnabled}
                className="data-[state=checked]:bg-[#0E7EE4]"
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="text-[#065BA9] border-[#DBE4FF] hover:bg-[#F9FAFF] hover:border-[#0E7EE4]"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSave}
            className="bg-[#0E7EE4] text-white hover:bg-[#0E7EE4]/90"
          >
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}