import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";

interface LineChartProps {
  deviceId: string;
  datapoints: string[];
  compareMode: boolean;
  secondaryYAxisDatapoints: string[];
  dateRange: [Date, Date];
}

interface DataPoint {
  timestamp: string;
  value: number;
  datapoint: string;
}

// Mock data generator for now - will be replaced with API call
const generateMockData = (
  deviceId: string, 
  datapoints: string[], 
  startDate: Date, 
  endDate: Date, 
  compareMode: boolean
): DataPoint[] => {
  const data: DataPoint[] = [];
  const dayDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const numPoints = compareMode ? 24 * 6 : dayDiff * 24 * 6; // Every 10 minutes
  
  for (const datapoint of datapoints) {
    // Create base pattern for the datapoint
    const baseValue = Math.random() * 100 + 50;
    const amplitude = Math.random() * 30 + 10;
    const phaseShift = Math.random() * Math.PI;
    
    for (let i = 0; i < numPoints; i++) {
      const currentTime = new Date(startDate.getTime());
      
      if (compareMode) {
        // In compare mode, we overlay data from different days at the same time of day
        currentTime.setHours(Math.floor(i / 6));
        currentTime.setMinutes((i % 6) * 10);
      } else {
        // In regular mode, we show data across the full date range
        currentTime.setTime(startDate.getTime() + (i * 10 * 60 * 1000));
      }
      
      // Add some randomness and patterns
      let value = baseValue;
      value += amplitude * Math.sin((i / numPoints) * 2 * Math.PI + phaseShift);
      
      // Add daily patterns
      const hour = currentTime.getHours();
      if (hour >= 9 && hour <= 17) {
        value += 20; // Higher during work hours
      }
      
      // Add some noise
      value += (Math.random() - 0.5) * 10;
      
      data.push({
        timestamp: currentTime.toISOString(),
        value,
        datapoint
      });
    }
  }
  
  return data;
};

const LineChart: React.FC<LineChartProps> = ({ 
  deviceId, 
  datapoints, 
  compareMode, 
  secondaryYAxisDatapoints,
  dateRange 
}) => {
  const [chartData, setChartData] = useState<DataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    setLoading(true);
    
    // This would be replaced with an API call in production
    const mockData = generateMockData(deviceId, datapoints, dateRange[0], dateRange[1], compareMode);
    
    setChartData(mockData);
    setLoading(false);
  }, [deviceId, datapoints, dateRange, compareMode]);
  
  const getOption = (): EChartsOption => {
    // Group data by datapoint
    const datapointMap = new Map<string, DataPoint[]>();
    chartData.forEach(point => {
      if (!datapointMap.has(point.datapoint)) {
        datapointMap.set(point.datapoint, []);
      }
      datapointMap.get(point.datapoint)?.push(point);
    });
    
    // Generate series
    const series = [];
    
    for (const [datapoint, points] of datapointMap.entries()) {
      // Sort points by timestamp
      points.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      
      const isSecondaryAxis = secondaryYAxisDatapoints.includes(datapoint);
      
      series.push({
        name: datapoint,
        type: 'line' as const, // Use const assertion to satisfy TypeScript
        yAxisIndex: isSecondaryAxis ? 1 : 0,
        data: points.map(point => {
          let xValue;
          if (compareMode) {
            // In compare mode, format as time of day 
            const date = new Date(point.timestamp);
            xValue = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
          } else {
            // In regular mode, use full timestamp
            xValue = point.timestamp;
          }
          return [xValue, point.value];
        }),
        smooth: true,
        showSymbol: false,
      });
    }
    
    return {
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          let tooltip = '';
          if (params[0]?.axisValue) {
            tooltip += `${params[0].axisValue}<br/>`;
          }
          
          params.forEach((param: any) => {
            const color = param.color;
            const seriesName = param.seriesName;
            const value = param.value[1].toFixed(2);
            tooltip += `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span> ${seriesName}: ${value}<br/>`;
          });
          
          return tooltip;
        }
      },
      legend: {
        data: Array.from(datapointMap.keys()),
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '60px',
        top: '40px',
        containLabel: true
      },
      xAxis: {
        type: 'category', // Always use 'category' to avoid typescript errors
        name: compareMode ? 'Time of Day' : 'Date',
        boundaryGap: false,
        axisLabel: {
          formatter: function(value: string) {
            if (compareMode) {
              return value; // Already formatted as time
            } else {
              const date = new Date(value);
              return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
            }
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: 'Primary Scale',
          position: 'left',
        },
        {
          type: 'value',
          name: 'Secondary Scale',
          position: 'right',
          splitLine: {
            show: false
          }
        }
      ],
      series
    };
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Historical Data: {deviceId}</CardTitle>
        <CardDescription>
          {compareMode ? 'Compare Mode (Time of Day)' : 'Timeline View'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="h-[500px] flex items-center justify-center">
            <p className="text-muted-foreground">Loading chart data...</p>
          </div>
        ) : (
          <div className="border rounded-md p-4">
            <ReactECharts 
              option={getOption()} 
              style={{ height: '500px', width: '100%' }} 
              notMerge={true}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LineChart;