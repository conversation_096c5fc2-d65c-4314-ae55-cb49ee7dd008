import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragMoveEvent,
  useDraggable,
  useDroppable,
  MouseSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { Loader } from "@/components/ui/loader";
import {
  ComponentConfig,
  TextBoxConfig,
  RealTimeValueConfig,
  MachineStatusConfig,
  GifConfig,
  AVAILABLE_COMPONENTS,
  DatapointDisplayConfig,
  ControlInputConfig,
  TransitioningGifConfig,
  ClickableConfig,
} from './components/types';
import { TextBox } from './components/overlays/TextBox';
import { RealTimeValue } from './components/overlays/RealTimeValue';
import { MachineStatus } from './components/overlays/MachineStatus';
import { Gif } from './components/overlays/Gif';
import { TransitioningGif } from './components/overlays/TransitioningGif';
import { DatapointDisplayCard } from './components/overlays/DatapointDisplayCard';
import { ControlInput } from './components/overlays/ControlInput';
import { ComponentSettings } from './components/dialogs/ComponentSettings';
import { DraggableToolbar } from './components/core/Toolbar';
import { TableConfigDialog } from './components/tables/TableConfigDialog';
import type { TableSettings } from './components/types';
import { RealtimeProvider } from '@/contexts/RealtimeContext';
import { AutopilotProvider } from '@/contexts/AutopilotContext';
import { ActionProvider } from '@/contexts/ActionContext';
import { PlantEfficiencyCard } from './components/cards/PlantEfficiencyCard';
import { CoolingLoadCard } from './components/cards/CoolingLoadCard';
import { PlantPowerCard } from './components/cards/PlantPowerCard';
import { SystemEfficiencyCard } from './components/cards/SystemEfficiencyCard';
import { EnergyUsageComparisonCard } from './components/cards/EnergyUsageComparisonCard';
import NextEventTimeline from '@/components/common/NextEventTimeline';
import { IndoorAirQualityTable } from './components/tables/IndoorAirQualityTable';
import { RiserTable } from './components/tables/RiserTable';
import {
  loadMapConfig,
  saveMapConfig,
  getMedia,
} from '@/services/mapService';
import { BackgroundImageUpload } from './components/dialogs/BackgroundImageUpload';
import { HeaderLogoUpload } from './components/dialogs/HeaderLogoUpload';
import { useAuth } from '@/contexts/AuthContext';
import { Clickable } from './components/overlays/Clickable';

interface DraggableComponentProps {
  id: string;
  item: ComponentConfig;
  isEditMode: boolean;
  onComponentClick: (e: React.MouseEvent, item: ComponentConfig) => void;
  activeId: string | null;
  dragTransform: { x: number; y: number } | null;
  onDelete: (id: string) => void;
  onZIndexChange: (id: string, change: 'up' | 'down' | 'top' | 'bottom') => void;
}

const DraggableComponent: React.FC<DraggableComponentProps> = ({
  id,
  item,
  isEditMode,
  onComponentClick,
  activeId,
  dragTransform,
  onDelete,
  onZIndexChange,
}) => {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id,
    disabled: !isEditMode,
  });

  const props = {
    config: item,
    isEditMode,
    onClick: (e: React.MouseEvent) => {
      e.stopPropagation();
      onComponentClick(e, item);
    },
  };

  const renderComponent = () => {
    switch (item.type) {
      case 'textbox':
        return <TextBox {...props} config={item as TextBoxConfig} />;
      case 'realtime-value':
        return <RealTimeValue {...props} config={item as RealTimeValueConfig} />;
      case 'machine-status':
        return <MachineStatus {...props} config={item as MachineStatusConfig} />;
      case 'gif':
        return <Gif {...props} config={item as GifConfig} />;
      case 'transitioning-gif':
        return <TransitioningGif {...props} config={item as TransitioningGifConfig} />;
      case 'datapoint-display':
        return <DatapointDisplayCard {...props} config={item as DatapointDisplayConfig} />;
      case 'control-input':
        return <ControlInput {...props} config={item as ControlInputConfig} />;
      case 'clickable':
        return <Clickable {...props} config={item as ClickableConfig} />;
      default:
        console.error('Unknown component configuration:', item);
        return null;
    }
  };

  const isBeingDragged = activeId === id;
  const transform = isBeingDragged && dragTransform
    ? `translate(calc(-50% + ${dragTransform.x}px), calc(-50% + ${dragTransform.y}px))`
    : 'translate(-50%, -50%)';

  return (
    <div
      id={id}
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      className="absolute"
      style={{
        left: `${item.xPercent}%`,
        top: `${item.yPercent}%`,
        transform,
        cursor: isEditMode ? 'move' : 'default',
        touchAction: 'none',
      }}
    >
      {renderComponent()}
    </div>
  );
};

interface MapConfig {
  zoomLevel: number;
  xOffset: number;
  yOffset: number;
  backgroundImage?: string;
}

const DEFAULT_MAP_CONFIG: MapConfig = {
  zoomLevel: 1,
  xOffset: 0,
  yOffset: 0,
  backgroundImage: undefined,
};

const Map: React.FC = () => {
  const { userRole, hasRole, isSuperuser, isLoading: authLoading, currentUser } = useAuth();
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<ComponentConfig | null>(null);
  const [toolbarPosition, setToolbarPosition] = useState({ x: 90, y: 90 });
  const [overlayItems, setOverlayItems] = useState<ComponentConfig[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [dragTransform, setDragTransform] = useState<{ x: number; y: number } | null>(null);
  const [mapConfig, setMapConfig] = useState<MapConfig>(DEFAULT_MAP_CONFIG);
  const [backgroundImageUrl, setBackgroundImageUrl] = useState<string | null>(null);
  const [isBackgroundLoading, setIsBackgroundLoading] = useState(false);
  const [isPanningEnabled, setIsPanningEnabled] = useState(false);
  const [showBackgroundUpload, setShowBackgroundUpload] = useState(false);
  const [showHeaderLogoUpload, setShowHeaderLogoUpload] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const [mapId, setMapId] = useState<string>(localStorage.getItem('currentMapId') || '1');
  const [showTableConfig, setShowTableConfig] = useState(false);
  const [tableSettings, setTableSettings] = useState<TableSettings>({
    riserTable: {
      enabled: true,
      rows: [
        { id: 'A', displayName: 'Riser A', deviceId: 'riser_A', controlDeviceId: 'riser_A' },
        { id: 'B', displayName: 'Riser B', deviceId: 'riser_B', controlDeviceId: 'riser_B' },
        { id: 'C', displayName: 'Riser C', deviceId: 'riser_C', controlDeviceId: 'riser_C' },
        { id: 'D', displayName: 'Riser D', deviceId: 'riser_D', controlDeviceId: 'riser_D' }
      ],
      datapoints: {
        flow: 'flow',
        pressure: 'pressure',
        temperature: 'temperature'
      }
    },
    indoorAirQualityTable: {
      enabled: true,
      rows: [
        { id: 1, displayName: 'Floor 1', deviceId: 'floor_1' },
        { id: 2, displayName: 'Floor 2', deviceId: 'floor_2' },
        { id: 3, displayName: 'Floor 3', deviceId: 'floor_3' },
        { id: 4, displayName: 'Floor 4', deviceId: 'floor_4' },
        { id: 5, displayName: 'Floor 5', deviceId: 'floor_5' },
        { id: 6, displayName: 'Floor 6', deviceId: 'floor_6' },
        { id: 7, displayName: 'Floor 7', deviceId: 'floor_7' },
        { id: 8, displayName: 'Floor 8', deviceId: 'floor_8' },
        { id: 9, displayName: 'Floor 9', deviceId: 'floor_9' },
        { id: 10, displayName: 'Floor 10', deviceId: 'floor_10' }
      ],
      datapoints: {
        temperature: 'temperature',
        humidity: 'humidity',
        co2: 'co2'
      }
    }
  });

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 5,
    },
  });

  const sensors = useSensors(mouseSensor);

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  // Handle header logo upload
  const [headerLogo, setHeaderLogo] = useState<string | undefined>(undefined);

  useEffect(() => {
    const loadSavedConfig = async () => {
      try {
        const config = await loadMapConfig(mapId);
        if (config.components.length > 0) {
          setOverlayItems(config.components);
        }

        if (config.properties.toolbarPosition) {
          setToolbarPosition(config.properties.toolbarPosition);
        }

        if (config.properties.mapConfig) {
          setMapConfig(config.properties.mapConfig);
        }

        // Load table config if available
        if (config.properties.tableConfig) {
          setTableSettings(config.properties.tableConfig);
        }

        // Get header logo if available
        if (config.properties.headerLogo) {
          setHeaderLogo(config.properties.headerLogo);
        }
      } catch (error) {
        console.error('Failed to load map configuration:', error);
      }
    };

    if (mapId) {
      loadSavedConfig();
    }
  }, [mapId]);

  // Handle header logo upload
  const handleHeaderLogoUploaded = (filename: string | null) => {
    setHeaderLogo(filename || undefined);
  };

  // Update saveCurrentState
  const saveCurrentState = useCallback(async (): Promise<boolean> => {
    try {
      // Create config object with correct handling of null headerLogo
      const configToSave = {
        components: overlayItems,
        properties: {
          toolbarPosition,
          mapConfig,
          tableConfig: tableSettings,
          // If headerLogo is undefined/null, explicitly set it to null
          headerLogo: headerLogo === undefined ? null : headerLogo
        }
      };

      console.log('Saving map configuration:', configToSave);
      await saveMapConfig(mapId, configToSave);
      return true;
    } catch (error) {
      console.error('Failed to save map configuration:', error);
      return false;
    }
  }, [mapId, overlayItems, toolbarPosition, mapConfig, tableSettings, headerLogo]);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragMove = (event: DragMoveEvent) => {
    if (!containerRef.current) return;

    setDragTransform({
      x: event.delta.x,
      y: event.delta.y
    });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active } = event;

    if (active && containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      const x = event.delta.x;
      const y = event.delta.y;

      if (active.id === 'toolbar') {
        setToolbarPosition(prev => ({
          x: Math.max(0, Math.min(100, prev.x + (x / rect.width) * 100)),
          y: Math.max(0, Math.min(100, prev.y + (y / rect.height) * 100))
        }));
      } else {
        setOverlayItems(items =>
          items.map(item =>
            item.id === active.id
              ? {
                  ...item,
                  xPercent: Math.max(0, Math.min(100, item.xPercent + (x / rect.width) * 100)),
                  yPercent: Math.max(0, Math.min(100, item.yPercent + (y / rect.height) * 100))
                }
              : item
          )
        );
      }
    }

    setActiveId(null);
    setDragTransform(null);
  };

  const handleComponentClick = async (e: React.MouseEvent, item: ComponentConfig) => {
    if (!isEditMode) return;
    e.stopPropagation();
    setSelectedComponent(item);
    const updatedItems = overlayItems.map(existing =>
      existing.id === item.id ? item : existing
    );
    setOverlayItems(updatedItems);
  };

  const handleComponentAdd = async (type: string) => {
    const componentDef = AVAILABLE_COMPONENTS.find(c => c.type === type);
    if (!componentDef) {
      console.error('Component type not found:', type);
      return;
    }

    // Create the new item with proper typing
    let newItem: ComponentConfig;
    switch (type) {
      case 'textbox':
        newItem = {
          id: Date.now().toString(),
          type: 'textbox',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {
            text: 'New Text',
            backgroundColor: '#ffffff',
            textColor: '#000000',
          }
        };
        break;
      case 'realtime-value':
        newItem = {
          id: Date.now().toString(),
          type: 'realtime-value',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {
            title: 'New Value',
            deviceId: '',
            datapoint: '',
            unit: '',
            precision: 2,
            backgroundColor: '#ffffff',
            textColor: '#000000',
          }
        };
        break;
      case 'machine-status':
        newItem = {
          id: Date.now().toString(),
          type: 'machine-status',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {
            deviceId: '',
            deviceName: '',
            showName: true,
            equipmentType: 'Normal' as const,
            isManualOverride: false,
          }
        };
        break;
      case 'gif':
        newItem = {
          id: Date.now().toString(),
          type: 'gif',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {
            deviceId: '',
            datapoint: '',
            gifName: '',
            zoomLevel: 1
          }
        };
        break;
      case 'plant-efficiency':
        newItem = {
          id: Date.now().toString(),
          type: 'plant-efficiency',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {}
        };
        break;
      case 'datapoint-display':
        newItem = {
          id: Date.now().toString(),
          type: 'datapoint-display',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {
            title: 'New Datapoint Display',
            datapoints: [
              {
                name: 'Temperature',
                deviceId: 'device1',
                datapoint: 'temp',
                precision: 2,
                unit: '°C'
              }
            ]
          }
        };
        break;
      case 'control-input':
        newItem = {
          id: Date.now().toString(),
          type: 'control-input',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {
            title: 'CHS Setpoint',
            deviceId: 'plant',
            datapoint: 'target_chw_setpoint',
            unit: '°F',
          }
        };
        break;
      case 'transitioning-gif':
        newItem = {
          id: Date.now().toString(),
          type: 'transitioning-gif',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {
            controlDeviceId: '',
            controlDatapoint: '',
            feedbackDeviceId: '',
            feedbackDatapoint: '',
            zoomLevel: 1
          }
        };
        break;
      case 'clickable':
        newItem = {
          id: Date.now().toString(),
          type: 'clickable',
          label: `${componentDef.label} ${overlayItems.length + 1}`,
          xPercent: 50,
          yPercent: 50,
          properties: {
            popup_type: 'chiller_popup',
            payload: {
              device_id: ''
            },
            zoomLevel: 1
          }
        };
        break;
      default:
        console.error('Unhandled component type:', type);
        return;
    }

    // Add new item to end of array (top layer)
    const updatedItems = [...overlayItems, newItem];
    setOverlayItems(updatedItems);
  };

  const handleZIndexChange = async (id: string, change: 'up' | 'down' | 'top' | 'bottom') => {
    const updatedItems = [...overlayItems];
    const itemIndex = updatedItems.findIndex(item => item.id === id);
    if (itemIndex === -1) return;

    switch (change) {
      case 'up': {
        if (itemIndex < updatedItems.length - 1) {
          // Swap with next item
          [updatedItems[itemIndex], updatedItems[itemIndex + 1]] =
          [updatedItems[itemIndex + 1], updatedItems[itemIndex]];
        }
        break;
      }
      case 'down': {
        if (itemIndex > 0) {
          // Swap with previous item
          [updatedItems[itemIndex], updatedItems[itemIndex - 1]] =
          [updatedItems[itemIndex - 1], updatedItems[itemIndex]];
        }
        break;
      }
      case 'top': {
        // Move item to end of array (top layer)
        const item = updatedItems.splice(itemIndex, 1)[0];
        updatedItems.push(item);
        break;
      }
      case 'bottom': {
        // Move item to start of array (bottom layer)
        const item = updatedItems.splice(itemIndex, 1)[0];
        updatedItems.unshift(item);
        break;
      }
    }

    setOverlayItems(updatedItems);
  };

  const handleComponentDelete = async (id: string) => {
    const updatedItems = overlayItems.filter(item => item.id !== id);
    setOverlayItems(updatedItems);
  };

  const { setNodeRef: setDroppableRef } = useDroppable({
    id: 'droppable-area',
  });

  const handleZoomIn = () => {
    setMapConfig(prev => ({
      ...prev,
      zoomLevel: Math.min(prev.zoomLevel + 0.1, 3), // Max zoom 3x
    }));
  };

  const handleZoomOut = () => {
    setMapConfig(prev => ({
      ...prev,
      zoomLevel: Math.max(prev.zoomLevel - 0.1, 1), // Min zoom 1x
    }));
  };

  const handleMapPan = useCallback((deltaX: number, deltaY: number) => {
    setMapConfig(prev => {
      const maxOffset = (prev.zoomLevel - 1) * 100; // Maximum pan range
      const sensitivity = 1 / prev.zoomLevel; // Adjust sensitivity based on zoom level

      // Apply zoom-adjusted deltas
      const adjustedDeltaX = deltaX * sensitivity;
      const adjustedDeltaY = deltaY * sensitivity;

      return {
        ...prev,
        xOffset: Math.max(-maxOffset, Math.min(maxOffset, prev.xOffset + adjustedDeltaX)),
        yOffset: Math.max(-maxOffset, Math.min(maxOffset, prev.yOffset + adjustedDeltaY)),
      };
    });
  }, []);

  const handleMapMouseDown = useCallback((e: React.MouseEvent) => {
    if (!isEditMode || mapConfig.zoomLevel <= 1 || !isPanningEnabled) return;

    const startX = e.clientX;
    const startY = e.clientY;
    let lastX = startX;
    let lastY = startY;
    let lastTime = Date.now();

    const handleMouseMove = (e: MouseEvent) => {
      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime;

      // Skip if we're getting too many events
      if (deltaTime < 16) return; // Limit to ~60fps

      const currentX = e.clientX;
      const currentY = e.clientY;

      // Calculate raw deltas
      const deltaX = currentX - lastX;
      const deltaY = currentY - lastY;

      handleMapPan(deltaX, deltaY);

      lastX = currentX;
      lastY = currentY;
      lastTime = currentTime;
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [isEditMode, mapConfig.zoomLevel, handleMapPan, isPanningEnabled]);

  const handleResetMapConfig = useCallback(() => {
    setMapConfig(prev => ({
      ...DEFAULT_MAP_CONFIG,
      backgroundImage: prev.backgroundImage
    }));
  }, []);

  const handleTableConfigSave = (settings: TableSettings) => {
    setTableSettings(settings);

    // Save the configuration immediately
    saveCurrentState();
  };

  // Load background image when mapConfig.backgroundImage changes
  useEffect(() => {
    const loadBackgroundImage = async () => {
      if (mapConfig.backgroundImage) {
        try {
          setIsBackgroundLoading(true);
          const mediaBlob = await getMedia(mapConfig.backgroundImage);
          const url = URL.createObjectURL(mediaBlob);
          setBackgroundImageUrl(url);
        } catch (error) {
          console.error('Error loading background image:', error);
          setBackgroundImageUrl(null);
        } finally {
          setIsBackgroundLoading(false);
        }
      } else {
        setBackgroundImageUrl(null);
      }
    };

    loadBackgroundImage();

    // Cleanup function to revoke object URL
    return () => {
      if (backgroundImageUrl) {
        URL.revokeObjectURL(backgroundImageUrl);
      }
    };
  }, [mapConfig.backgroundImage]);

  // Handle background image upload
  const handleBackgroundImageUploaded = (filename: string) => {
    setMapConfig(prev => ({
      ...prev,
      backgroundImage: filename
    }));
    saveCurrentState();
  };

  // Check if user has edit permissions (admin or superuser)
  const hasEditPermission = () => {
    return hasRole('admin') || isSuperuser;
  };

  // Check if user is trying to enable edit mode without proper permissions
  const handleEditModeToggle = () => {
    if (!isEditMode && !hasEditPermission()) {
      // Don't allow users without permission to enter edit mode
      console.log('Edit mode is only available for admin and superuser roles');
      return;
    }

    if (isEditMode) {
      setSelectedComponent(null);
    }
    setIsEditMode(!isEditMode);
  };

  return (
    <AutopilotProvider>
      <ActionProvider>
        <div className="w-[calc(100vw-56px)] h-[calc(100vh-71px)] overflow-auto">
          {/* Main Content Container */}
          <div className="flex flex-row bg-[#F9FAFF] w-[1864px] h-[938px]">
            {/* Left Section: Cards and Map (75%) */}
            <div className="w-[75%] py-4 h-full pl-[15px]">
              {/* Metrics Cards Section */}
              <div className="grid grid-cols-9 gap-[14px] mb-4">
                <div className="col-span-2">
                  <PlantEfficiencyCard />
                </div>
                <div className="col-span-1">
                  <PlantPowerCard />
                </div>
                <div className="col-span-2">
                  <CoolingLoadCard />
                </div>
                <div className="col-span-2">
                  <SystemEfficiencyCard />
                </div>
                <div className="col-span-2">
                  <EnergyUsageComparisonCard />
                </div>
              </div>

              {/* Map Section */}
              <div className="mt-4 h-auto relative bg-[#EDEFF9] rounded-[10px] overflow-auto border border-[#DBE4FF]">
                <div className="relative bg-white rounded-lg shadow">
                  <div
                    ref={mapContainerRef}
                    className="relative overflow-hidden"
                    onMouseDown={handleMapMouseDown}
                    style={{
                      cursor: isEditMode && mapConfig.zoomLevel > 1 ? 'move' : 'default',
                    }}
                  >
                    {backgroundImageUrl ? (
                      <img
                        src={backgroundImageUrl}
                        alt="Plant Layout"
                        className="block w-full h-auto min-w-0 will-change-transform"
                        draggable={false}
                        style={{
                          transform: `translate3d(${mapConfig.xOffset}%, ${mapConfig.yOffset}%, 0) scale(${mapConfig.zoomLevel})`,
                          transformOrigin: 'center',
                          transition: 'transform 0.1s ease-out',
                        }}
                      />
                    ) : (
                      <div
                        className="block w-full h-[600px] bg-background flex items-center justify-center"
                        style={{
                          transform: `translate3d(${mapConfig.xOffset}%, ${mapConfig.yOffset}%, 0) scale(${mapConfig.zoomLevel})`,
                          transformOrigin: 'center',
                          transition: 'transform 0.1s ease-out',
                        }}
                      >
                        {isBackgroundLoading && (
                          <div className="flex flex-col items-center gap-2">
                            <Loader />
                          </div>
                        )}
                      </div>
                    )}
                    <DndContext
                      sensors={sensors}
                      onDragStart={handleDragStart}
                      onDragMove={handleDragMove}
                      onDragEnd={handleDragEnd}
                      modifiers={[restrictToWindowEdges]}
                    >
                      <div ref={containerRef} className="absolute inset-0">
                        <div ref={setDroppableRef} className="relative w-full h-full">
                          {overlayItems.map((item) => (
                            <DraggableComponent
                              key={item.id}
                              id={item.id}
                              item={item}
                              isEditMode={isEditMode}
                              onComponentClick={handleComponentClick}
                              activeId={activeId}
                              dragTransform={dragTransform}
                              onDelete={handleComponentDelete}
                              onZIndexChange={handleZIndexChange}
                            />
                          ))}
                          <DraggableToolbar
                            position={toolbarPosition}
                            isEditMode={isEditMode}
                            onEditModeToggle={handleEditModeToggle}
                            onSave={saveCurrentState}
                            onComponentAdd={handleComponentAdd}
                            activeId={activeId}
                            dragTransform={dragTransform}
                            onZoomIn={handleZoomIn}
                            onZoomOut={handleZoomOut}
                            onReset={handleResetMapConfig}
                            isPanningEnabled={isPanningEnabled}
                            onPanningToggle={() => setIsPanningEnabled(!isPanningEnabled)}
                            onTableConfig={() => setShowTableConfig(true)}
                            onBackgroundUpload={() => setShowBackgroundUpload(true)}
                            onHeaderLogoUpload={() => setShowHeaderLogoUpload(true)}
                            mapId={mapId}
                            isAdmin={hasEditPermission()}
                          />
                        </div>
                      </div>
                    </DndContext>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Section: Control Panel */}
            <div className="w-[25%] py-4 p-[15px] h-full flex flex-col">
              <div className="flex-grow overflow-hidden">
                <NextEventTimeline className="h-full w-full" />
              </div>
              {tableSettings.indoorAirQualityTable?.enabled && (
                <IndoorAirQualityTable
                  floors={tableSettings.indoorAirQualityTable.rows}
                  datapoints={tableSettings.indoorAirQualityTable.datapoints}
                  className="w-full"
                />
              )}
              {tableSettings.riserTable?.enabled && (
                <RiserTable
                  risers={tableSettings.riserTable.rows}
                  datapoints={tableSettings.riserTable.datapoints}
                  className="w-full"
                />
              )}
            </div>
          </div>

          {/* Settings Panel */}
          {selectedComponent && (
            <ComponentSettings
              component={selectedComponent}
              onClose={() => setSelectedComponent(null)}
              onSave={(updatedConfig) => {
                const updatedItems = overlayItems.map((item) =>
                  item.id === updatedConfig.id ? updatedConfig : item
                );
                setOverlayItems(updatedItems);
              }}
              onDelete={handleComponentDelete}
              onDuplicate={(duplicatedConfig) => {
                setOverlayItems([...overlayItems, duplicatedConfig]);
              }}
              onZIndexChange={handleZIndexChange}
            />
          )}

          <TableConfigDialog
            open={showTableConfig}
            onOpenChange={setShowTableConfig}
            settings={tableSettings}
            onSave={handleTableConfigSave}
          />

          {/* Background Image Upload Dialog */}
          {showBackgroundUpload && (
            <BackgroundImageUpload
              open={showBackgroundUpload}
              onOpenChange={setShowBackgroundUpload}
              onImageUploaded={handleBackgroundImageUploaded}
            />
          )}

          {showHeaderLogoUpload && (
            <HeaderLogoUpload
              open={showHeaderLogoUpload}
              onOpenChange={setShowHeaderLogoUpload}
              onImageUploaded={handleHeaderLogoUploaded}
            />
          )}
        </div>
      </ActionProvider>
    </AutopilotProvider>
  );
};

export default Map;
