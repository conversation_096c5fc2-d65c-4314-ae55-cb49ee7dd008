// Shared types for Summary page components

/**
 * Interface for weather data items from API used in BoxPlot and ComparisonByDate
 */
export interface WeatherDataItem {
  timestamp: string;
  start_datetime: string;
  end_datetime: string;
  site_id: string;
  device_id: string;
  max_drybulb_temperature: number;
  max_wetbulb_temperature: number;
  max_humidity: number;
  min_drybulb_temperature: number;
  min_wetbulb_temperature: number;
  min_humidity: number;
  mean_drybulb_temperature: number;
  mean_wetbulb_temperature: number;
  mean_humidity: number;
  median_drybulb_temperature: number;
  median_wetbulb_temperature: number;
  median_humidity: number;
}

/**
 * Interface for temperature data used in BoxPlot and ComparisonByDate
 */
export interface TemperatureData {
  min: number;
  q1: number;
  median: number;
  q3: number;
  max: number;
}

/**
 * Interface for data points used in ScatterPlot
 */
export interface DataPoint {
  coolingLoad: number[];
  tse: number[];
  date?: string;
  timestamp?: string;
}

/**
 * Original data point format received from API
 */
export interface OriginalDataPoint {
  coolingLoad: number;
  tse: number;
  date?: string;
} 