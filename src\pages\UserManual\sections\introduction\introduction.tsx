import { Book } from 'lucide-react';
import { ManualChapter } from '../types';
import altoLogo from '@/assets/alto_logo.svg';

export const introductionContent: ManualChapter = {
  id: 'introduction',
  title: {
    en: 'Alto CERO',
    th: 'Alto CERO'
  },
  icon: <Book size={18} />,
  sections: [
    {
      id: 'introduction-overview',
      title: {
        en: 'Introduction to Alto CERO', 
        th: 'แนะนำ Alto CERO'
      },
      content: {
        en: `
          <div class="space-y-4">
            <p>
              Welcome to the Alto CERO user manual. This guide provides comprehensive information
              on how to use and manage your building's energy systems efficiently with our platform.
            </p>
            <p>
              The Alto CERO is designed to give you complete control and visibility over your building's energy consumption,
              helping you optimize operations, reduce costs, and improve sustainability.
            </p>
            
            <h3 class="text-lg font-medium mt-4">Key Features</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li>Real-time monitoring of energy consumption and system performance</li>
              <li>Advanced analytics and reporting tools</li>
              <li>Automated control and optimization strategies</li>
              <li>Historical data tracking and trend analysis</li>
              <li>Fault detection and diagnostic capabilities</li>
              <li>Energy saving reports and sustainability metrics</li>
            </ul>

            <h3 class="text-lg font-medium mt-6">Getting Started</h3>
            <p>
              To get started with the Alto CERO, follow these basic steps:
            </p>
            <ol class="list-decimal pl-6 space-y-2">
              <li>
                <strong>Login to the system</strong> using your provided credentials. If you don't have access yet, 
                contact your system administrator.
              </li>
              <li>
                <strong>Navigate the interface</strong> using the sidebar menu on the left side of the screen, which 
                provides access to all major features.
              </li>
              <li>
                <strong>Explore the dashboard</strong> to get a quick overview of your system's current status and 
                performance metrics.
              </li>
              <li>
                <strong>Customize your views</strong> according to your specific needs and responsibilities.
              </li>
            </ol>

            <h3 class="text-lg font-medium mt-6">Navigation Guide</h3>
            <p>
              The Alto CERO interface is designed to be intuitive and easy to navigate. The main components are:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li>
                <strong>Top Header</strong>: Contains the date and time, weather information, user menu, and access to this manual.
              </li>
              <li>
                <strong>Left Sidebar</strong>: The main navigation menu with icons for each major section of the application.
              </li>
              <li>
                <strong>Main Content Area</strong>: Displays the active section's content and controls.
              </li>
              <li>
                <strong>Footer</strong>: Shows system information and version details.
              </li>
            </ul>
            <p class="mt-4">
              This manual is organized to follow the same structure as the main navigation menu, making it easy to 
              find information about specific features.
            </p>
          </div>
        `,
        th: `
          <div class="space-y-4">
            <p>
              ยินดีต้อนรับสู่คู่มือการใช้งาน Alto CERO คู่มือนี้ให้ข้อมูลที่ครอบคลุมเกี่ยวกับวิธีการใช้และจัดการระบบพลังงานของอาคารของคุณอย่างมีประสิทธิภาพด้วยแพลตฟอร์มของเรา
            </p>
            <p>
              Alto CERO ออกแบบมาเพื่อให้คุณมีการควบคุมและการมองเห็นการใช้พลังงานของอาคารอย่างสมบูรณ์ ช่วยให้คุณเพิ่มประสิทธิภาพการดำเนินงาน ลดต้นทุน และปรับปรุงความยั่งยืน
            </p>
            
            <h3 class="text-lg font-medium mt-4">คุณสมบัติหลัก</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li>การตรวจสอบการใช้พลังงานและประสิทธิภาพของระบบแบบเรียลไทม์</li>
              <li>เครื่องมือวิเคราะห์และรายงานขั้นสูง</li>
              <li>กลยุทธ์การควบคุมและการเพิ่มประสิทธิภาพอัตโนมัติ</li>
              <li>การติดตามข้อมูลในอดีตและการวิเคราะห์แนวโน้ม</li>
              <li>ความสามารถในการตรวจจับและวินิจฉัยข้อผิดพลาด</li>
              <li>รายงานการประหยัดพลังงานและตัวชี้วัดความยั่งยืน</li>
            </ul>

            <h3 class="text-lg font-medium mt-6">เริ่มต้นใช้งาน</h3>
            <p>
              เริ่มต้นใช้งาน Alto CERO ด้วยขั้นตอนพื้นฐานเหล่านี้:
            </p>
            <ol class="list-decimal pl-6 space-y-2">
              <li>
                <strong>เข้าสู่ระบบ</strong> โดยใช้ข้อมูลประจำตัวที่ให้ไว้ หากคุณยังไม่มีสิทธิ์เข้าถึง โปรดติดต่อผู้ดูแลระบบของคุณ
              </li>
              <li>
                <strong>นำทางอินเทอร์เฟซ</strong> โดยใช้เมนูแถบด้านข้างทางด้านซ้ายของหน้าจอ ซึ่งให้การเข้าถึงคุณลักษณะหลักทั้งหมด
              </li>
              <li>
                <strong>สำรวจแดชบอร์ด</strong> เพื่อรับภาพรวมอย่างรวดเร็วของสถานะปัจจุบันของระบบและเมตริกประสิทธิภาพ
              </li>
              <li>
                <strong>ปรับแต่งมุมมองของคุณ</strong> ตามความต้องการและความรับผิดชอบเฉพาะของคุณ
              </li>
            </ol>

            <h3 class="text-lg font-medium mt-6">คู่มือการนำทาง</h3>
            <p>
              อินเทอร์เฟซ Alto CERO ได้รับการออกแบบให้เข้าใจง่ายและนำทางได้ง่าย ส่วนประกอบหลักคือ:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li>
                <strong>ส่วนหัวด้านบน</strong>: มีวันที่และเวลา ข้อมูลสภาพอากาศ เมนูผู้ใช้ และการเข้าถึงคู่มือนี้
              </li>
              <li>
                <strong>แถบด้านข้างซ้าย</strong>: เมนูนำทางหลักพร้อมไอคอนสำหรับแต่ละส่วนหลักของแอปพลิเคชัน
              </li>
              <li>
                <strong>พื้นที่เนื้อหาหลัก</strong>: แสดงเนื้อหาและการควบคุมของส่วนที่ใช้งานอยู่
              </li>
              <li>
                <strong>ส่วนท้าย</strong>: แสดงข้อมูลระบบและรายละเอียดเวอร์ชัน
              </li>
            </ul>
            <p class="mt-4">
              คู่มือนี้จัดระเบียบตามโครงสร้างเดียวกับเมนูนำทางหลัก ทำให้ง่ายต่อการค้นหาข้อมูลเกี่ยวกับคุณลักษณะเฉพาะ
            </p>
          </div>
        `
      }
    }
  ]
}; 