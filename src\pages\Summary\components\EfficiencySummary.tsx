import React from "react";
import SystemSummary from "./SystemSummary";

interface EfficiencySummaryProps {
  plantEfficiency: number;
  airEfficiency: number;
  totalEfficiency?: number;
}

const EfficiencySummary: React.FC<EfficiencySummaryProps> = ({
  plantEfficiency = 0,
  airEfficiency = 0,
  totalEfficiency,
}) => {
  return (
    <SystemSummary
      title="Efficiency"
      airSideValue={airEfficiency}
      plantValue={plantEfficiency}
      totalValue={totalEfficiency}
      unit="kW/RT"
      decimalPlaces={3}
    />
  );
};

export default EfficiencySummary;
