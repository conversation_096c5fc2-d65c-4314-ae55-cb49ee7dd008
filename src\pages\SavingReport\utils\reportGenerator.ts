import { DataRow, ReportingDataRow, Regression<PERSON><PERSON>ult, DateRange, SummaryStats, VariableSelection } from '../types';

// Generate reporting data using the baseline model
export const generateReportingData = (
  data: DataRow[],
  regressionResult: RegressionResult,
  variableSelection?: VariableSelection
): ReportingDataRow[] => {
  // First, determine the dependent variable
  let dependentVar = variableSelection?.dependent || '';
  
  // If dependent variable not set in variableSelection or it doesn't exist in data,
  // try to infer it by finding variables that aren't in the coefficient list (except intercept)
  if (!dependentVar || !data[0]?.[dependentVar]) {
    const coefficientVars = new Set(Object.keys(regressionResult.coefficients).filter(key => key !== 'intercept'));
    
    // First, check the standard energy fields
    const energyFields = ['plant_energy', 'total_energy', 'airside_energy', 'cooling_load'];
    for (const field of energyFields) {
      if (data[0]?.[field] !== undefined && !coefficientVars.has(field)) {
        dependentVar = field;
        break;
      }
    }
    
    // If still not found, look for any numerical field not in coefficients
    if (!dependentVar && data[0]) {
      for (const key in data[0]) {
        if (
          key !== 'id' && 
          key !== 'date' && 
          !coefficientVars.has(key) &&
          typeof data[0][key] === 'number'
        ) {
          dependentVar = key;
          break;
        }
      }
    }
    
    // If still no dependent variable found, use plant_energy as default
    if (!dependentVar) {
      dependentVar = 'plant_energy';
    }
  }
  
  return data.map((row, index) => {
    // Extract predictors for the regression model
    const predictors: { [key: string]: number } = {};
    Object.keys(regressionResult.coefficients)
      .filter(key => key !== 'intercept')
      .forEach(variable => {
        predictors[variable] = row[variable];
      });
    
    // Calculate baseline prediction using the fit model from baseline page
    const baseline_kwh = regressionResult.predictionFunction(predictors);
    
    // Use the identified dependent variable; if missing, use 0
    const actual_kwh = row[dependentVar] !== undefined ? row[dependentVar] : 0;
    
    // Calculate savings (baseline - actual)
    const savings_kwh = baseline_kwh - actual_kwh;
    
    // Calculate savings percentage (ensure we don't divide by zero)
    const savings_percent = baseline_kwh !== 0 ? (savings_kwh / baseline_kwh) * 100 : 0;
    
    return {
      ...row,
      id: row.id || index + 1,
      baseline_kwh,
      actual_kwh,
      savings_kwh,
      savings_percent
    };
  });
};

// Calculate summary statistics for the reporting period
export const calculateSummaryStats = (
  reportingData: ReportingDataRow[],
  dateRange: DateRange,
  regressionResult?: RegressionResult | null,
  baselineData?: DataRow[],
  variableSelection?: VariableSelection
): SummaryStats => {
  // Use all data if no specific range is provided
  const filteredData = dateRange.start && dateRange.end 
    ? reportingData.filter(row => {
        const rowDate = new Date(row.date);
        const startDate = new Date(dateRange.start);
        const endDate = new Date(dateRange.end);
        return rowDate >= startDate && rowDate <= endDate;
      })
    : reportingData;
  
  // If there's no data after filtering, use all reporting data
  const dataToUse = filteredData.length > 0 ? filteredData : reportingData;
  
  // Sort data by date
  const sortedData = [...dataToUse].sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  // Helper function to get days in month
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Helper function to check if a date is a weekday
  const isWeekday = (date: Date): boolean => {
    const day = date.getDay();
    return day !== 0 && day !== 6; // 0 is Sunday, 6 is Saturday
  };

  // Helper function to count days in date range considering weekday filter
  const countDaysInRange = (start: Date, end: Date, weekdayOnly: boolean): number => {
    let count = 0;
    const currentDate = new Date(start);
    currentDate.setHours(0, 0, 0, 0);
    const endDate = new Date(end);
    endDate.setHours(23, 59, 59, 999);

    while (currentDate <= endDate) {
      if (!weekdayOnly || isWeekday(currentDate)) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return count;
  };

  // Determine if we're only looking at weekdays
  const isWeekdayOnly = sortedData.every(row => isWeekday(new Date(row.date)));

  // Calculate total reporting days using the same logic as monthly breakdown
  const startDate = new Date(sortedData[0]?.date || dateRange.start || '');
  const endDate = new Date(sortedData[sortedData.length - 1]?.date || dateRange.end || '');
  const reportingDays = startDate && endDate ? countDaysInRange(startDate, endDate, isWeekdayOnly) : 0;
  
  // Calculate total values
  const totalBaselineConsumption = sortedData.reduce((sum, row) => sum + row.baseline_kwh, 0);
  const totalActualConsumption = sortedData.reduce((sum, row) => sum + row.actual_kwh, 0);
  
  // Calculate total savings (baseline - actual)
  const totalSaving = totalBaselineConsumption - totalActualConsumption;
  
  // Calculate savings percentage (ensure we don't divide by zero)
  const savingPercentage = totalBaselineConsumption !== 0 
    ? (totalSaving / totalBaselineConsumption) * 100 
    : 0;

  // Group data by month
  const monthlyData: { [key: string]: ReportingDataRow[] } = {};
  const monthRanges: { [key: string]: { start: Date; end: Date } } = {};
  
  sortedData.forEach(row => {
    const date = new Date(row.date);
    const monthYear = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
    
    if (!monthlyData[monthYear]) {
      monthlyData[monthYear] = [];
      // For the first and last month, use the actual start/end dates from the reporting period
      const isFirstMonth = date.getMonth() === startDate.getMonth() && date.getFullYear() === startDate.getFullYear();
      const isLastMonth = date.getMonth() === endDate.getMonth() && date.getFullYear() === endDate.getFullYear();
      
      monthRanges[monthYear] = {
        start: isFirstMonth ? startDate : new Date(date.getFullYear(), date.getMonth(), 1),
        end: isLastMonth ? endDate : new Date(date.getFullYear(), date.getMonth() + 1, 0)
      };
    }
    
    monthlyData[monthYear].push(row);
  });
  
  // Calculate monthly breakdown
  const monthlyBreakdown = Object.entries(monthlyData).map(([month, rows]) => {
    // Count only the days present in the data for this month, respecting isWeekdayOnly
    const uniqueDays = new Set<string>();
    rows.forEach(row => {
        const date = new Date(row.date);
        // The isWeekdayOnly flag is determined once for the whole dataset.
        // If it's true, we only count weekdays. If false, we count all days present.
        if (!isWeekdayOnly || isWeekday(date)) {
            // Use ISO date string (YYYY-MM-DD) to ensure uniqueness per day
            uniqueDays.add(date.toISOString().split('T')[0]);
        }
    });
    const days = uniqueDays.size; // This now represents the number of actual data days for the month

    // Calculate daily averages first
    // Note: Using the new 'days' count based on unique days with data.
    const baselineKwh = rows.reduce((sum, row) => sum + row.baseline_kwh, 0);
    const actualKwh = rows.reduce((sum, row) => sum + row.actual_kwh, 0);
    
    // Calculate savings (baseline - actual)
    const savingsKwh = baselineKwh - actualKwh;
    
    // Calculate savings percentage (ensure we don't divide by zero)
    const savingsPercent = baselineKwh !== 0 
      ? (savingsKwh / baselineKwh) * 100 
      : 0;
    
    // Calculate baseline uncertainty from Standard Error / Mean Daily Consumption
    const modelSE = regressionResult?.statistics?.rse || 0;
    
    // Calculate mean daily consumption using actual days in the period
    const avgDailyBaseline = days > 0 ? baselineKwh / days : 0;
    
    // Baseline uncertainty is Standard Error / Mean Daily Consumption (CV)
    const baselineUncertainty = avgDailyBaseline > 0 ? 
      (modelSE / avgDailyBaseline) : 
      0.0469; // Default to 4.69% if calculation not possible
    
    const meterUncertainty = 0.0125; // 1.25% default meter uncertainty
    const combinedUncertainty = Math.sqrt(Math.pow(baselineUncertainty, 2) + Math.pow(meterUncertainty, 2));
    
    // Daily uncertainty calculation
    const errorKwhPerDay = avgDailyBaseline * combinedUncertainty;
    const confidenceLevel = 1.96; // 95% confidence level
    const dailyUncertainty = errorKwhPerDay * confidenceLevel;
    
    // Monthly uncertainty (adjusted for sample size using the new 'days')
    const uncertaintyKwh = days > 0 ? dailyUncertainty * Math.sqrt(days) : 0;
    
    // Percentage uncertainty (ensure we don't divide by zero)
    const uncertaintyPercent = baselineKwh !== 0 
      ? (uncertaintyKwh / baselineKwh) * 100 
      : 0;
    
    return {
      month,
      days,
      baselineKwh,
      actualKwh,
      savingsKwh,
      uncertaintyKwh,
      savingsPercent,
      uncertaintyPercent
    };
  });
  
  return {
    reportingDays,
    reportingPeriod: {
      start: dateRange.start || sortedData[0]?.date || '',
      end: dateRange.end || sortedData[sortedData.length - 1]?.date || ''
    },
    totalBaselineConsumption,
    totalActualConsumption,
    totalSaving,
    savingPercentage,
    monthlyBreakdown
  };
};