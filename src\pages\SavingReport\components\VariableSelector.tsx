import React from 'react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface VariableSelectorProps {
  disabled?: boolean;
}

const VariableSelector: React.FC<VariableSelectorProps> = ({ disabled = false }) => {
  const { baselineData, variableSelection, setVariableSelection } = useSavingDashboard();

  if (baselineData.length === 0) {
    return null;
  }

  // Get all available columns from the first data row
  const availableColumns = Object.keys(baselineData[0]).filter(
    col => col !== 'id' && col !== 'date'
  );

  const handleDependentChange = (value: string) => {
    if (disabled) return;
    
    setVariableSelection({
      ...variableSelection,
      dependent: value
    });
  };

  const handleIndependentChange = (value: string, checked: boolean) => {
    if (disabled) return;
    
    if (checked) {
      setVariableSelection({
        ...variableSelection,
        independent: [...variableSelection.independent, value]
      });
    } else {
      setVariableSelection({
        ...variableSelection,
        independent: variableSelection.independent.filter(v => v !== value)
      });
    }
  };

  return (
    <div className="flex flex-col space-y-2">
      <div>
        <Label htmlFor="dependent-var" className="text-xs">Dependent Variable (y)</Label>
        <Select 
          value={variableSelection.dependent} 
          onValueChange={handleDependentChange}
          disabled={disabled}
        >
          <SelectTrigger id="dependent-var" className="h-9 text-sm">
            <SelectValue placeholder="Select variable" />
          </SelectTrigger>
          <SelectContent>
            {availableColumns.map(column => (
              <SelectItem key={column} value={column}>
                {column}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <Label className="text-xs mb-1 block">Independent Variables (x)</Label>
        <div className="grid grid-cols-2 gap-2">
          {availableColumns.map(column => (
            <div key={column} className="flex items-center">
              <input
                type="checkbox"
                id={`var-${column}`}
                checked={variableSelection.independent.includes(column)}
                onChange={(e) => handleIndependentChange(column, e.target.checked)}
                disabled={disabled || column === variableSelection.dependent}
                className="h-3 w-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor={`var-${column}`} className="ml-1.5 block text-xs text-gray-900">
                {column}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VariableSelector;