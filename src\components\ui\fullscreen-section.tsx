import React, { useState, useEffect } from 'react';
import { Maximize2, Minimize2 } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';

interface FullscreenSectionProps {
  children: React.ReactNode | ((isFullscreen: boolean) => React.ReactNode);
  title: React.ReactNode;
  className?: string;
  contentClassName?: string;
  headerContent?: React.ReactNode;
}

const FullscreenSection: React.FC<FullscreenSectionProps> = ({
  children,
  title,
  className,
  contentClassName,
  headerContent
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    // Trigger window resize event to help charts redraw
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
  };

  // Add effect to handle resize and ESC key for exiting fullscreen
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
        setTimeout(() => {
          window.dispatchEvent(new Event('resize'));
        }, 100);
      }
    };
    
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [isFullscreen]);

  // Render children, supporting both direct ReactNode and function pattern
  const renderChildren = () => {
    return typeof children === 'function' ? children(isFullscreen) : children;
  };

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 flex flex-col bg-white p-4 overflow-hidden">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-semibold">{title}</h2>
          <div className="flex items-center gap-2">
            {headerContent}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={toggleFullscreen}
              className="h-8 w-8 p-0 ml-2"
            >
              <Minimize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className={cn("flex-1 w-full h-[calc(100vh-70px)] overflow-auto", contentClassName)}>
          {renderChildren()}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("bg-white rounded-lg border p-3 overflow-auto relative flex flex-col h-full", className)}>
      <div className="flex items-center justify-between mb-2">
        <h2 className="text-sm font-semibold">{title}</h2>
        <div className="flex items-center gap-2">
          {headerContent}
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={toggleFullscreen}
            className="h-6 w-6 p-0 ml-2"
          >
            <Maximize2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
      <div className={cn("flex-1 min-h-0 h-[1000px] w-full", contentClassName)}>
        {renderChildren()}
      </div>
    </div>
  );
};

export default FullscreenSection; 