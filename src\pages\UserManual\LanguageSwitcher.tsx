import React from 'react';
import { useLanguage } from './LanguageContext';
import { Languages } from 'lucide-react';

const LanguageSwitcher: React.FC = () => {
  const { language, setLanguage } = useLanguage();

  return (
    <div className="flex items-center space-x-2 justify-end mb-4">
      <Languages size={16} className="text-gray-500 mr-1" />
      <div className="flex rounded-md overflow-hidden border border-gray-200 shadow-sm">
        <button
          className={`px-3 py-1 text-sm font-medium transition-colors ${
            language === 'en'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
          onClick={() => setLanguage('en')}
        >
          EN
        </button>
        <button
          className={`px-3 py-1 text-sm font-medium transition-colors ${
            language === 'th'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
          onClick={() => setLanguage('th')}
        >
          TH
        </button>
      </div>
    </div>
  );
};

export default LanguageSwitcher; 