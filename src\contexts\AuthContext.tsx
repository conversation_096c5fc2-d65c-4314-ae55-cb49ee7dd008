import React, { createContext, useContext, useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { login, logout, isAuthenticated, getSiteId, getUserInfo, getUserRole, setUserRole, isSuperUser } from '../services/authService';

interface User {
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role?: 'admin' | 'operator' | 'viewer';
  sites?: Array<{
    siteId: string;
    siteName: string;
    role: string;
    isPrimary: boolean;
  }>;
  isSuperuser?: boolean;
}

interface Site {
  id: string;
  name?: string;
  timezone?: string;
}

interface AuthContextType {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  site: Site | null;
  userRole: string | null;
  isSuperuser: boolean;
  hasRole: (requiredRole: string | string[]) => boolean;
  fetchUserInfo: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  currentUser: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  logout: () => {},
  site: null,
  userRole: null,
  isSuperuser: false,
  hasRole: () => false,
  fetchUserInfo: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isAuth, setIsAuth] = useState(isAuthenticated());
  const [isLoading, setIsLoading] = useState(true);
  const [siteData, setSiteData] = useState<{id: string; name?: string; timezone?: string;} | null>(null);
  const [userRole, setStateUserRole] = useState<string | null>(getUserRole());
  const [isSuperuser, setIsSuperuser] = useState<boolean>(isSuperUser());
  
  // References to track API call status and throttle requests
  const lastFetchTimestamp = useRef<number>(0);
  const fetchInProgress = useRef<boolean>(false);
  const MIN_FETCH_INTERVAL = 5000; // 5 seconds minimum between fetch calls

  // Memoize the site object to stabilize references
  const site = useMemo(() => siteData, [siteData]);

  const fetchUserInfo = useCallback(async (): Promise<void> => {
    // Skip if not authenticated
    if (!isAuth) return;
    
    // Skip if a fetch is already in progress
    if (fetchInProgress.current) {
      console.log('Fetch already in progress, skipping duplicate request');
      return;
    }
    
    // Check if we've fetched recently
    const now = Date.now();
    if (now - lastFetchTimestamp.current < MIN_FETCH_INTERVAL) {
      console.log('Fetch request throttled: too soon since last fetch');
      return;
    }
    
    try {
      // Mark fetch as in progress
      fetchInProgress.current = true;
      lastFetchTimestamp.current = now;
      
      const userInfo = await getUserInfo();
      
      // Explicitly check and set superuser status first
      const superuserStatus = !!userInfo.is_superuser;
      setIsSuperuser(superuserStatus);
      
      // Map backend data to frontend user model
      setCurrentUser({
        email: userInfo.email,
        username: userInfo.username,
        firstName: userInfo.first_name,
        lastName: userInfo.last_name,
        role: userInfo.primary_site_role,
        sites: userInfo.sites?.map(site => ({
          siteId: site.site_id,
          siteName: site.site_name,
          role: site.role,
          isPrimary: site.is_primary
        })),
        isSuperuser: superuserStatus
      });
      
      // Store role in localStorage and update state
      if (userInfo.primary_site_role) {
        setUserRole(userInfo.primary_site_role);
        setStateUserRole(userInfo.primary_site_role);
      }
      
      // Update site information
      if (userInfo.primary_site) {
        const primarySite = userInfo.sites?.find(site => site.is_primary);
        
        setSiteData({ 
          id: userInfo.primary_site,
          name: primarySite?.site_name,
          timezone: userInfo.timezone || 'Asia/Bangkok' // Use timezone from backend or default
        });
        
        // Also store site_id in localStorage
        localStorage.setItem('site_id', userInfo.primary_site);
      }
      
    } catch (error: any) {
      console.error('Error fetching user info:', error);
      throw error;
    } finally {
      // Reset fetch in progress flag
      fetchInProgress.current = false;
    }
  }, [isAuth]);

  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);
      setIsAuth(isAuthenticated());
      
      const siteId = getSiteId();
      if (siteId) {
        setSiteData({ id: siteId, timezone: 'Asia/Bangkok' }); // Default timezone until user info is fetched
      }
      
      // Fetch user info on initial load if authenticated
      if (isAuthenticated()) {
        try {
          await fetchUserInfo();
        } catch (error: any) {
          console.error('Error initializing auth context:', error);
          // If we can't fetch user info, user might need to login again
          if (error.response?.status === 401) {
            handleLogout();
          }
        }
      }
      
      setIsLoading(false);
    };
    
    initializeAuth();
  }, [fetchUserInfo]);

  const handleLogin = async (username: string, password: string) => {
    try {
      const loginData = await login(username, password);
      setIsAuth(true);
      setCurrentUser({ 
        email: loginData.user.email,
        username: loginData.user.username 
      });
      
      if (loginData.user.primary_site) {
        setSiteData({ id: loginData.user.primary_site, timezone: 'Asia/Bangkok' }); // Default timezone until user info is fetched
      }
      
      // Fetch additional user info after login
      await fetchUserInfo();
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const handleLogout = () => {
    logout();
    setIsAuth(false);
    setCurrentUser(null);
    setSiteData(null);
    setStateUserRole(null);
  };
  
  // Function to check if user has required role(s) or is a superuser
  const hasRole = (requiredRole: string | string[]): boolean => {
    // Superusers have all permissions
    if (isSuperuser) return true;
    
    if (!userRole) return false;
    
    if (Array.isArray(requiredRole)) {
      const hasRequiredRole = requiredRole.includes(userRole);
      return hasRequiredRole;
    }
    
    const roleMatches = userRole === requiredRole;
    return roleMatches;
  };

  return (
    <AuthContext.Provider
      value={{
        currentUser,
        isAuthenticated: isAuth,
        isLoading,
        login: handleLogin,
        logout: handleLogout,
        site,
        userRole,
        isSuperuser,
        hasRole,
        fetchUserInfo,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};