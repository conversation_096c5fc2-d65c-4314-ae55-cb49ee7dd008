import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Loader } from "@/components/ui/loader";
import {
  getDevicesWithZones,
  DeviceData,
  GroupsData,
} from "@/services/deviceService";
import { useRealtime } from "@/contexts/RealtimeContext";

const AirSideHeatMap = ({
  rowsPerColumn = 10, // How many rows fit in each column before creating a new column
}) => {
  const { getValue } = useRealtime();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [groupsData, setGroupsData] = useState<GroupsData>({});
  const [groupNames, setGroupNames] = useState<string[]>([]);

  // Status codes and their corresponding colors
  const statusColors = {
    normal: "bg-success", // hsl(178, 80%, 40%)
    off: "bg-muted/20", // light gray
    warning: "bg-warning", // hsl(42, 99%, 66%)
    alarm: "bg-destructive", // hsl(4, 85%, 57%)
  };

  // Temperature thresholds for determining status
  const tempThresholds = {
    ahu: {
      warning: { min: 25, max: 28 },
      alarm: { min: 28, max: 32 },
    },
    vav: {
      warning: { min: 25, max: 28 },
      alarm: { min: 28, max: 32 },
    },
  };

  // Determine device status based on temperature value and thresholds
  const determineDeviceStatus = (
    deviceId: string,
    deviceType: "ahu" | "vav"
  ): "normal" | "off" | "warning" | "alarm" => {
    // Check device on/off status first
    const statusValue = getValue(deviceId, "status_write");

    // If status_write is 0 or undefined, device is off
    if (statusValue === 0 || statusValue === undefined) {
      return "off";
    }

    // Get temperature value based on device type
    let tempValue: number | undefined;
    if (deviceType === "ahu") {
      tempValue = getValue(deviceId, "supply_air_temperature_read");
    } else {
      tempValue = getValue(deviceId, "room_temperature");
    }

    // If temperature value is undefined, return normal as default
    if (tempValue === undefined) {
      return "normal";
    }

    // Get thresholds based on device type
    const thresholds =
      deviceType === "ahu" ? tempThresholds.ahu : tempThresholds.vav;

    // Check against thresholds
    if (tempValue >= thresholds.alarm.min) {
      return "alarm";
    } else if (tempValue >= thresholds.warning.min) {
      return "warning";
    } else {
      return "normal";
    }
  };

  // Helper to determine device type
  const getDeviceType = (device: DeviceData): "ahu" | "vav" => {
    const model = device.model.toLowerCase();
    return model === "ahu" || model === "vsd_ahu" ? "ahu" : "vav";
  };

  // Fetch devices data from API
  useEffect(() => {
    const fetchDevices = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Use the service to fetch and process data
        const processedData = await getDevicesWithZones();

        // Filter only Air-side devices (AHUs and VAVs)
        const filteredData: GroupsData = {};

        for (const [key, devices] of Object.entries(
          processedData.groupedData
        )) {
          filteredData[key] = devices.filter(
            (device) =>
              device.model.toLowerCase() === "ahu" ||
              device.model.toLowerCase() === "vav"
          );
        }

        setGroupsData(filteredData);
        setGroupNames(processedData.floorNames);
      } catch (err) {
        console.error("Error fetching device data:", err);
        setError("Failed to fetch device data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDevices();
  }, []);

  // Helper to render a single group and its devices
  const renderGroup = (
    groupId: string,
    groupData: DeviceData[],
    groupIndex: number
  ) => {
    const cellsPerGroup = groupData.length;
    // Calculate columns layout
    const fullColumns = Math.floor(cellsPerGroup / rowsPerColumn);
    const remainingCells = cellsPerGroup % rowsPerColumn;
    const totalColumns = fullColumns + (remainingCells > 0 ? 1 : 0);

    return (
      <div className="flex-shrink-0 mr-4" key={groupId}>
        <div className="text-xs text-gray-700 font-medium mb-1 whitespace-nowrap">
          {groupNames[groupIndex] || `Group ${groupIndex + 1}`}
        </div>
        <div className="flex">
          {/* Render columns for this group */}
          {Array(totalColumns)
            .fill(null)
            .map((_, colIndex) => {
              const startIndex = colIndex * rowsPerColumn;
              const endIndex = Math.min(
                startIndex + rowsPerColumn,
                cellsPerGroup
              );
              const columnDevices = groupData.slice(startIndex, endIndex);

              return (
                <div
                  className="flex flex-col mr-0.5"
                  key={`${groupId}-col-${colIndex}`}
                >
                  {columnDevices.map(
                    (device: DeviceData, deviceIndex: number) => {
                      // Determine device type and status based on temperature
                      const deviceType = getDeviceType(device);
                      const status = determineDeviceStatus(
                        device.deviceId,
                        deviceType
                      );

                      // Get temperature value for display in tooltip
                      const tempField =
                        deviceType === "ahu"
                          ? "supply_air_temperature_read"
                          : "room_temperature";
                      const tempValue = getValue(device.deviceId, tempField);

                      return (
                        <TooltipProvider
                          key={`${groupId}-device-${startIndex + deviceIndex}`}
                        >
                          <Tooltip delayDuration={50}>
                            <TooltipTrigger asChild>
                              <div
                                className={`w-3 h-3 rounded-[3px] mb-0.5 cursor-pointer shadow-sm transition-all hover:shadow-md ${statusColors[status]}`}
                              />
                            </TooltipTrigger>
                            <TooltipContent className="bg-white/90 backdrop-blur-sm border border-gray-100 shadow-lg rounded-lg p-3">
                              <div className="text-sm">
                                <div className="font-bold text-gray-800">
                                  {device.deviceName
                                    .replace(/_/g, "-")
                                    .toUpperCase()}
                                </div>
                                <div className="text-gray-600">
                                  Floor {device.floor}, Zone {device.zone}, Unit{" "}
                                  {device.unit}
                                </div>
                                <div className="flex items-center">
                                  <span
                                    className={`inline-block w-2 h-2 rounded-full mr-2 ${statusColors[status]}`}
                                  ></span>
                                  <span className="capitalize text-gray-700">
                                    {device.autopilotStatus}
                                  </span>
                                </div>
                                {tempValue !== undefined && (
                                  <div className="mt-1">
                                    <span className="text-gray-600">
                                      Temperature:{" "}
                                    </span>
                                    <span className="font-medium">
                                      {tempValue?.toFixed(1)}°C
                                    </span>
                                  </div>
                                )}
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      );
                    }
                  )}
                </div>
              );
            })}
        </div>
      </div>
    );
  };

  // Render only floors that have devices
  const renderGroups = () => {
    return Object.entries(groupsData)
      .filter(([_, groupData]) => groupData.length > 0) // Skip empty groups
      .map(([groupId, groupData], index) =>
        renderGroup(groupId, groupData, index)
      );
  };

  if (isLoading) {
    return (
      <div className="p-4 flex justify-center items-center h-[40vh]">
        <Loader className="h-8 w-8" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-destructive">
        <p>Error: {error}</p>
      </div>
    );
  }

  // If no data
  if (Object.keys(groupsData).length === 0) {
    return (
      <div className="p-4 text-muted-foreground">
        <p>No device data available.</p>
      </div>
    );
  }

  return (
    <div className="p-4 relative">
      <div className="flex flex-nowrap overflow-x-auto max-h-[40vh] pb-2">
        {renderGroups()}
      </div>

      {/* Legend */}
      <div className="mt-4 flex flex-wrap gap-4">
        <div className="flex items-center">
          <div className={`w-3 h-3 ${statusColors.off} rounded mr-2`}></div>
          <span className="text-xs">Off</span>
        </div>
        <div className="flex items-center">
          <div className={`w-3 h-3 ${statusColors.normal} rounded mr-2`}></div>
          <span className="text-xs">Normal (≤25°C)</span>
        </div>
        <div className="flex items-center">
          <div className={`w-3 h-3 ${statusColors.warning} rounded mr-2`}></div>
          <span className="text-xs">Warning (25°C - 28°C)</span>
        </div>
        <div className="flex items-center">
          <div className={`w-3 h-3 ${statusColors.alarm} rounded mr-2`}></div>
          <span className="text-xs">Alarm (≥28°C)</span>
        </div>
      </div>
    </div>
  );
};

export default AirSideHeatMap;
