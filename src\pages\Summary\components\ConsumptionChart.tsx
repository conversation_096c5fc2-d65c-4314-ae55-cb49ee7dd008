import React, { useEffect, useRef, useState, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import * as echarts from "echarts";
import { fetchAggregatedData } from "@/services/timescaleService";
import { useAuth } from "@/contexts/AuthContext";
import { Loader } from "@/components/ui/loader";
import { DateTime } from "luxon";

// Define the interface for the transformed data structure
interface TransformedData {
  bucket: string;
  avg_value: number;
  site_id: string;
  device_id: string;
  model: string;
  datapoint: string;
}

interface ConsumptionChartProps {
  siteId?: string;
}

const ConsumptionChart: React.FC<ConsumptionChartProps> = ({ siteId }) => {
  const { site } = useAuth();
  
  // Get timezone once at component level
  const timezone = site?.timezone || 'Asia/Bangkok';
  
  // Memoize the siteId to prevent unnecessary re-renders
  const siteIdToUse = useMemo(() => siteId || site?.id, [siteId, site?.id]);
  
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<echarts.ECharts | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"power" | "efficiency">("power");
  const [chartData, setChartData] = useState<{
    power: {
      plant: TransformedData[],
      airDistribution: TransformedData[],
    },
    efficiency: {
      plant: TransformedData[],
      airDistribution: TransformedData[],
    }
  }>({
    power: { plant: [], airDistribution: [] },
    efficiency: { plant: [], airDistribution: [] }
  });

  // Initialize chart and plot data
  const renderChart = () => {
    if (!chartRef.current) return;

    // Create or recreate chart instance
    if (chartInstanceRef.current) {
      chartInstanceRef.current.dispose();
    }
    
    if (chartRef.current.clientWidth === 0 || chartRef.current.clientHeight === 0) {
      // Try again when container has dimensions
      setTimeout(renderChart, 50);
      return;
    }

    // Initialize chart
    const chartInstance = echarts.init(chartRef.current);
    chartInstanceRef.current = chartInstance;
    
    // Get date range for today using Luxon with timezone
    const startDatetime = DateTime.now().setZone(timezone).startOf('day');
    const endDateTime = startDatetime.plus({ days: 1 });

    // Get data for the active tab
    const activeData = chartData[activeTab];
    
    // Transform data for chart with timezone awareness
    const transformData = (data: TransformedData[]) => {
      return data.map(item => [
        DateTime.fromISO(item.bucket).setZone(timezone).toMillis(),
        item.avg_value
      ]);
    };

    const plantChartData = transformData(activeData.plant);
    const airChartData = transformData(activeData.airDistribution);
    
    // Calculate Total HVAC System as sum of Plant and Air Distribution
    const plantDataMap = new Map(plantChartData.map(item => [item[0], item[1]]));
    const airDataMap = new Map(airChartData.map(item => [item[0], item[1]]));
    
    // Get all unique timestamps
    const allTimestamps = Array.from(new Set([
      ...plantChartData.map(item => item[0]),
      ...airChartData.map(item => item[0])
    ])).sort();
    
    // Create the total data by summing values at each timestamp
    const hvacChartData = allTimestamps.map(timestamp => {
      const plantValue = plantDataMap.get(timestamp) || 0;
      const airValue = airDataMap.get(timestamp) || 0;
      return [timestamp, plantValue + airValue];
    });

    // Use default empty data if we don't have real data yet
    const hasData = hvacChartData.length > 0;
    
    // Create empty data arrays for initial/empty display
    const emptyData = Array.from({ length: 24 }, (_, i) => [
      startDatetime.plus({ hours: i }).toMillis(),
      0
    ]);

    // Calculate y-axis settings
    let yAxisMax, yAxisInterval;
    
    if (hasData) {
      // Calculate max values for y-axis scaling
      const allValues = [
        ...hvacChartData.map(data => data[1]), 
        ...plantChartData.map(data => data[1]), 
        ...airChartData.map(data => data[1])
      ];
      const maxValue = Math.max(...allValues, 1);
      
      if (activeTab === "efficiency") {
        // Dynamic scaling for efficiency with 20% headroom, capped at 2
        const efficiencyMax = Math.min(Math.max(maxValue * 1.2, 1.0), 2.0);
        yAxisMax = Math.min(Math.ceil(efficiencyMax * 2) / 2, 2.0);
        yAxisInterval = 0.5; // Fixed interval for 0-2 range
      } else {
        // Power scaling with 20% headroom, capped at 2000
        const powerMax = Math.min(Math.ceil((maxValue * 1.2) / 100) * 100, 2000);
        yAxisMax = powerMax;
        
        if (powerMax <= 500) {
          yAxisInterval = 100; 
        } else if (powerMax <= 1000) {
          yAxisInterval = 200;
        } else {
          yAxisInterval = 400;
        }
      }
    } else {
      // Default values when no data
      yAxisMax = activeTab === "efficiency" ? 2 : 500;
      yAxisInterval = activeTab === "efficiency" ? 0.5 : 100;
    }

    // Set chart options
    chartInstance.setOption({
      tooltip: {
        trigger: "axis",
        formatter: function (params: any[]) {
          const date = DateTime.fromMillis(params[0].data[0]).setZone(timezone);
          const dateString = date.toLocaleString({
            day: "2-digit",
            month: "short",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });

          const unit = activeTab === "power" ? " kW" : " kW/RT";
          const tooltipItems = params
            .map((param: any) => {
              return `<span style="color: ${param.color}; font-size: 16px;">●</span> ${param.seriesName}: <b>${param.value[1].toFixed(activeTab === "power" ? 1 : 3)}${unit}</b>`;
            })
            .join("<br/>");
          return `${dateString}<br/>${tooltipItems}`;
        },
        backgroundColor: '#fff',
        borderColor: '#ddd',
        borderWidth: 1,
        textStyle: { color: '#333' }
      },
      legend: {
        data: [
          { name: "Total HVAC System", icon: 'circle' },
          { name: "Plant", icon: 'circle' },
          { name: "Air Distribution", icon: 'circle' }
        ],
        top: 0,
        right: 10,
        textStyle: {
          fontSize: 12,
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        top: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "time",
        splitNumber: 8,
        min: startDatetime.toISO(),
        max: endDateTime.toISO(),
        axisLabel: {
          formatter: function (params: number) {
            const date = DateTime.fromMillis(params).setZone(timezone);
            return String(date.hour).padStart(2, "0");
          },
          margin: 12,
          fontSize: 12,
          color: "#788796",
          interval: 2 * 3600 * 1000,
        },
        axisTick: { show: false },
        axisLine: {
          lineStyle: { color: '#ddd' }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#eee',
            type: 'solid'
          }
        }
      },
      yAxis: {
        type: "value",
        name: activeTab === "power" ? "Power (kW)" : "Efficiency (kW/RT)",
        min: 0,
        max: yAxisMax,
        interval: yAxisInterval,
        axisLabel: {
          fontSize: 12,
          formatter: (value: number) => activeTab === "power" ? value.toFixed(0) : value.toFixed(2),
        },
        nameTextStyle: {
          fontSize: 12,
          padding: [0, 0, 0, 0],
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#eee',
            type: 'solid'
          }
        }
      },
      series: [
        {
          name: "Total HVAC System",
          type: "line",
          showSymbol: false,
          symbolSize: 0,
          data: hasData ? hvacChartData : emptyData,
          color: "#3b82f6",
          lineStyle: {
            width: 2,
            type: "solid"
          },
          smooth: true,
          z: 2,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(59, 130, 246, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(59, 130, 246, 0.1)",
                },
              ],
            },
          },
        },
        {
          name: "Plant",
          type: "line",
          showSymbol: false,
          symbolSize: 0,
          data: hasData ? plantChartData : emptyData,
          color: "#ef4444",
          lineStyle: {
            width: 2,
            type: "solid"
          },
          smooth: true,
          z: 2,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(239, 68, 68, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(239, 68, 68, 0.1)",
                },
              ],
            },
          },
        },
        {
          name: "Air Distribution",
          type: "line",
          showSymbol: false,
          symbolSize: 0,
          data: hasData ? airChartData : emptyData,
          color: "#10b981",
          lineStyle: {
            width: 2,
            type: "solid"
          },
          smooth: true,
          z: 2,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(16, 185, 129, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(16, 185, 129, 0.1)",
                },
              ],
            },
          },
        }
      ],
    });
  };

  // When tab changes, just re-render the chart
  useEffect(() => {
    if (!loading && !error && chartRef.current) {
      renderChart();
    }
  }, [activeTab, chartData]);

  // Resize observer to detect when container is ready
  useEffect(() => {
    if (!chartRef.current) return;
    
    const resizeObserver = new ResizeObserver(() => {
      // Only initialize if we don't already have a chart and container has dimensions
      if (chartRef.current && 
          chartRef.current.clientWidth > 0 && 
          chartRef.current.clientHeight > 0 &&
          !loading && !error) {
        renderChart();
      }
    });
    
    resizeObserver.observe(chartRef.current);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, [loading, error]);

  // Main data fetching effect
  useEffect(() => {
    console.log('ConsumptionChart component mounted');
    
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize();
      }
    };

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const activeSiteId = siteIdToUse || '';
        
        // Use the timezone from component level
        
        // Calculate date range for today using Luxon with timezone awareness
        const startDate = DateTime.now().setZone(timezone).startOf('day');
        const endDate = startDate.plus({ days: 1 });
        
        // Fetch all data in parallel for better performance
        const [plantPowerResponse, airPowerResponse, plantEfficiencyResponse, airEfficiencyResponse] = 
          await Promise.all([
            // Power data
            fetchAggregatedData({
              site_id: activeSiteId,
              device_id: 'plant',
              datapoints: ['power'],
              start_timestamp: startDate.toString(),
              end_timestamp: endDate.toString(),
              resampling: '30min'
            }),
            fetchAggregatedData({
              site_id: activeSiteId,
              device_id: 'air_distribution_system',
              datapoints: ['power'],
              start_timestamp: startDate.toString(),
              end_timestamp: endDate.toString(),
              resampling: '30min'
            }),
            // Efficiency data
            fetchAggregatedData({
              site_id: activeSiteId,
              device_id: 'plant',
              datapoints: ['efficiency'],
              start_timestamp: startDate.toString(),
              end_timestamp: endDate.toString(),
              resampling: '30min'
            }),
            fetchAggregatedData({
              site_id: activeSiteId,
              device_id: 'air_distribution_system',
              datapoints: ['efficiency'],
              start_timestamp: startDate.toString(),
              end_timestamp: endDate.toString(),
              resampling: '30min'
            })
          ]);
        
        // Transform the data
        const transformResponseData = (response: any, datapoint: string) => {
          if (response.success && response.data) {
            // Find the data entry for the specific datapoint
            const dataEntry = response.data.find((item: any) => item.datapoint === datapoint);
            if (dataEntry && dataEntry.values) {
              return dataEntry.values.map((item: any) => ({
                bucket: item.timestamp,
                avg_value: item.value,
                site_id: dataEntry.site_id,
                device_id: dataEntry.device_id,
                model: dataEntry.model,
                datapoint: dataEntry.datapoint
              }));
            }
          }
          return [];
        };
        
        // Build new data object
        const newChartData = {
          power: {
            plant: transformResponseData(plantPowerResponse, 'power'),
            airDistribution: transformResponseData(airPowerResponse, 'power')
          },
          efficiency: {
            plant: transformResponseData(plantEfficiencyResponse, 'efficiency'),
            airDistribution: transformResponseData(airEfficiencyResponse, 'efficiency')
          }
        };
        
        // Store all the data
        setChartData(newChartData);
        
        // We'll render the chart after loading is complete 
        // and the state has been updated
        
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    // Fetch data when the component mounts or siteIdToUse changes
    fetchData();
    
    // Add resize event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      console.log('ConsumptionChart component unmounting');
      window.removeEventListener('resize', handleResize);
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
        chartInstanceRef.current = null;
      }
    };
  }, [siteIdToUse]); // Only depend on the memoized siteIdToUse

  return (
    <div className="col-span-1 md:col-span-2 alto-card">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-sm font-medium">
            Building {activeTab === "power" ? "Power" : "Efficiency"} Breakdown
          </CardTitle>
          <div className="flex gap-3">
            <button
              className={`px-3 py-1 text-xs rounded ${
                activeTab === "power" ? "bg-primary text-white" : "bg-secondary"
              }`}
              onClick={() => setActiveTab("power")}
            >
              Power
            </button>
            <button
              className={`px-3 py-1 text-xs rounded ${
                activeTab === "efficiency"
                  ? "bg-primary text-white"
                  : "bg-secondary"
              }`}
              onClick={() => setActiveTab("efficiency")}
            >
              Efficiency
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="relative">
        {loading ? (
          <div className="w-full h-[300px] flex items-center justify-center">
            <Loader/>
          </div>
        ) : error ? (
          <div className="w-full h-[300px] flex items-center justify-center text-red-500">
            {error}
          </div>
        ) : (
          <div ref={chartRef} style={{ width: "100%", height: "300px" }} />
        )}
      </CardContent>
    </div>
  );
};

export default ConsumptionChart;
