import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { FileUp, AlertCircle, Save, Trash2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';

const DataImport: React.FC = () => {
  const { importData, saveCurrentState, deleteSavedData, hasSavedData } = useSavingDashboard();
  const [error, setError] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      try {
        setError(null);
        await importData(acceptedFiles);
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred during import');
        }
      }
    },
    [importData]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    multiple: false
  });

  const handleSave = () => {
    try {
      saveCurrentState();
      setSaveSuccess(true);
      // Hide success message after 2 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 2000);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while saving');
      }
    }
  };

  const handleDelete = () => {
    try {
      deleteSavedData();
      setIsDeleteDialogOpen(false);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while deleting data');
      }
    }
  };

  return (
    <div className="flex gap-2 items-center">
      <div
        {...getRootProps()}
        className={`border border-dashed rounded-md p-1.5 flex items-center gap-1 cursor-pointer transition-colors h-9 w-32 ${
          isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-blue-500 hover:bg-gray-50'
        }`}
      >
        <input {...getInputProps()} />
        <FileUp className="h-4 w-4 text-blue-500" />
        <span className="text-xs text-gray-700">Import CSV</span>
      </div>
      
      <Button 
        onClick={handleSave} 
        variant="outline" 
        size="sm" 
        className="h-9 text-xs flex items-center gap-1"
      >
        <Save size={14} className="text-green-500" />
        Save
      </Button>
      
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className="h-9 text-xs flex items-center gap-1"
            disabled={!hasSavedData}
          >
            <Trash2 size={14} className="text-red-500" />
            Delete Data
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Data Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete all saved data? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDelete}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {saveSuccess && (
        <div className="ml-2 text-xs text-green-600 flex items-center">
          Data saved successfully!
        </div>
      )}
      
      {error && (
        <div className="ml-2 p-1.5 bg-red-50 border border-red-200 rounded text-red-600 text-xs flex items-center">
          <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
};

export default DataImport;