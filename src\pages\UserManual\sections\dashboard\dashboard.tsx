import { LayoutDashboard } from 'lucide-react';
import { ManualChapter } from '../types';

export const dashboardContent: ManualChapter = {
  id: 'dashboard',
  title: {
    en: 'Summary Dashboard',
    th: 'Summary Dashboard'
  },
  icon: <LayoutDashboard size={18} />,
  sections: [
    {
      id: 'dashboard-overview',
      title: {
        en: 'Plant Dashboard',
        th: 'แดชบอร์ดโรงงาน'
      },
      content: {
        en: `
          <div class="space-y-4">
            <p>
              The Plant Dashboard provides a comprehensive overview of your facility's energy performance and system status.
              It displays key metrics, trends, and alerts in an easy-to-understand format.
            </p>
            
            <h3 class="text-lg font-medium mt-4">Dashboard Elements</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>Performance Metrics</strong>: Real-time KPIs and efficiency indicators</li>
              <li><strong>Equipment Status</strong>: Status overview of major system components</li>
              <li><strong>Energy Consumption</strong>: Current and historical energy usage data</li>
              <li><strong>Alert Panel</strong>: Active alerts and notifications</li>
              <li><strong>Weather Data</strong>: Current conditions and forecast</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">Dashboard Customization</h3>
            <p>
              The Dashboard can be customized to match your specific monitoring needs:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>Widget Arrangement</strong>: Drag and drop widgets to organize your view</li>
              <li><strong>Time Range Selection</strong>: Adjust the time period for displayed data</li>
              <li><strong>Metric Selection</strong>: Choose which performance metrics to display</li>
              <li><strong>Alert Filtering</strong>: Filter notifications by type, severity, or equipment</li>
              <li><strong>View Presets</strong>: Save and load custom dashboard configurations</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">Dashboard Interpretation</h3>
            <p>
              The dashboard uses visual cues to help you quickly understand system status:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li>Green indicators show optimal performance</li>
              <li>Yellow indicators show areas that may need attention</li>
              <li>Red indicators show critical issues requiring immediate action</li>
              <li>Trend arrows show whether metrics are improving or degrading</li>
            </ul>
          </div>
        `,
        th: `
          <div class="space-y-4">
            <p>
              แดชบอร์ดโรงงานให้ภาพรวมที่ครอบคลุมของประสิทธิภาพด้านพลังงานและสถานะระบบของสถานที่ของคุณ
              แสดงเมตริกสำคัญ, แนวโน้ม, และการแจ้งเตือนในรูปแบบที่เข้าใจง่าย
            </p>
            
            <h3 class="text-lg font-medium mt-4">องค์ประกอบของแดชบอร์ด</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>เมตริกประสิทธิภาพ</strong>: KPI และตัวบ่งชี้ประสิทธิภาพแบบเรียลไทม์</li>
              <li><strong>สถานะอุปกรณ์</strong>: ภาพรวมสถานะของส่วนประกอบระบบหลัก</li>
              <li><strong>การใช้พลังงาน</strong>: ข้อมูลการใช้พลังงานปัจจุบันและในอดีต</li>
              <li><strong>แผงการแจ้งเตือน</strong>: การแจ้งเตือนและการแจ้งเตือนที่ใช้งานอยู่</li>
              <li><strong>ข้อมูลสภาพอากาศ</strong>: สภาพปัจจุบันและการพยากรณ์</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">การปรับแต่งแดชบอร์ด</h3>
            <p>
              แดชบอร์ดสามารถปรับแต่งให้ตรงกับความต้องการการตรวจสอบเฉพาะของคุณ:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>การจัดเรียงวิดเจ็ต</strong>: ลากและวางวิดเจ็ตเพื่อจัดระเบียบมุมมองของคุณ</li>
              <li><strong>การเลือกช่วงเวลา</strong>: ปรับช่วงเวลาสำหรับข้อมูลที่แสดง</li>
              <li><strong>การเลือกเมตริก</strong>: เลือกเมตริกประสิทธิภาพที่จะแสดง</li>
              <li><strong>การกรองการแจ้งเตือน</strong>: กรองการแจ้งเตือนตามประเภท, ความรุนแรง, หรืออุปกรณ์</li>
              <li><strong>การตั้งค่าล่วงหน้าของมุมมอง</strong>: บันทึกและโหลดการกำหนดค่าแดชบอร์ดที่กำหนดเอง</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">การตีความแดชบอร์ด</h3>
            <p>
              แดชบอร์ดใช้สัญญาณภาพเพื่อช่วยให้คุณเข้าใจสถานะระบบอย่างรวดเร็ว:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li>ตัวบ่งชี้สีเขียวแสดงประสิทธิภาพที่เหมาะสม</li>
              <li>ตัวบ่งชี้สีเหลืองแสดงพื้นที่ที่อาจต้องการความสนใจ</li>
              <li>ตัวบ่งชี้สีแดงแสดงปัญหาสำคัญที่ต้องดำเนินการทันที</li>
              <li>ลูกศรแนวโน้มแสดงว่าเมตริกกำลังปรับปรุงหรือเสื่อมลง</li>
            </ul>
          </div>
        `
      }
    }
  ]
}; 