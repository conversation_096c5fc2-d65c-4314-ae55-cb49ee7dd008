import { api } from './api';

// User type definition
export interface User {
  id: string | number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role?: 'admin' | 'operator' | 'viewer';
  is_active: boolean;
  is_superuser: boolean;
  last_login?: string;
  date_joined?: string;
  primary_site?: string | null;
  primary_site_role?: 'admin' | 'operator' | 'viewer' | null;
  primary_site_role_display?: string | null;
  sites?: {
    site_id: string;
    site_name: string;
    role: string;
    role_display?: string;
    is_primary: boolean;
  }[];
  // Added for compatibility when processing response
  role_display?: string;
}

// Create user payload interface
export interface CreateUserPayload {
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  role?: string;
  is_active?: boolean;
  is_superuser?: boolean;
  sites?: {
    site_id: string | null;
    role: string | undefined;
    is_primary: boolean;
  }[];
}

// Update user payload interface
export interface UpdateUserPayload {
  username?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  password?: string;
  role?: string;
  is_active?: boolean;
  is_superuser?: boolean;
  sites?: {
    site_id: string | null;
    role: string | undefined;
    is_primary: boolean;
  }[];
}

/**
 * Get all users
 * @returns Array of users
 */
export const getUsers = async (): Promise<User[]> => {
  try {
    const response = await api.get('/auth/users/');
    console.log('Get users API response:', response.data);
    
    // Handle nested API response structure
    const responseData = response.data as any;
    
    // Check if response has the expected nested structure
    if (responseData && responseData.success === true && responseData.data && Array.isArray(responseData.data.users)) {
      // Process users to ensure role is set
      return responseData.data.users.map((user: any) => ({
        ...user,
        role: user.primary_site_role || user.role || '',
        role_display: user.primary_site_role_display || ''
      }));
    }
    
    // Fallback for other possible response formats
    if (responseData && Array.isArray(responseData.users)) {
      return responseData.users;
    }
    
    if (responseData && Array.isArray(responseData.results)) {
      return responseData.results;
    }
    
    // If response is an array directly
    if (Array.isArray(responseData)) {
      return responseData;
    }
    
    // If we can't identify the format, log it and return empty array
    console.error('Unexpected API response format:', responseData);
    return [];
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

/**
 * Create a new user
 * @param userData User data
 * @returns Created user
 */
export const createUser = async (userData: CreateUserPayload): Promise<User> => {
  try {
    const response = await api.post('/auth/users/create/', userData);
    console.log('Create user response:', response.data);
    
    // Handle nested response structure
    const responseData = response.data as any;
    if (responseData && responseData.success && responseData.data) {
      return responseData.data.user || responseData.data;
    }
    
    return response.data as User;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

/**
 * Update existing user
 * @param userId User ID
 * @param userData Updated user data
 * @returns Updated user
 */
export const updateUser = async (userId: string, userData: UpdateUserPayload): Promise<User> => {
  try {
    const response = await api.put(`/auth/users/${userId}/`, userData);
    console.log('Update user response:', response.data);
    
    // Handle nested response structure
    const responseData = response.data as any;
    if (responseData && responseData.success && responseData.data) {
      return responseData.data.user || responseData.data;
    }
    
    return response.data as User;
  } catch (error) {
    console.error(`Error updating user ${userId}:`, error);
    throw error;
  }
};

/**
 * Delete user
 * @param userId User ID
 * @returns Success message
 */
export const deleteUser = async (userId: string): Promise<any> => {
  try {
    const response = await api.delete(`/auth/users/${userId}/delete/`);
    console.log('Delete user response:', response.data);
    return response.data;
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    throw error;
  }
};

/**
 * Get a single user by ID
 * @param userId User ID
 * @returns User data
 */
export const getUserById = async (userId: string): Promise<User> => {
  try {
    const response = await api.get(`/auth/users/${userId}/`);
    console.log('Get user by ID response:', response.data);
    
    // Handle nested response structure
    const responseData = response.data as any;
    if (responseData && responseData.success && responseData.data) {
      return responseData.data.user || responseData.data;
    }
    
    return response.data as User;
  } catch (error) {
    console.error(`Error fetching user ${userId}:`, error);
    throw error;
  }
}; 