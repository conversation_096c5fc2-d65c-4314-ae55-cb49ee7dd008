import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getDashAppUrl } from '@/utils/dash';
import { SavingDashboardProvider } from './contexts/SavingDashboardContext';
import MainDashboard from './components/MainDashboard';

const SavingReport: React.FC = () => {
  const { site } = useAuth();
  const dashboardAppUrl = getDashAppUrl('saving-calculation-report', site?.id);

  return (
    <div className="w-full h-screen overflow-hidden bg-gray-50 p-4">
      <SavingDashboardProvider>
        <MainDashboard />
      </SavingDashboardProvider>
    </div>
  );
};

export default SavingReport; 