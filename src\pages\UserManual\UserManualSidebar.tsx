import React, { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { ManualChapter, Language } from './sections/types';
import { ScrollArea } from '@/components/ui/scroll-area';

interface ManualSidebarProps {
  manualContent: ManualChapter[];
  activeChapter: string;
  activeSection: string;
  activeSubsection: string;
  setActiveChapter: (chapterId: string) => void;
  setActiveSection: (sectionId: string) => void;
  setActiveSubsection: (subsectionId: string | null) => void;
  language: Language;
}

const UserManualSidebar: React.FC<ManualSidebarProps> = ({
  manualContent,
  activeChapter,
  activeSection,
  activeSubsection,
  setActiveChapter,
  setActiveSection,
  setActiveSubsection,
  language
}) => {
  // State to track expanded chapters
  const [expandedChapters, setExpandedChapters] = useState<Record<string, boolean>>(
    manualContent.reduce((acc, chapter) => {
      acc[chapter.id] = chapter.id === activeChapter;
      return acc;
    }, {} as Record<string, boolean>)
  );

  // Toggle chapter expansion
  const toggleChapter = (chapterId: string) => {
    setExpandedChapters(prev => ({
      ...prev,
      [chapterId]: !prev[chapterId]
    }));
  };

  return (
    <div className="h-full alto-card p-6 sticky top-0">
      <ScrollArea className="h-[calc(100vh-120px)]">
        <div className="space-y-1">
          {manualContent.map(chapter => (
            <div key={chapter.id} className="mb-2">
              {/* Chapter header */}
              <button
                className={`w-full flex items-center justify-between p-2 rounded-md text-left text-sm ${
                  activeChapter === chapter.id ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-100 text-gray-700'
                }`}
                onClick={() => {
                  toggleChapter(chapter.id);
                  setActiveChapter(chapter.id);
                  if (chapter.sections.length > 0) {
                    setActiveSection(chapter.sections[0].id);
                    setActiveSubsection(null);
                  }
                }}
              >
                <div className="flex items-center">
                  <span className="mr-2">{chapter.icon}</span>
                  <span>{chapter.title[language]}</span>
                </div>
                <span>
                  {expandedChapters[chapter.id] ? (
                    <ChevronRight size={16} />
                  ) : (
                    <ChevronRight size={16} />
                  )}
                </span>
              </button>
              {/* Sections */}
              {expandedChapters[chapter.id] && (
                <div className="ml-6 mt-1 space-y-1">
                  {chapter.sections.map((section) => (
                    <div key={section.id}>
                      {/* <button
                        className={`w-full text-left p-1.5 text-sm rounded-md ${
                          activeChapter === chapter.id && activeSection === section.id
                            ? 'text-blue-700 font-medium'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        onClick={() => {
                          setActiveChapter(chapter.id);
                          setActiveSection(section.id);
                          setActiveSubsection(null);
                        }}
                      >
                        {section.title}
                      </button> */}
                      {/* Subsections */}
                      {/* {section.subsections && activeChapter === chapter.id && activeSection === section.id && (
                        <div className="ml-4 mt-1 space-y-1">
                          {section.subsections.map((subsection) => (
                            <button
                              key={subsection.id}
                              className={`w-full text-left p-1 text-xs rounded-md ${
                                activeSubsection === subsection.id
                                  ? 'text-blue-700 font-medium'
                                  : 'text-gray-500 hover:text-gray-700'
                              }`}
                              onClick={() => {
                                setActiveChapter(chapter.id);
                                setActiveSection(section.id);
                                setActiveSubsection(subsection.id);
                              }}
                            >
                              {subsection.title}
                            </button>
                          ))}
                        </div>
                      )} */}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default UserManualSidebar;