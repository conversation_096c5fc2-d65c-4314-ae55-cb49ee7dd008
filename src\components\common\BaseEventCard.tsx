import React from 'react';
import { cn } from '@/lib/utils';

// Removed formatRemainingTimeDisplay helper, as it will be used in specific cards

export interface BaseEventCardProps {
  isActive?: boolean;
  showHourMarker?: boolean;
  time: string; // Still needed for the hour marker label
  children: React.ReactNode; // To render the specific card content
  className?: string;
}

const BaseEventCard: React.FC<BaseEventCardProps> = ({
  isActive = false,
  showHourMarker = false,
  time,
  children, // Destructure children
  className,
}) => {
  return (
    <div className={cn("grid grid-cols-[2.5rem,2rem,1fr] items-start gap-0", className)}>
      {/* Time Label - Only show for hour markers */}
      <div className="text-right h-full flex items-end justify-end">
        {showHourMarker && (
          <span className="text-xs font-medium text-muted-foreground self-start leading-none">
            {time}
          </span>
        )}
      </div>

      {/* Timeline Dot Container */}
      <div className="flex justify-center relative">
        {showHourMarker ? (
          <div
            className={cn(
              "w-3 h-3 rounded-full shrink-0 relative z-10",
              isActive
                ? "bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4]"
                : "bg-[#8BC6FF]"
            )}
          />
        ) : (
          <div className="w-2 h-2 rounded-full bg-[#8BC6FF]/50 mt-[2px]" />
        )}
      </div>

      {/* Event Card Content Wrapper - Renders children now */}
      <div
        className={cn(
          "pl-3 p-3 rounded-lg border",
          isActive
            ? "bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] border-[#0E7EE4] shadow-lg shadow-[#0E7EE4]/20"
            : "bg-primary-foreground border-border"
        )}
      >
        {children} {/* Render the content passed from specific cards */}
      </div>
    </div>
  );
};

export default BaseEventCard;
