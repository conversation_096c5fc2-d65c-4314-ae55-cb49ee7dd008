import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/utils/supabase';

interface MaintenanceData {
  [deviceId: string]: {
    status: 'under_maintenance' | 'completed';
    ticket_started_at: string;
    ticket_closed_at: string | null;
    description: string;
    ticket_started_by: string;
    ticket_closed_by: string | null;
  };
}

interface DeviceData {
  [id: string]: {
    deviceId: string;
    name: string;
    model: string;
  };
}

export interface Device {
  id: string;
  deviceId: string;
  name: string;
  model: string;
}

interface DeviceContextType {
  maintenanceData: MaintenanceData;
  deviceData: DeviceData;
  isDeviceUnderMaintenance: (deviceId: string) => boolean;
  getDeviceMaintenanceInfo: (deviceId: string) => MaintenanceData[string] | null;
  getDeviceName: (deviceId: string) => string | null;
  getDevicesByType: () => Record<string, Device[]>;
}

const DeviceContext = createContext<DeviceContextType | undefined>(undefined);

export function DeviceProvider({ children }: { children: React.ReactNode }) {
  const [maintenanceData, setMaintenanceData] = useState<MaintenanceData>({});
  const [deviceData, setDeviceData] = useState<DeviceData>({});
  const [loading, setLoading] = useState(true);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // 1. Fetch device data
        const { data: devices, error: deviceError } = await supabase
          .from('devices')
          .select('*');

        if (deviceError) {
          console.error('Error fetching devices:', deviceError);
          return;
        }
        
        // Create device mapping
        const deviceMap: DeviceData = {};
        devices.forEach(device => {
          deviceMap[device.id] = {
            deviceId: device.device_id,
            name: device.name,
            model: device.model,
          };
        });
        
        setDeviceData(deviceMap);
        
        // 2. Fetch maintenance data
        const { data: maintenanceRecords, error: maintenanceError } = await supabase
          .from('maintenance_history')
          .select('*')
          .eq('status', 'under_maintenance');
          
        if (maintenanceError) {
          console.error('Error fetching maintenance records:', maintenanceError);
          return;
        }
        
        // Create maintenance mapping
        const maintenanceMap: MaintenanceData = {};
        maintenanceRecords.forEach(record => {
          const device = deviceMap[record.device_id];
          if (device && device.deviceId) {
            maintenanceMap[device.deviceId] = {
              status: record.status,
              ticket_started_at: record.ticket_started_at,
              ticket_closed_at: record.ticket_closed_at,
              description: record.description,
              ticket_started_by: record.ticket_started_by,
              ticket_closed_by: record.ticket_closed_by,
            };
          }
        });
        
        setMaintenanceData(maintenanceMap);
      } catch (error) {
        console.error('Error initializing maintenance context:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    const channel = supabase
      .channel('maintenance_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'maintenance_history',
        },
        (payload: any) => {
          console.log('Maintenance update payload:', payload);
          try {
            // For any device with maintenance record updates
            if (payload.new && typeof payload.new === 'object' && 
                'device_id' in payload.new && 
                payload.new.device_id && 
                deviceData[payload.new.device_id]) {
              
              const deviceId = deviceData[payload.new.device_id].deviceId;
              
              // If status is completed, remove from active maintenance list
              if ('status' in payload.new && payload.new.status === 'completed') {
                setMaintenanceData(prev => {
                  const newData = { ...prev };
                  if (newData[deviceId]) {
                    delete newData[deviceId];
                  }
                  return newData;
                });
              } 
              // If status is under_maintenance, add or update in maintenance list
              else if ('status' in payload.new && payload.new.status === 'under_maintenance') {
                setMaintenanceData(prev => ({
                  ...prev,
                  [deviceId]: {
                    status: payload.new.status,
                    ticket_started_at: payload.new.ticket_started_at || '',
                    ticket_closed_at: payload.new.ticket_closed_at || null,
                    description: payload.new.description || '',
                    ticket_started_by: payload.new.ticket_started_by || '',
                    ticket_closed_by: payload.new.ticket_closed_by || null
                  }
                }));
              }
            }
            
            // Handle deletion of maintenance record
            else if (payload.eventType === 'DELETE' && 
                     payload.old && typeof payload.old === 'object' && 
                     'device_id' in payload.old && 
                     payload.old.device_id && 
                     deviceData[payload.old.device_id]) {
              
              const deviceId = deviceData[payload.old.device_id].deviceId;
              setMaintenanceData(prev => {
                const newData = { ...prev };
                if (newData[deviceId]) {
                  delete newData[deviceId];
                }
                return newData;
              });
            }
          } catch (error) {
            console.error('Error handling maintenance update:', error);
          }
        }
      )
      .subscribe();
      
    return () => {
      channel.unsubscribe();
    };
  }, [deviceData]);

  // Function to determine if a device is under maintenance
  const isDeviceUnderMaintenance = (deviceId: string): boolean => {
    return !!maintenanceData[deviceId] && maintenanceData[deviceId].status === 'under_maintenance';
  };

  // Function to get maintenance info for a device
  const getDeviceMaintenanceInfo = (deviceId: string): MaintenanceData[string] | null => {
    return maintenanceData[deviceId] || null;
  };

  // Function to get device name by device ID
  const getDeviceName = (deviceId: string): string | null => {
    // Find the device in the deviceData object by its deviceId property
    for (const id in deviceData) {
      if (deviceData[id].deviceId === deviceId) {
        return deviceData[id].name;
      }
    }
    return null;
  };

  // Function to get devices by type
  const getDevicesByType = (): Record<string, Device[]> => {
    const devicesByType: Record<string, Device[]> = {};
    for (const id in deviceData) {
      const device = deviceData[id];
      if (!devicesByType[device.model]) {
        devicesByType[device.model] = [];
      }
      devicesByType[device.model].push({
        id,
        deviceId: device.deviceId,
        name: device.name,
        model: device.model,
      });
    }
    return devicesByType;
  };

  return (
    <DeviceContext.Provider value={{ 
      maintenanceData, 
      deviceData,
      isDeviceUnderMaintenance,
      getDeviceMaintenanceInfo,
      getDeviceName,
      getDevicesByType
    }}>
      {children}
    </DeviceContext.Provider>
  );
}

export function useDevice() {
  const context = useContext(DeviceContext);
  if (context === undefined) {
    throw new Error('useDevice must be used within a DeviceProvider');
  }
  return context;
}