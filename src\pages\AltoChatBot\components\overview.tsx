import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { MessageIcon } from "./icons";
import { FloatingChat } from "./floating-chat";

export const Overview = () => {
  return (
    <>
      <div className="absolute inset-0 flex items-center justify-center bg-black">
        <motion.div
          key="overview"
          className="w-full max-w-3xl px-6"
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.98 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex flex-col gap-8 leading-relaxed text-center text-white">
            <p className="flex flex-row justify-center gap-4 items-center">
              <MessageIcon className="w-8 h-8 text-white" />
            </p>
            <p>
              Welcome to Alto CERO AI Assistant, your intelligent companion for building management. Powered by{" "}
              <a 
                className="font-medium underline underline-offset-4 text-blue-400 hover:text-blue-300"
                href="https://altotech.ai"
                target="_blank"
                rel="noreferrer"
              >
                AltoTech Global
              </a>
            </p>
            <p>
              Our AI assistant provides real-time insights and intelligent building management solutions to optimize your facility operations, with seamless integration into existing systems.
            </p>
          </div>
        </motion.div>
      </div>
      <FloatingChat />
    </>
  );
};
