import React, { useState, useEffect } from 'react';
import { CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search, Plus, Edit, Trash2, MoreHorizontal, RefreshCw, AlertCircle } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useAuth } from '@/contexts/AuthContext';
import { getUsers, createUser, updateUser, deleteUser, User, CreateUserPayload } from '@/services/userService';
import { useToast } from "@/components/ui/use-toast";
import { Loader } from "@/components/ui/loader";
import { getSiteId } from '@/services/authService';

interface UserManagementProps {
  isAdmin: boolean;
}

const UserManagement: React.FC<UserManagementProps> = ({ isAdmin }) => {
  const { hasRole, isSuperuser, userRole } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [isEditUserOpen, setIsEditUserOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // New user form state
  const [newUser, setNewUser] = useState<CreateUserPayload>({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    password: '',
    role: 'viewer',
    is_active: true
  });

  // Fetch users on component mount
  useEffect(() => {
    console.log("Component mounted, fetching users...");
    fetchUsers();
  }, []);

  // Fetch users from the API
  const fetchUsers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const users = await getUsers();
      console.log('Users loaded:', users);
      
      // Ensure the result is an array
      if (Array.isArray(users)) {
        setUsers(users);
      } else {
        console.error('Users is not an array:', users);
        setUsers([]);
        setError('Invalid response format from server. Please contact support.');
      }
    } catch (error) {
      console.error('Failed to fetch users', error);
      setUsers([]);
      setError('Failed to load users. Please try again later.');
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load users. Please try again later.",
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle refreshing the user list
  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchUsers();
  };

  // Add this helper function to safely access user properties
  const safeString = (value: any): string => {
    if (value === null || value === undefined) return '';
    return String(value);
  };

  // Modify filter function to be more robust
  const filteredUsers = Array.isArray(users) ? users.filter(user => {
    if (!user) return false;
    
    const usernameMatch = safeString(user.username).toLowerCase().includes(searchQuery.toLowerCase());
    const emailMatch = safeString(user.email).toLowerCase().includes(searchQuery.toLowerCase());
    const nameMatch = `${safeString(user.first_name)} ${safeString(user.last_name)}`.toLowerCase().includes(searchQuery.toLowerCase());
    
    return usernameMatch || emailMatch || nameMatch;
  }) : [];

  // Add debugging to help diagnose the issue
  useEffect(() => {
    if (users && !Array.isArray(users)) {
      console.error('Users is not an array:', users);
    }
  }, [users]);

  // Handle user creation
  const handleAddUser = async () => {
    // Validate required fields
    if (!newUser.username || !newUser.email || !newUser.password || !newUser.role) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fill all required fields.",
      });
      return;
    }

    // Get the current site ID
    const siteId = getSiteId();

    setIsSubmitting(true);
    try {      
      // Construct the payload exactly matching the working format
      const userPayload = {
        username: newUser.username,
        email: newUser.email,
        first_name: newUser.first_name,
        last_name: newUser.last_name,
        password: newUser.password,
        is_active: newUser.is_active,
        // Don't include role at the top level
        sites: [
          {
            site_id: siteId,
            role: newUser.role,
            is_primary: true
          }
        ]
      };
      
      await createUser(userPayload);
      
      // Reset form and close dialog
      setNewUser({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        password: '',
        role: 'viewer',
        is_active: true
      });
      
      toast({
        title: "Success",
        description: "User created successfully.",
      });
      
      // Refresh user list
      fetchUsers();
      setIsAddUserOpen(false);
    } catch (error: any) {
      console.error('Failed to create user', error);
      
      let errorMessage = "Failed to create user. Please try again.";
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }
      
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to convert ID to string safely
  const safeIdToString = (id: string | number | undefined): string => {
    if (id === undefined) return '';
    return String(id);
  };

  // Handle user update
  const handleUpdateUser = async () => {
    if (!selectedUser) return;
    
    // Get the current site ID
    const siteId = getSiteId();

    try {
      setIsSubmitting(true);
      
      // Construct the payload with sites array 
      const updatePayload = {
        first_name: selectedUser.first_name,
        last_name: selectedUser.last_name,
        email: selectedUser.email,
        is_active: selectedUser.is_active,
        // Include sites array for role updates
        sites: [
          {
            site_id: siteId,
            role: selectedUser.role,
            is_primary: true
          }
        ]
      };
      await updateUser(safeIdToString(selectedUser.id), updatePayload);
      
      toast({
        title: "Success",
        description: "User updated successfully."
      });
      
      fetchUsers();
      setIsEditUserOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Error updating user:', error);
      
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update user"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle user deletion
  const handleDeleteUser = async (userId: string | number) => {
    try {
      setIsSubmitting(true);
      await deleteUser(safeIdToString(userId));
      
      toast({
        title: "Success",
        description: "User deleted successfully."
      });
      
      fetchUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete user"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle user status toggle
  const handleToggleStatus = async (userId: string | number, isActive: boolean, userRole?: string) => {
    
    // Get the current site ID
    const siteId = getSiteId();

    try {
      setIsLoading(true);
      
      // Create payload with both status update and existing role
      const updatePayload = {
        is_active: !isActive,
        // Include sites array to preserve role
        sites: [
          {
            site_id: siteId,
            role: userRole,
            is_primary: true
          }
        ]
      };
      
      await updateUser(safeIdToString(userId), updatePayload);
      
      toast({
        title: "Success",
        description: "User status updated successfully."
      });
      
      fetchUsers();
    } catch (error) {
      console.error('Error toggling user status:', error);
      
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update user status"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if current user has enough permissions to manage users
  const canManageUsers = isAdmin || hasRole('admin') || isSuperuser;

  // Function to format date string
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (e) {
      return dateString;
    }
  };

  // Function to get user's full name
  const getUserFullName = (user: User) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    } else if (user.first_name) {
      return user.first_name;
    } else if (user.last_name) {
      return user.last_name;
    } else {
      return user.username;
    }
  };

  if (!canManageUsers) {
    return null;
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="container py-6 flex flex-col items-center justify-center h-80">
        <Loader size="md" />
        <p className="text-muted-foreground">Loading users...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 flex justify-center">
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-6">
      <div className="alto-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                View and manage user accounts and permissions
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                onClick={handleRefresh} 
                variant="outline" 
                size="icon"
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
              <Button onClick={() => setIsAddUserOpen(true)}>
                <Plus className="mr-2 h-4 w-4" /> Add User
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="rounded-md border h-[57vh] overflow-y-scroll">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      {searchQuery 
                        ? "No users found matching your search." 
                        : "No users found in the system."}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{getUserFullName(user)}</span>
                          <span className="text-sm text-muted-foreground">{user.email}</span>
                          <span className="text-xs text-muted-foreground">@{user.username}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`inline-block rounded-full px-2 py-1 text-xs font-medium ${
                          user.is_superuser
                            ? 'bg-primary/10 text-primary' 
                            : (user.role === 'admin' || user.primary_site_role === 'admin')
                              ? 'bg-primary/10 text-primary'
                            : (user.role === 'operator' || user.primary_site_role === 'operator')
                              ? 'bg-yellow-500/10 text-yellow-600'
                              : 'bg-gray-100 text-gray-600'
                        }`}>
                          {user.is_superuser 
                            ? 'Superuser' 
                            : user.role_display || user.primary_site_role_display
                              ? (user.role_display || user.primary_site_role_display)
                              : (user.role || user.primary_site_role || '').charAt(0).toUpperCase() + (user.role || user.primary_site_role || '').slice(1) || 'Viewer'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Switch
                            checked={user.is_active}
                            onCheckedChange={() => handleToggleStatus(user.id, user.is_active, user.role)}
                            className="mr-2"
                          />
                          <span className={`text-sm ${
                            user.is_active
                              ? 'text-green-600' 
                              : 'text-gray-500'
                          }`}>
                            {user.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatDate(user.last_login)}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedUser(user);
                                setIsEditUserOpen(true);
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </div>

      {/* Add User Dialog */}
      <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user account and set their permissions
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  value={newUser.first_name}
                  onChange={(e) => setNewUser({ ...newUser, first_name: e.target.value })}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  value={newUser.last_name}
                  onChange={(e) => setNewUser({ ...newUser, last_name: e.target.value })}
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="email">Email Address<span className="text-red-500">*</span></Label>
              <Input
                id="email"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                required
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="username">Username<span className="text-red-500">*</span></Label>
              <Input
                id="username"
                value={newUser.username}
                onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
                required
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="password">Password<span className="text-red-500">*</span></Label>
              <Input
                id="password"
                type="password"
                value={newUser.password}
                onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                required
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="role">Role</Label>
              <Select
                value={newUser.role}
                onValueChange={(value) => setNewUser({ ...newUser, role: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewer">Viewer</SelectItem>
                  <SelectItem value="operator">Operator</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2 pt-2">
              <Switch 
                id="is_active"
                checked={!!newUser.is_active}
                onCheckedChange={(checked) => setNewUser({ ...newUser, is_active: checked })}
              />
              <Label htmlFor="is_active">Active account</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsAddUserOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleAddUser}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader size="sm" />
                  Creating...
                </>
              ) : (
                "Create User"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      {selectedUser && (
        <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit User</DialogTitle>
              <DialogDescription>
                Update user account details and permissions
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-first-name">First Name</Label>
                  <Input
                    id="edit-first-name"
                    value={selectedUser.first_name}
                    onChange={(e) => setSelectedUser({ ...selectedUser, first_name: e.target.value })}
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="edit-last-name">Last Name</Label>
                  <Input
                    id="edit-last-name"
                    value={selectedUser.last_name}
                    onChange={(e) => setSelectedUser({ ...selectedUser, last_name: e.target.value })}
                  />
                </div>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="edit-email">Email Address</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={selectedUser.email}
                  onChange={(e) => setSelectedUser({ ...selectedUser, email: e.target.value })}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="edit-username">Username</Label>
                <Input
                  id="edit-username"
                  value={selectedUser.username}
                  onChange={(e) => setSelectedUser({ ...selectedUser, username: e.target.value })}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="edit-role">Role</Label>
                <Select
                  value={selectedUser.role || 'viewer'}
                  onValueChange={(value) => setSelectedUser({ 
                    ...selectedUser, 
                    role: value as 'admin' | 'operator' | 'viewer' 
                  })}
                  disabled={selectedUser.is_superuser}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="viewer">Viewer</SelectItem>
                    <SelectItem value="operator">Operator</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
                {selectedUser.is_superuser && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Role cannot be changed for superusers
                  </p>
                )}
              </div>
              
              <div className="flex items-center space-x-2 pt-2">
                <Switch 
                  id="edit-is-active"
                  checked={selectedUser.is_active}
                  onCheckedChange={(checked) => setSelectedUser({ ...selectedUser, is_active: checked })}
                />
                <Label htmlFor="edit-is-active">Active account</Label>
              </div>
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsEditUserOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleUpdateUser}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader size="sm" />
                    Saving...
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default UserManagement; 