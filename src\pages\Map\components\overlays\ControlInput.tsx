import React, { useState } from 'react';
import { Settings } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { ControlInputConfig } from '../types';
import { useRealtime } from '@/contexts/RealtimeContext';
import { useAutopilot } from '@/contexts/AutopilotContext';
import { useDevice } from '@/contexts/DeviceContext';
import manual_icon from '@/assets/manual_icon.svg';
import maintenance_icon from '@/assets/maintenance_icon.svg';
import { sendControl, updateAutopilotStatus } from '@/services/controlService';

interface ControlInputProps {
  config: ControlInputConfig;
  isEditMode?: boolean;
  onClick?: (e: React.MouseEvent) => void;
}

// Auto-Manual Switch component
const AutoManualSwitch: React.FC<{
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
}> = ({ checked, onCheckedChange, disabled }) => {
  return (
    <div 
      className="relative inline-flex items-center" 
      onClick={(e) => e.stopPropagation()}
    >
      <Switch
        checked={checked}
        onCheckedChange={onCheckedChange}
        disabled={disabled}
        size="sm"
        className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-[#0E7EE4] data-[state=checked]:to-[#14B8B4]"
      />
      <div className="absolute inset-0 flex justify-between items-center px-[6px] pointer-events-none">
        <span className="text-[8px] font-medium text-white">A</span>
        <span className="text-[8px] font-medium text-white">M</span>
      </div>
    </div>
  );
};

export const ControlInput: React.FC<ControlInputProps> = ({
  config,
  isEditMode = false,
  onClick,
}) => {
  // Safely extract properties with fallbacks to prevent undefined errors
  const { 
    title = 'Control', 
    unit = '', 
    deviceId = '',
    datapoint = ''
  } = config.properties || {};
  
  const { getValue, setValue } = useRealtime();
  const { isDeviceInAutopilotMode } = useAutopilot();
  const { isDeviceUnderMaintenance } = useDevice();
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newValue, setNewValue] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [isAutopilotMode, setIsAutopilotMode] = useState(isDeviceInAutopilotMode(deviceId));
  
  // Get the current value from the realtime context
  const currentValue = getValue(deviceId, datapoint);
  const displayValue = currentValue !== null && currentValue !== undefined 
    ? Number(currentValue).toFixed(1) 
    : '-';

  const handleSave = async () => {
    try {
      setIsUpdating(true);
      await sendControl(deviceId, datapoint, Number(newValue));
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error setting value:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleAutopilotToggle = async (checked: boolean) => {
    try {
      setIsUpdating(true);
      await updateAutopilotStatus(deviceId, checked);
      setIsAutopilotMode(checked);
    } catch (error) {
      console.error('Error updating autopilot status:', error);
      // Revert UI state if update fails
      setIsAutopilotMode(!checked);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div onClick={onClick}>
      <div 
        className={cn(
          "w-[196px] h-[26px] flex justify-between items-center rounded-md",
          "bg-background border border-border shadow-sm"
        )}
        style={{ 
          width: 196,
          height: 26,
          padding: '4px 6px',
          background: 'var(--color-card-background, #F9FAFF)',
          borderRadius: 6,
          border: '1px solid var(--color-border-subtle, #EDEFF9)',
          boxShadow: '1px 3px 20px rgba(154, 170, 207, 0.10)'
        }}
      >
        <div 
          className="text-center text-primary text-xs font-semibold tracking-tight flex items-center gap-1"
          style={{
            color: 'var(--color-primary, #065BA9)',
            fontSize: 10,
            fontWeight: 600,
            letterSpacing: '0.01em'
          }}
        >
          {!isDeviceInAutopilotMode(deviceId) && 
            <img src={manual_icon} alt="Manual Mode" className="w-[10px] h-[10px]" />
          }
          {isDeviceUnderMaintenance(deviceId) && 
            <img src={maintenance_icon} alt="Under Maintenance" className="w-[10px] h-[10px]" />
          }
          <span>{title}</span>
        </div>
        <div className="flex items-center gap-2">
          <div 
            className="text-center text-primary-bright text-xs font-semibold tracking-tight"
            style={{
              color: 'var(--color-primary-bright, #0E7EE4)',
              fontSize: 12,
              fontWeight: 600,
              letterSpacing: '0.01em'
            }}
          >
            {displayValue} {unit}
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setIsDialogOpen(true);
              setNewValue(displayValue !== '-' ? displayValue : '0.0');
              setIsAutopilotMode(isDeviceInAutopilotMode(deviceId));
            }}
            className="w-5 h-5 rounded-full flex items-center justify-center bg-primary/10 text-primary hover:bg-primary/20"
            title="Edit Value"
          >
            <Settings className="h-3 w-3" />
          </button>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-primary-dark">Edit Control Value</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="control-name" className="text-right text-muted">
                Control
              </Label>
              <div className="col-span-3 text-foreground font-medium">
                {title}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="autopilot-mode" className="text-right text-muted">
                Mode
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <AutoManualSwitch 
                  checked={isAutopilotMode} 
                  onCheckedChange={handleAutopilotToggle}
                  disabled={isUpdating || deviceId === ''}
                />
                <span className="text-sm text-foreground">
                  {isAutopilotMode ? 'Autopilot' : 'Manual'}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="current-value" className="text-right text-muted">
                Current Value
              </Label>
              <div className="col-span-3 text-foreground">
                {displayValue} {unit}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="new-value" className="text-right text-muted">
                New Value
              </Label>
              <Input
                id="new-value"
                value={newValue}
                onChange={(e) => setNewValue(e.target.value)}
                className="col-span-3 bg-background border-border"
                disabled={isAutopilotMode || isUpdating}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)} className="border-border text-muted-foreground hover:bg-card hover:text-foreground">
              Cancel
            </Button>
            <Button 
              onClick={handleSave} 
              className="bg-primary text-primary-foreground hover:bg-primary/90"
              disabled={isAutopilotMode || isUpdating || deviceId === ''}
            >
              Send Command
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
