export interface DataRow {
  id?: number;
  date: string;
  plant_energy: number;
  humidity: number;
  [key: string]: any;
}

export interface ReportingDataRow extends DataRow {
  baseline_kwh: number;
  actual_kwh: number;
  savings_kwh: number;
  savings_percent: number;
}

export interface RegressionResult {
  equation: string;
  coefficients: {
    intercept: number;
    [key: string]: number;
  };
  statistics: {
    r2: number;
    adjustedR2: number;
    rse: number;
    fStatistic: number;
    pValue: number;
    degreesOfFreedom: number;
    significanceF: number;
    observations: number;
    coefficientErrors: { [key: string]: number };
    tStatistics: { [key: string]: number };
    pValues: { [key: string]: number };
    confidenceIntervals: { [key: string]: [number, number] };
    cvrmse: number;
    nmbe: number;
    // Additional statistics for statsmodels-like output
    logLikelihood?: number;
    aic?: number;
    bic?: number;
    omnibus?: number;
    durbinWatson?: number;
    probOmnibus?: number;
    jarqueBera?: number;
    probJB?: number;
    skew?: number;
    kurtosis?: number;
    condNo?: number;
  };
  predictionFunction: (data: { [key: string]: number }) => number;
}

export interface VariableSelection {
  dependent: string;
  independent: string[];
}

export interface DateRange {
  start: string;
  end: string;
}

export interface SummaryStats {
  reportingDays: number;
  reportingPeriod: {
    start: string;
    end: string;
  };
  totalBaselineConsumption: number;
  totalActualConsumption: number;
  totalSaving: number;
  savingPercentage: number;
  monthlyBreakdown: Array<{
    month: string;
    days: number;
    baselineKwh: number;
    actualKwh: number;
    savingsKwh: number;
    uncertaintyKwh: number;
    savingsPercent: number;
    uncertaintyPercent: number;
  }>;
}

export type ChartType = 'bar' | 'scatter' | 'line';
export type TabType = 'baseline' | 'reporting' | 'summary' | 'settings';