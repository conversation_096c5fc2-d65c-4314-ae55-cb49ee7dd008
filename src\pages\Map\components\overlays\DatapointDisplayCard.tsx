import React from 'react';
import { cn } from '@/lib/utils';
import { useRealtime } from '@/contexts/RealtimeContext';

interface DatapointRowProps {
  name: string;
  value: any;
  unit: string;
  precision: number;
}

const DatapointRow: React.FC<DatapointRowProps> = ({ name, value, unit, precision }) => {
  const displayValue = value !== null && value !== undefined 
    ? `${Number(value).toFixed(precision)} ${unit}`
    : '--';

  return (
    <div className="w-full px-[6px] py-[3px] bg-card rounded-[5px] flex justify-between items-center">
      <div className="text-center text-muted-foreground text-[11px] font-normal">
        {name}
      </div>
      <div className="text-center text-primary text-[12px] font-semibold tracking-[0.01em]">
        {displayValue}
      </div>
    </div>
  );
};

interface DatapointDisplayCardProps {
  config: {
    id: string;
    type: string;
    label: string;
    properties: {
      title: string;
      datapoints: Array<{
        name: string;
        deviceId: string;
        datapoint: string;
        unit: string;
        precision: number;
      }>;
    };
  };
  isEditMode?: boolean;
  onClick?: (e: React.MouseEvent) => void;
}

export const DatapointDisplayCard: React.FC<DatapointDisplayCardProps> = ({
  config,
  isEditMode,
  onClick,
}) => {
  const { getValue } = useRealtime();

  return (
    <div
      onClick={onClick}
      className={cn(
        "w-[196px] p-[6px] bg-background rounded-[6px] border border-border flex flex-col gap-[4px] shadow-sm",
        isEditMode && "cursor-move ring-2 ring-ring ring-offset-2"
      )}
    >
      {/* Title */}
      <div className="text-primary text-[10px] font-semibold tracking-[0.01em]">
        {config.properties.title}
      </div>

      {/* Datapoints */}
      <div className="flex flex-col gap-[4px]">
        {config.properties.datapoints.map((datapoint) => (
          <DatapointRow
            key={`${datapoint.deviceId}:${datapoint.datapoint}`}
            name={datapoint.name}
            value={getValue(datapoint.deviceId, datapoint.datapoint)}
            unit={datapoint.unit}
            precision={datapoint.precision}
          />
        ))}
      </div>
    </div>
  );
};

