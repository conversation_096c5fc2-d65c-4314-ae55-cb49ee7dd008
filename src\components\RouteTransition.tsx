// RouteTransition.jsx
import { useState, useEffect } from 'react';
import { useLocation, useOutlet } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';

export function RouteTransition() {
  const location = useLocation();
  const currentOutlet = useOutlet();
  const [prevLocation, setPrevLocation] = useState(location);
  const [prevOutlet, setPrevOutlet] = useState(currentOutlet);
  const [isTransitioning, setIsTransitioning] = useState(false);
  
  useEffect(() => {
    // Only update if the path changed
    if (location.pathname !== prevLocation.pathname) {
      setIsTransitioning(true);
      // Store the previous outlet so we can animate it out
      setPrevOutlet(prevOutlet);
      setPrevLocation(prevLocation);
    }
  }, [location]);

  // Content to render (previous during transition, current after)
  const content = isTransitioning ? prevOutlet : currentOutlet;
  
  return (
    <AnimatePresence
      mode="wait"
      onExitComplete={() => setIsTransitioning(false)}
    >
      <motion.div
        key={location.pathname}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.2 }}
      >
        {content}
      </motion.div>
    </AnimatePresence>
  );
}