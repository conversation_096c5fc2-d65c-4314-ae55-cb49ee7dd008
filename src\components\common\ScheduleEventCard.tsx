import React from 'react';
import BaseEventCard, { BaseEventCardProps } from './BaseEventCard';
import { cn } from '@/lib/utils';
import { ScheduleActionEvent } from '@/services/actionService';
import { format, parseISO } from 'date-fns';

// Helper function (can be moved to utils if used elsewhere)
const formatRemainingTimeDisplay = (timeString: string): React.ReactNode => {
  const [hours, minutes, seconds] = timeString.split(':').map(Number);
  if (hours === 0 && minutes === 0 && seconds === 0) return "Now";
  if (hours > 0) return `in ${hours} ${hours === 1 ? 'hr' : 'hrs'} ${minutes} ${minutes === 1 ? 'min' : 'mins'}`;
  if (minutes > 0) return `in ${minutes} ${minutes === 1 ? 'min' : 'mins'} ${seconds} ${seconds === 1 ? 'sec' : 'secs'}`;
  return `in ${seconds} ${seconds === 1 ? 'sec' : 'secs'}`;
};

// Helper to format device name (can be moved to utils)
const formatDeviceName = (deviceId: string): string => {
  return deviceId.replace('chiller', 'CH').toUpperCase().replace(/_/g, '-');
};

// Define DeviceTag locally or import if moved to a shared location
interface DeviceTag {
  id: string;
  name: string;
}

// Props specific to this card
interface ScheduleEventCardProps {
  event: ScheduleActionEvent;
  isActive?: boolean;
  showHourMarker?: boolean;
  time: string; // HH:mm format
  remainingTime: string; // H:MM:SS format for calculation
  eventStatus: 'pending' | 'in-progress' | 'completed';
  className?: string;
}

const ScheduleEventCard: React.FC<ScheduleEventCardProps> = ({
  event,
  isActive = false,
  showHourMarker = false,
  time,
  remainingTime,
  eventStatus,
  className,
}) => {
  // Calculate specific content within this component
  const deviceData = event.payload.device_datapoint_pair_list || [];
  const title = event.description || 'Scheduled Event';
  const deviceList: DeviceTag[] = deviceData.map(([deviceId]) => ({
    id: deviceId,
    name: formatDeviceName(deviceId),
  }));

  // Calculate planned time range
  let plannedTime: string;
  const scheduledTime = parseISO(event.scheduled_time);
  try {
    const endTime = parseISO(event.payload.end_time); // Use parseISO for reliability
    plannedTime = `${format(scheduledTime, 'HH:mm')} - ${format(endTime, 'HH:mm')}`;
  } catch (e) {
    console.error("Error parsing schedule end_time:", e, event.payload.end_time);
    plannedTime = format(scheduledTime, 'HH:mm'); // Fallback
  }

  return (
    <BaseEventCard
      time={time}
      isActive={isActive}
      showHourMarker={showHourMarker}
      className={className}
    >
      <div className="flex flex-col gap-1.5">
        {/* Top Row - Time Info & Status */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-1">
            {/* Clock Icon */}
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" className={isActive ? "text-card" : "text-muted-foreground"}>
              <path fillRule="evenodd" clipRule="evenodd" d="M2.91666 6.99996C2.91666 4.74474 4.74477 2.91663 6.99999 2.91663C9.25521 2.91663 11.0833 4.74474 11.0833 6.99996C11.0833 9.25518 9.25521 11.0833 6.99999 11.0833C4.74477 11.0833 2.91666 9.25518 2.91666 6.99996ZM6.99999 3.56136C5.10085 3.56136 3.56139 5.10082 3.56139 6.99996C3.56139 8.8991 5.10085 10.4386 6.99999 10.4386C8.89913 10.4386 10.4386 8.8991 10.4386 6.99996C10.4386 5.10082 8.89913 3.56136 6.99999 3.56136Z" fill="currentColor"/>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.00181 4.42114C7.17985 4.42114 7.32418 4.56547 7.32418 4.74351V6.86826L8.42562 7.96983C8.55151 8.09573 8.55149 8.29985 8.42559 8.42573C8.29969 8.55162 8.09558 8.5516 7.9697 8.4257L6.77385 7.22972C6.7134 7.16926 6.67944 7.08727 6.67944 7.00178V4.74351C6.67944 4.56547 6.82377 4.42114 7.00181 4.42114Z" fill="currentColor"/>
            </svg>
            <span className={cn("text-xs", isActive ? "text-card" : "text-muted-foreground")}>
              {plannedTime}
            </span>
          </div>
          {/* Status/Remaining Time */}
          <span className={cn("text-xs font-semibold", isActive ? "text-card" : "text-primary")}>
            {eventStatus === 'pending' ? formatRemainingTimeDisplay(remainingTime) :
              eventStatus === 'in-progress' ? (
                <span className="inline-flex items-center animate-pulse">
                  In Progress
                  <span className="ml-0.5 inline-flex">
                    <span className="animate-bounce delay-0">•</span>
                    <span className="animate-bounce delay-150">•</span>
                    <span className="animate-bounce delay-300">•</span>
                  </span>
                </span>
              ) : (
                'Completed'
              )}
          </span>
        </div>

        {/* Bottom Row - Title and Device List */}
        <div className="flex justify-between items-center">
          <span className={cn("text-sm font-semibold", isActive ? "text-card" : "text-primary")}>
            {title}
          </span>
          <div className="flex flex-wrap items-center gap-1 w-[185px] justify-end">
            {deviceList.map((device) => (
              <div
                key={device.id}
                className={cn(
                  "h-4 px-0.5 rounded inline-flex items-center",
                  isActive ? "bg-[#C0E2FF]" : "bg-card"
                )}
              >
                <span className={cn(
                  "text-[9px] leading-none whitespace-nowrap text-[#052745]"
                )}>
                  {device.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </BaseEventCard>
  );
};

export default ScheduleEventCard;