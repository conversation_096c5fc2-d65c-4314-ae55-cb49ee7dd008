import React, { useState } from 'react';
import { useSavingDashboard } from '../../contexts/SavingDashboardContext';
import ReportingTable from '../ReportingTable';
import ReportingPlot from '../ReportingPlot';
import EnvironmentPlot from '../EnvironmentPlot';
import SummaryReport from '../SummaryReport';
import DataControl from '../DataControl';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import FullscreenSection from '@/components/ui/fullscreen-section';

const ReportingTab: React.FC = () => {
  const { reportingData, summaryStats, variableSelection, regressionResult, generateReport, setActiveTab } = useSavingDashboard();
  const [environmentTabValue, setEnvironmentTabValue] = useState<string>("daily");
  const [reportingPlotTabValue, setReportingPlotTabValue] = useState<string>("scatter");

  // Prepare data for visualization components
  const prepareReportingData = () => {
    if (!reportingData.length || !variableSelection.dependent || !variableSelection.independent.length) {
      return { data: [], multiVarData: [] };
    }

    const data = reportingData.map(row => ({
      x: row[variableSelection.independent[0]],
      y: row[variableSelection.dependent],
      date: row.date
    }));

    // For multi-variable support
    const multiVarData = reportingData.map(row => {
      const point: any = { date: row.date };
      variableSelection.independent.forEach(variable => {
        point[variable] = row[variable];
      });
      point[variableSelection.dependent] = row[variableSelection.dependent];
      return point;
    });

    // Calculate r-squared values for each variable
    const r2Values: {[key: string]: number} = {};
    if (regressionResult && regressionResult.statistics) {
      // Store the total model R²
      r2Values['model'] = regressionResult.statistics.r2 || 0;
      
      // Calculate individual R² values using correlation coefficient for each variable
      variableSelection.independent.forEach(variable => {
        // Filter out any rows where either X or Y is null/undefined/NaN
        const validData = reportingData.filter(row => 
          row[variable] != null && 
          !isNaN(row[variable]) && 
          row[variableSelection.dependent] != null && 
          !isNaN(row[variableSelection.dependent])
        );

        const xValues = validData.map(row => Number(row[variable]));
        const yValues = validData.map(row => Number(row[variableSelection.dependent]));
        
        if (xValues.length === 0 || yValues.length === 0) {
          r2Values[variable] = 0;
          return;
        }

        // Calculate means
        const xMean = xValues.reduce((a, b) => a + b, 0) / xValues.length;
        const yMean = yValues.reduce((a, b) => a + b, 0) / yValues.length;
        
        // Calculate correlation coefficient
        let numerator = 0;
        let denominatorX = 0;
        let denominatorY = 0;
        
        for (let i = 0; i < xValues.length; i++) {
          const xDiff = xValues[i] - xMean;
          const yDiff = yValues[i] - yMean;
          numerator += xDiff * yDiff;
          denominatorX += xDiff * xDiff;
          denominatorY += yDiff * yDiff;
        }
        
        // Avoid division by zero
        if (denominatorX === 0 || denominatorY === 0) {
          r2Values[variable] = 0;
          return;
        }

        const correlation = numerator / Math.sqrt(denominatorX * denominatorY);
        // Round to 4 decimal places to ensure consistency
        r2Values[variable] = Math.round(correlation * correlation * 10000) / 10000;
      });
    }

    return { 
      data, 
      multiVarData,
      xVariables: variableSelection.independent,
      yLabel: variableSelection.dependent,
      r2Values
    };
  };

  const reportingVisData = prepareReportingData();
  
  // If no reporting data is available, show appropriate message or loader
  if (!reportingData.length) {
    return (
      <div className="flex flex-col gap-3">
        <DataControl />
        <div className="flex flex-col items-center justify-center h-[calc(100vh-220px)] bg-white rounded-lg border p-8">
          {regressionResult ? (
            <>
              <h2 className="text-lg font-semibold mb-4">Generating Report</h2>
              <p className="text-gray-500 mb-6 text-center">
                Please wait while we analyze your data and generate the report...
              </p>
              <div className="animate-pulse flex space-x-4">
                <div className="h-2 w-16 bg-gray-200 rounded"></div>
                <div className="h-2 w-16 bg-gray-300 rounded"></div>
                <div className="h-2 w-16 bg-gray-200 rounded"></div>
              </div>
              <Button 
                onClick={(e) => {
                  e.preventDefault();
                  generateReport();
                }} 
                className="px-6 mt-6"
              >
                Generate Report Now
              </Button>
            </>
          ) : (
            <>
              <h2 className="text-lg font-semibold mb-4">No Baseline Model Available</h2>
              <p className="text-gray-500 mb-6 text-center">
                Please configure your baseline model first by selecting variables and date ranges in the Baseline tab.
              </p>
              <Button 
                onClick={() => setActiveTab('baseline')}
                className="px-6"
              >
                Go to Baseline
              </Button>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      <DataControl />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 h-[calc(100vh-220px)]">
        <FullscreenSection title={<span className="text-[#065BA9]">5. Reporting Table</span>}>
          <ReportingTable />
        </FullscreenSection>

        <FullscreenSection 
          title={<span className="text-[#065BA9]">6. Environment Parameter</span>}
          headerContent={
            <div className="flex space-x-1">
              <button 
                className={`text-xs px-2 py-0.5 h-6 rounded-sm ${environmentTabValue === 'daily' ? 'bg-background shadow-sm' : 'bg-card hover:bg-card/80'}`}
                onClick={() => setEnvironmentTabValue('daily')}
              >
                Daily
              </button>
              <button 
                className={`text-xs px-2 py-0.5 h-6 rounded-sm ${environmentTabValue === 'monthly' ? 'bg-background shadow-sm' : 'bg-card hover:bg-card/80'}`}
                onClick={() => setEnvironmentTabValue('monthly')}
              >
                Monthly
              </button>
            </div>
          }
        >
          {(isFullscreen) => (
            <Tabs value={environmentTabValue} onValueChange={setEnvironmentTabValue} className="w-full">
              <TabsList className="hidden">
                <TabsTrigger value="daily">Daily</TabsTrigger>
                <TabsTrigger value="monthly">Monthly</TabsTrigger>
              </TabsList>
              <TabsContent value="daily">
                <EnvironmentPlot period="daily" isFullscreenComponent={isFullscreen} />
              </TabsContent>
              <TabsContent value="monthly">
                <EnvironmentPlot period="monthly" isFullscreenComponent={isFullscreen} />
              </TabsContent>
            </Tabs>
          )}
        </FullscreenSection>

        <FullscreenSection title={<span className="text-[#065BA9]">7. Summary</span>}>
          {summaryStats ? (
            <SummaryReport stats={summaryStats} />
          ) : (
            <div className="p-3 text-center text-gray-500 text-sm">
              No summary statistics available. Please generate a report first.
            </div>
          )}
        </FullscreenSection>

        <FullscreenSection 
          title={<span className="text-[#065BA9]">8. Reporting Plot</span>}
          headerContent={
            <div className="flex space-x-1">
              <button 
                className={`text-xs px-2 py-0.5 h-6 rounded-sm ${reportingPlotTabValue === 'scatter' ? 'bg-background shadow-sm' : 'bg-card hover:bg-card/80'}`}
                onClick={() => setReportingPlotTabValue('scatter')}
              >
                Scatter
              </button>
              <button 
                className={`text-xs px-2 py-0.5 h-6 rounded-sm ${reportingPlotTabValue === 'timeseries' ? 'bg-background shadow-sm' : 'bg-card hover:bg-card/80'}`}
                onClick={() => setReportingPlotTabValue('timeseries')}
              >
                Time Series
              </button>
              <button 
                className={`text-xs px-2 py-0.5 h-6 rounded-sm ${reportingPlotTabValue === 'bar' ? 'bg-background shadow-sm' : 'bg-card hover:bg-card/80'}`}
                onClick={() => setReportingPlotTabValue('bar')}
              >
                Bar
              </button>
            </div>
          }
        >
          {(isFullscreen) => (
            <Tabs value={reportingPlotTabValue} onValueChange={setReportingPlotTabValue} className="w-full">
              <TabsList className="hidden">
                <TabsTrigger value="scatter">Scatter</TabsTrigger>
                <TabsTrigger value="timeseries">Time Series</TabsTrigger>
                <TabsTrigger value="bar">Bar</TabsTrigger>
              </TabsList>
              <TabsContent value="scatter">
                <ReportingPlot type="scatter" isFullscreenComponent={isFullscreen} />
              </TabsContent>
              <TabsContent value="timeseries">
                <ReportingPlot type="timeseries" isFullscreenComponent={isFullscreen} />
              </TabsContent>
              <TabsContent value="bar">
                <ReportingPlot type="bar" isFullscreenComponent={isFullscreen} />
              </TabsContent>
            </Tabs>
          )}
        </FullscreenSection>
      </div>
    </div>
  );
};

export default ReportingTab;