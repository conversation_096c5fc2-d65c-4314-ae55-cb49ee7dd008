import { LineChart } from 'lucide-react';
import { ManualChapter } from '../types';

export const historicalDataContent: ManualChapter = {
  id: 'historical-data',
  title: {
    en: 'Historical Data',
    th: 'Historical Data'
  },
  icon: <LineChart size={18} />,
  sections: [
    {
      id: 'historical-data-overview',
      title: {
        en: 'Historical Data Visualization',
        th: 'การแสดงผลข้อมูลย้อนหลัง'
      },
      content: {
        en: `
          <div class="space-y-4">
            <p>
              The Historical Data Visualization module allows you to access, analyze, and export historical system data.
              This feature is essential for understanding trends, identifying optimization opportunities, and verifying 
              system performance.
            </p>
            
            <h3 class="text-lg font-medium mt-4">Key Features</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li>Flexible date range selection</li>
              <li>Multiple parameter visualization</li>
              <li>Comparative analysis tools</li>
              <li>Data export capabilities</li>
              <li>Custom report generation</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">Data Visualization Tools</h3>
            <p>
              The Historical Data module offers several visualization options:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>Time Series Charts</strong>: Plot data values over time</li>
              <li><strong>Heat Maps</strong>: Visualize data density and patterns</li>
              <li><strong>Scatter Plots</strong>: Analyze relationships between parameters</li>
              <li><strong>Bar and Pie Charts</strong>: Compare categorical data</li>
              <li><strong>Data Tables</strong>: View raw data in tabular format</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">Data Export Options</h3>
            <p>
              Export your historical data in various formats:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li>CSV files for spreadsheet analysis</li>
              <li>PDF reports for documentation and sharing</li>
              <li>Images of charts and visualizations</li>
              <li>API access for integration with other tools</li>
            </ul>
            
            <p class="mt-4">
              All visualizations are interactive, allowing you to zoom, pan, and filter the data for detailed analysis.
            </p>
          </div>
        `,
        th: `
          <div class="space-y-4">
            <p>
              โมดูลการแสดงผลข้อมูลย้อนหลังช่วยให้คุณเข้าถึง วิเคราะห์ และส่งออกข้อมูลระบบในอดีต
              คุณสมบัตินี้มีความสำคัญสำหรับการทำความเข้าใจแนวโน้ม การระบุโอกาสในการเพิ่มประสิทธิภาพ และการตรวจสอบ
              ประสิทธิภาพของระบบ
            </p>
            
            <h3 class="text-lg font-medium mt-4">คุณสมบัติหลัก</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li>การเลือกช่วงวันที่ที่ยืดหยุ่น</li>
              <li>การแสดงผลพารามิเตอร์หลายตัว</li>
              <li>เครื่องมือวิเคราะห์เปรียบเทียบ</li>
              <li>ความสามารถในการส่งออกข้อมูล</li>
              <li>การสร้างรายงานแบบกำหนดเอง</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">เครื่องมือการแสดงผลข้อมูล</h3>
            <p>
              โมดูลข้อมูลย้อนหลังมีตัวเลือกการแสดงผลหลายแบบ:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>กราฟอนุกรมเวลา</strong>: แสดงค่าข้อมูลตามเวลา</li>
              <li><strong>แผนที่ความร้อน</strong>: แสดงความหนาแน่นของข้อมูลและรูปแบบ</li>
              <li><strong>แผนภาพการกระจาย</strong>: วิเคราะห์ความสัมพันธ์ระหว่างพารามิเตอร์</li>
              <li><strong>กราฟแท่งและวงกลม</strong>: เปรียบเทียบข้อมูลตามหมวดหมู่</li>
              <li><strong>ตารางข้อมูล</strong>: ดูข้อมูลดิบในรูปแบบตาราง</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">ตัวเลือกการส่งออกข้อมูล</h3>
            <p>
              ส่งออกข้อมูลย้อนหลังของคุณในรูปแบบต่างๆ:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li>ไฟล์ CSV สำหรับการวิเคราะห์ในสเปรดชีต</li>
              <li>รายงาน PDF สำหรับการจัดทำเอกสารและการแบ่งปัน</li>
              <li>ภาพของกราฟและการแสดงผล</li>
              <li>การเข้าถึง API สำหรับการบูรณาการกับเครื่องมืออื่นๆ</li>
            </ul>
            
            <p class="mt-4">
              การแสดงผลทั้งหมดเป็นแบบโต้ตอบ ทำให้คุณสามารถซูม เลื่อน และกรองข้อมูลสำหรับการวิเคราะห์อย่างละเอียด
            </p>
          </div>
        `
      }
    }
  ]
}; 