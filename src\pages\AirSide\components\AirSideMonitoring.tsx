import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Loader } from "@/components/ui/loader";
import {
  getDevicesWithZones,
  DeviceData,
  GroupsData,
} from "@/services/deviceService";
import { useRealtime } from "@/contexts/RealtimeContext";
import { useAutopilot } from "@/contexts/AutopilotContext";
import manual_icon from '@/assets/manual_icon.svg';

interface AirSideMonitoringProps {
  rowsPerColumn?: number;
  onSelectAhu?: (ahu: DeviceData) => void;
  selectedAhuId?: string;
  devices?: DeviceData[];
  dataMode?: DataMode;
}

// Data display modes - limited to only temperature and damper
type DataMode = "temp" | "damper";

const AirSideMonitoring = ({
  rowsPerColumn = 8,
  onSelectAhu,
  selectedAhuId,
  devices = [],
  dataMode = "temp",
}: AirSideMonitoringProps) => {
  const { getValue } = useRealtime();
  const { isDeviceInAutopilotMode } = useAutopilot();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [groupsData, setGroupsData] = useState<GroupsData>({});
  const [groupNames, setGroupNames] = useState<string[]>([]);

  // Status styles with background and text colors
  const statusStyles = {
    normal: { bg: "bg-[#62D9D4]", dot: "bg-[#62D9D4]", text: "text-[#063E40]" },
    off: { bg: "bg-[#E5E7EB]", dot: "bg-[#D1D5DB]", text: "text-[#9CA3AF]" },
    warning: {
      bg: "bg-[#DFCB28]",
      dot: "bg-[#DFCB28]",
      text: "text-[#521503]",
    },
    alarm: { bg: "bg-[#D73C31]", dot: "bg-[#D73C31]", text: "text-white" },
  };

  // Temperature thresholds for determining status
  const tempThresholds = {
    ahu: {
      warning: { min: 26, max: 28 },
      alarm: { min: 28, max: 32 },
    },
    vav: {
      warning: { min: 26, max: 28 },
      alarm: { min: 28, max: 32 },
    },
  };

  // Damper thresholds for determining status
  const damperThresholds = {
    warning: { min: 80, max: 90 },
    alarm: { min: 90, max: 100 },
  };

  // Function to format VAV device ID
  const formatVavId = (deviceId: string) => {
    // Extract only the part after the last dash
    const parts = deviceId.split("-");
    const lastPart = parts[parts.length - 1];

    // Remove "vav_" prefix if it exists
    // Convert to uppercase and replace underscores with dashes
    return lastPart.replace(/^vav_/i, "").replace(/_/g, "-").toUpperCase();
  };

  // Function to extract numeric part for sorting VAVs
  const getVavSortKey = (deviceId: string) => {
    // Extract level and number from the device ID
    // Format: vav_17_l1_1 -> level: l1, number: 1
    const levelMatch = deviceId.match(/l(\d+)/i);
    const numberMatch = deviceId.match(/_(\d+)$/);

    const level = levelMatch ? parseInt(levelMatch[1], 10) : 999;
    const number = numberMatch ? parseInt(numberMatch[1], 10) : 999;

    // Return a composite key: level * 1000 + number
    // This ensures level is the primary sort key, and number is secondary
    return level * 1000 + number;
  };

  // Get device status based on value thresholds and on/off state
  const determineDeviceStatus = (
    deviceId: string,
    value: number | undefined,
    isAhu: boolean
  ): "normal" | "off" | "warning" | "alarm" => {
    // Read status from latest_data using status_write datapoint
    const statusValue = getValue(deviceId, "status_read");

    // If status_write is 0 or undefined, device is off
    if (statusValue === 0 || statusValue === undefined) {
      return "off";
    }

    // If value is undefined, we can't determine a status
    if (value === undefined) {
      return "normal";
    }

    // For temperature mode
    if (dataMode === "temp") {
      const thresholds = isAhu ? tempThresholds.ahu : tempThresholds.vav;

      if (value >= thresholds.alarm.min) {
        return "alarm";
      } else if (value >= thresholds.warning.min) {
        return "warning";
      } else {
        return "normal";
      }
    }

    // For damper mode
    if (dataMode === "damper") {
      if (value >= damperThresholds.alarm.min) {
        return "alarm";
      } else if (value >= damperThresholds.warning.min) {
        return "warning";
      } else {
        return "normal";
      }
    }

    return "normal";
  };

  // Process devices from props or fetch from API if needed
  useEffect(() => {
    // If devices are provided via props, use them instead of fetching
    if (devices && devices.length > 0) {
      processDevices(devices);
      return;
    }

    // Fetch devices only if none are provided via props
    const fetchDevices = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Use the service to fetch and process data
        const processedData = await getDevicesWithZones();

        // Filter only Air-side devices (AHUs, VSD_AHUs, VAVs)
        const filteredData: GroupsData = {};

        for (const [key, devices] of Object.entries(
          processedData.groupedData
        )) {
          filteredData[key] = devices.filter(
            (device) =>
              device.model.toLowerCase() === "ahu" ||
              device.model.toLowerCase() === "vsd_ahu" ||
              device.model.toLowerCase() === "vav"
          );
        }

        setGroupsData(filteredData);
        setGroupNames(processedData.floorNames);
      } catch (err) {
        console.error("Error fetching device data:", err);
        setError("Failed to fetch device data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDevices();
  }, [devices]); // Re-run when devices prop changes

  // Helper function to process devices from props
  const processDevices = (devices: DeviceData[]) => {
    setIsLoading(true);
    try {
      // Group by floor
      const groupedByFloor: GroupsData = {};
      const floorNames: string[] = [];

      devices.forEach((device) => {
        const floor = device.floor || "Unknown";
        const floorKey = `F${floor}`;

        if (!groupedByFloor[floorKey]) {
          groupedByFloor[floorKey] = [];
          floorNames.push(floorKey);
        }

        groupedByFloor[floorKey].push(device);
      });

      setGroupsData(groupedByFloor);
      setGroupNames(floorNames);
    } catch (err) {
      console.error("Error processing device data:", err);
      setError("Failed to process device data.");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper to check if a device is an AHU type
  const isAhuType = (device: DeviceData) => {
    const model = device.model.toLowerCase();
    return model === "ahu" || model === "vsd_ahu";
  };

  // Get the value based on data mode and device type
  const getDeviceValue = (device: DeviceData): number | undefined => {
    if (isAhuType(device)) {
      // Data for AHUs
      switch (dataMode) {
        case "temp":
          return getValue(device.deviceId, "supply_air_temperature");
        case "damper":
          return getValue(device.deviceId, "damper_position_setpoint_read");
        default:
          return undefined;
      }
    } else {
      // Data for VAVs
      switch (dataMode) {
        case "temp":
          return getValue(device.deviceId, "room_temperature");
        case "damper":
          return getValue(device.deviceId, "damper_position_setpoint_read");
        default:
          return undefined;
      }
    }
  };

  // Get the appropriate data based on data mode
  const getDeviceData = (device: DeviceData) => {
    const value = getDeviceValue(device);

    if (dataMode === "temp") {
      return {
        value,
        unit: "°C",
        label: "Temp",
      };
    } else if (dataMode === "damper") {
      return {
        value,
        unit: "%",
        label: "Damper",
      };
    }

    return { value: undefined, unit: "", label: "" };
  };

  // Handle AHU click
  const handleAhuClick = (ahu: DeviceData) => {
    if (onSelectAhu) {
      onSelectAhu(ahu);
    }
  };

  // Render a floor group
  const renderGroup = (groupId: string, groupData: DeviceData[]) => {
    // No filtering - show all devices
    const filteredDevices = groupData;

    // Skip rendering if no devices pass the filter
    if (filteredDevices.length === 0) return null;

    // Get AHUs for this floor
    const ahus = filteredDevices.filter((device) => isAhuType(device));

    // Skip if no AHUs after filtering
    if (ahus.length === 0) return null;

    // Format floor label properly
    let floorLabel = groupId;
    // Check if this is a floor with format "F8" but should be "Floor 8"
    if (groupId.match(/^F\d+$/)) {
      const floorNumber = groupId.substring(1); // Extract number after "F"
      floorLabel = `Floor ${floorNumber}`;
    } else {
      // Handle other formats like "floor8-17" by replacing dashes with spaces
      floorLabel = groupId.replace(/-/g, " ");
    }

    return (
      <div key={groupId} className="flex items-center gap-[6px] w-full">
        <div className="space-y-4 w-full pl-[30px] relative">
          <div className="text-[#F9FAFB] text-[11px] border bg-[#9CA3AF] rounded-[6px] rotate-[-90deg] text-nowrap absolute top-[50%] left-[-5%] w-[12%]">
            <p className="text-center capitalize">{floorLabel}</p>
          </div>
          {/* Render each AHU with its VAVs */}
          {ahus.map((ahu) => {
            const isSelected = ahu.deviceId === selectedAhuId;
            const ahuData = getDeviceData(ahu);
            const ahuStatus = determineDeviceStatus(
              ahu.deviceId,
              ahuData.value,
              true
            );

            // Get VAVs for this AHU (matching floor)
            const vavs = filteredDevices.filter(
              (device) =>
                device.model.toLowerCase() === "vav" &&
                device.floor === ahu.floor
            );

            // Sort VAVs
            const sortedVavs = vavs.sort(
              (a, b) => getVavSortKey(a.deviceId) - getVavSortKey(b.deviceId)
            );

            return (
              <div
                key={ahu.deviceId}
                className={`mr-4 flex border-2 bg-[#F9FAFF] rounded-lg overflow-hidden ${
                  isSelected ? "border-[#8BC6FF] shadow-sm" : "border-[#E5E7EB]"
                }`}
                onClick={() => handleAhuClick(ahu)}
              >
                {/* AHU Header */}
                <div
                  className={`flex flex-col items-center justify-center p-3 cursor-pointer ${
                    isSelected ? "bg-[#F9FAFF]" : "bg-white"
                  } hover:bg-blue-50 transition-colors border-b border-slate-200`}
                >
                  <div className="flex items-center gap-1">
                    <div
                      className={`w-3 h-3 rounded-full ${statusStyles[ahuStatus].dot}`}
                    ></div>
                    <span className="text-[12px] text-[#212529] font-semibold uppercase text-nowrap">
                      {ahu.deviceId.split("_")[0]} {ahu.deviceId.split("_")[1]}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-[#065BA9] text-[10px] font-medium flex items-center gap-1">
                      {!isDeviceInAutopilotMode(ahu.deviceId) && 
                        <img src={manual_icon} alt="Manual Mode" className="w-[20px] h-[20px]" />
                      }
                      Detail
                    </span>
                  </div>
                </div>

                {/* VAVs Grid */}
                <div className="py-1 px-[6px]">
                  {vavs.length > 0 ? (
                    <div className="grid grid-cols-4 md:grid-cols-8 lg:grid-cols-12 xl:grid-cols-16 gap-2">
                      {/* Render all sorted VAVs in a single grid */}
                      {sortedVavs.map((vav) => {
                        const vavData = getDeviceData(vav);
                        const vavStatus = determineDeviceStatus(
                          vav.deviceId,
                          vavData.value,
                          false
                        );
                        const vavStyle = statusStyles[vavStatus];
                        const vavIdParts = formatVavId(vav.deviceId);

                        return (
                          <TooltipProvider key={vav.deviceId}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div
                                  className={`${vavStyle.bg} rounded p-1.5 flex flex-col justify-center items-center shadow-sm aspect-square w-full`}
                                >
                                  <div
                                    className={`text-center text-xs font-medium ${vavStyle.text}`}
                                  >
                                    {" "}
                                    VAV{" "}
                                  </div>
                                  <div
                                    className={`text-center text-[10px] ${vavStyle.text} leading-tight mt-0.5`}
                                  >
                                    {" "}
                                    {vavIdParts}{" "}
                                  </div>
                                  <div
                                    className={`text-center text-[9px] px-[4px] py-[2px] rounded-[5px] mt-0.5 text-[#0E7EE4] bg-white`}
                                  >
                                    {vavData.value !== undefined
                                      ? Number(vavData.value).toFixed(1)
                                      : "--"}
                                    <span className={`text-[9px] opacity-80`}>
                                      {" "}
                                      {vavData.unit}{" "}
                                    </span>
                                  </div>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-sm">
                                  <p>
                                    <strong>VAV {vavIdParts}</strong>
                                  </p>
                                  <p>Status: {vavStatus}</p>
                                  <p>
                                    {vavData.label}:{" "}
                                    {vavData.value !== undefined
                                      ? Number(vavData.value).toFixed(1)
                                      : "--"}{" "}
                                    {vavData.unit}
                                  </p>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center text-slate-500 py-3 text-sm">
                      No VAV devices for this AHU
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Sort and render floors by number
  const renderSortedFloors = () => {
    // Get floor groups with their number
    const floorGroups = Object.entries(groupsData)
      .filter(([_, groupData]) => groupData.length > 0) // Skip empty groups
      .map(([groupId, groupData]) => {
        // Extract floor number (e.g., "F1-Z1" -> 1)
        const floorMatch = groupId.match(/F(\d+)/);
        const floorNumber = floorMatch ? parseInt(floorMatch[1], 10) : 999; // Default high number if not found
        return { floorNumber, groupId, groupData };
      })
      // Sort by floor number (lowest first)
      .sort((a, b) => a.floorNumber - b.floorNumber);

    return floorGroups.map(({ groupId, groupData }) =>
      renderGroup(groupId, groupData)
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[30vh]">
        <Loader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-rose-500 bg-rose-50 rounded-md">
        <p className="font-medium">Error: {error}</p>
      </div>
    );
  }

  // If no data
  if (Object.keys(groupsData).length === 0) {
    return (
      <div className="p-6 text-slate-500 text-center bg-slate-50 rounded-md">
        <p>No device data available for monitoring.</p>
      </div>
    );
  }

  return (
    <div>
      {/* Floor and Zone Display - sorted by floor number */}
      <div className="flex flex-col">{renderSortedFloors()}</div>
    </div>
  );
};

export default AirSideMonitoring;
