import { ComponentConfig } from '../components/types';

interface MapConfig {
  zoomLevel: number;
  xOffset: number;
  yOffset: number;
}

interface Config {
  overlayItems: ComponentConfig[];
  toolbarPosition: {
    x: number;
    y: number;
  };
  mapConfig: MapConfig;
}

const DEFAULT_CONFIG: Config = {
  overlayItems: [],
  toolbarPosition: { x: 90, y: 90 },
  mapConfig: {
    zoomLevel: 1,
    xOffset: 0,
    yOffset: 0,
  }
};

export default class ConfigService {
  private static API_URL = 'http://localhost:6969/api';
  private static CONFIG_KEY = 'map_config';

  private static getLocalConfig(): Config {
    try {
      const storedConfig = localStorage.getItem(this.CONFIG_KEY);
      if (!storedConfig) return DEFAULT_CONFIG;

      const parsedConfig = JSON.parse(storedConfig);
      // Ensure mapConfig exists with all required properties
      return {
        ...DEFAULT_CONFIG,
        ...parsedConfig,
        mapConfig: {
          ...DEFAULT_CONFIG.mapConfig,
          ...(parsedConfig.mapConfig || {}),
        },
      };
    } catch (error) {
      console.error('Error parsing local config:', error);
      return DEFAULT_CONFIG;
    }
  }

  private static setLocalConfig(config: Config): void {
    try {
      // Ensure mapConfig is properly structured before saving
      const configToSave = {
        ...config,
        mapConfig: {
          zoomLevel: config.mapConfig?.zoomLevel ?? DEFAULT_CONFIG.mapConfig.zoomLevel,
          xOffset: config.mapConfig?.xOffset ?? DEFAULT_CONFIG.mapConfig.xOffset,
          yOffset: config.mapConfig?.yOffset ?? DEFAULT_CONFIG.mapConfig.yOffset,
        },
      };
      localStorage.setItem(this.CONFIG_KEY, JSON.stringify(configToSave));
    } catch (error) {
      console.error('Error saving local config:', error);
    }
  }

  private static async fetchConfig(): Promise<Config> {
    try {
      // Always use local storage first
      const localConfig = this.getLocalConfig();
      
      // Try to fetch from server in background
      const response = await fetch(`${this.API_URL}/map/config`);
      if (response.ok) {
        const serverConfig = await response.json();
        // Merge server config with defaults to ensure all properties exist
        const mergedConfig = {
          ...DEFAULT_CONFIG,
          ...serverConfig,
          mapConfig: {
            ...DEFAULT_CONFIG.mapConfig,
            ...(serverConfig.mapConfig || {}),
          },
        };
        this.setLocalConfig(mergedConfig);
        return mergedConfig;
      }
      
      return localConfig;
    } catch (error) {
      console.warn('Failed to fetch config from server, using local storage:', error);
      return this.getLocalConfig();
    }
  }

  private static async updateConfig(config: Config): Promise<void> {
    // Ensure mapConfig is properly structured
    const configToUpdate = {
      ...config,
      mapConfig: {
        zoomLevel: config.mapConfig?.zoomLevel ?? DEFAULT_CONFIG.mapConfig.zoomLevel,
        xOffset: config.mapConfig?.xOffset ?? DEFAULT_CONFIG.mapConfig.xOffset,
        yOffset: config.mapConfig?.yOffset ?? DEFAULT_CONFIG.mapConfig.yOffset,
      },
    };

    // Always update local storage first
    this.setLocalConfig(configToUpdate);

    try {
      console.log(configToUpdate);
      const response = await fetch(`${this.API_URL}/map/config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(configToUpdate),
      });
      
      if (!response.ok) {
        console.warn('Failed to save to server, saved locally only');
      }
    } catch (error) {
      console.warn('Failed to save to server, saved locally only:', error);
    }
  }

  static async saveComponents(components: ComponentConfig[]): Promise<void> {
    try {
      const config = await this.fetchConfig();
      config.overlayItems = components;
      await this.updateConfig(config);
      console.log('Saved components to backend:', components);
    } catch (error) {
      console.error('Failed to save components:', error);
      throw error;
    }
  }

  static async loadComponents(): Promise<ComponentConfig[]> {
    try {
      const config = await this.fetchConfig();
      return config.overlayItems;
    } catch (error) {
      console.error('Failed to load components:', error);
      return [];
    }
  }

  static async saveToolbarPosition(position: { x: number; y: number }): Promise<void> {
    try {
      const config = await this.fetchConfig();
      config.toolbarPosition = position;
      await this.updateConfig(config);
      console.log('Saved toolbar position to backend:', position);
    } catch (error) {
      console.error('Failed to save toolbar position:', error);
      throw error;
    }
  }

  static async loadToolbarPosition(): Promise<{ x: number; y: number } | null> {
    try {
      const config = await this.fetchConfig();
      return config.toolbarPosition;
    } catch (error) {
      console.error('Failed to load toolbar position:', error);
      return null;
    }
  }

  static async saveMapConfig(mapConfig: MapConfig): Promise<void> {
    try {
      const config = await this.fetchConfig();
      config.mapConfig = mapConfig;
      await this.updateConfig(config);
      console.log('Saved map config to backend:', mapConfig);
    } catch (error) {
      console.error('Failed to save map config:', error);
      throw error;
    }
  }

  static async loadMapConfig(): Promise<MapConfig | null> {
    try {
      const config = await this.fetchConfig();
      return config.mapConfig;
    } catch (error) {
      console.error('Failed to load map config:', error);
      return null;
    }
  }
} 