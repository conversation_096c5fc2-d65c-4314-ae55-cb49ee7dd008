import { <PERSON>Alert } from 'lucide-react';
import { ManualChapter } from '../types';

export const afddContent: ManualChapter = {
  id: 'afdd',
  title: {
    en: 'AFDD',
    th: 'AFDD'
  },
  icon: <TriangleAlert size={18} />,
  sections: [
    {
      id: 'afdd-overview',
      title: {
        en: 'AFDD',
        th: 'ระบบตรวจจับและวินิจฉัยข้อผิดพลาดอัตโนมัติ'
      },
      content: {
        en: `
          <div class="space-y-4">
            <p>
              The Automated Fault Detection and Diagnostics (AFDD) module continuously monitors system operation to 
              identify potential issues before they become serious problems. It uses advanced algorithms to detect
              abnormal conditions and diagnose their causes.
            </p>
            
            <h3 class="text-lg font-medium mt-4">Key Features</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li>Real-time fault detection</li>
              <li>Automated diagnostic analysis</li>
              <li>Root cause identification</li>
              <li>Severity classification</li>
              <li>Recommended remediation actions</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">Fault Management</h3>
            <p>
              The AFDD module provides tools to manage detected faults:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>Fault Dashboard</strong>: View and prioritize active faults</li>
              <li><strong>Diagnostic Details</strong>: Access detailed information about each fault</li>
              <li><strong>Resolution Tracking</strong>: Document troubleshooting and resolution actions</li>
              <li><strong>Historical Analysis</strong>: Review past fault patterns and resolutions</li>
              <li><strong>Alert Configuration</strong>: Set up notification preferences for different fault types</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">Diagnostic Rules</h3>
            <p>
              The AFDD system uses a comprehensive set of rules to detect different fault types:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li>Sensor failures and calibration issues</li>
              <li>Equipment performance degradation</li>
              <li>Control sequence failures</li>
              <li>Energy efficiency issues</li>
              <li>Comfort and indoor air quality problems</li>
            </ul>
          </div>
        `,
        th: `
          <div class="space-y-4">
            <p>
              โมดูลตรวจจับและวินิจฉัยข้อผิดพลาดอัตโนมัติ (AFDD) ตรวจสอบการทำงานของระบบอย่างต่อเนื่องเพื่อ
              ระบุปัญหาที่อาจเกิดขึ้นก่อนที่จะกลายเป็นปัญหาร้ายแรง โดยใช้อัลกอริทึมขั้นสูงในการตรวจจับ
              สภาวะผิดปกติและวินิจฉัยสาเหตุ
            </p>
            
            <h3 class="text-lg font-medium mt-4">คุณสมบัติหลัก</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li>การตรวจจับข้อผิดพลาดแบบเรียลไทม์</li>
              <li>การวิเคราะห์การวินิจฉัยอัตโนมัติ</li>
              <li>การระบุสาเหตุที่แท้จริง</li>
              <li>การจำแนกความรุนแรง</li>
              <li>คำแนะนำการแก้ไขที่แนะนำ</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">การจัดการข้อผิดพลาด</h3>
            <p>
              โมดูล AFDD มีเครื่องมือสำหรับจัดการข้อผิดพลาดที่ตรวจพบ:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>แดชบอร์ดข้อผิดพลาด</strong>: ดูและจัดลำดับความสำคัญของข้อผิดพลาดที่เกิดขึ้น</li>
              <li><strong>รายละเอียดการวินิจฉัย</strong>: เข้าถึงข้อมูลโดยละเอียดเกี่ยวกับข้อผิดพลาดแต่ละรายการ</li>
              <li><strong>การติดตามการแก้ไข</strong>: บันทึกการแก้ไขปัญหาและการดำเนินการแก้ไข</li>
              <li><strong>การวิเคราะห์ประวัติ</strong>: ตรวจสอบรูปแบบข้อผิดพลาดในอดีตและการแก้ไข</li>
              <li><strong>การกำหนดค่าการแจ้งเตือน</strong>: ตั้งค่าการแจ้งเตือนสำหรับข้อผิดพลาดประเภทต่างๆ</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">กฎการวินิจฉัย</h3>
            <p>
              ระบบ AFDD ใช้ชุดกฎที่ครอบคลุมในการตรวจจับข้อผิดพลาดประเภทต่างๆ:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li>ความล้มเหลวของเซ็นเซอร์และปัญหาการสอบเทียบ</li>
              <li>การเสื่อมสภาพของประสิทธิภาพอุปกรณ์</li>
              <li>ความล้มเหลวของลำดับการควบคุม</li>
              <li>ปัญหาประสิทธิภาพพลังงาน</li>
              <li>ปัญหาความสบายและคุณภาพอากาศภายในอาคาร</li>
            </ul>
          </div>
        `
      }
    }
  ]
}; 