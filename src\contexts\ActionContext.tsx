import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/utils/supabase';
import { getSiteId } from '@/services/authService';
import { useRealtime } from './RealtimeContext';
import { AnyActionEvent, fetchActionEvents } from '@/services/actionService';

// Interface for an action queue item based on the schema in the image
interface ActionQueueItem {
  device_id: string;
  datapoint: string;
  value: number;
  command_type: string;
  execute_at: string;
  priority: number;
  source: string;
  status: string;
  created_at: string;
  updated_at: string;
  passing_conditions: any;
  reconsider_policy: any;
  site_id: string;
  action_group_id?: number;
}

interface ActionContextType {
  pendingActions: ActionQueueItem[];
  upcomingActions: ActionQueueItem[];
  processingActions: ActionQueueItem[];
  groupActionEvents: AnyActionEvent[];
  pendingGroupActionEvents: AnyActionEvent[];
  inProgressGroupActionEvents: AnyActionEvent[];
  completedGroupActionEvents: AnyActionEvent[];
  getActionsForDevice: (deviceId: string) => ActionQueueItem[];
  getActionsForDeviceDatapoint: (deviceId: string, datapoint: string) => ActionQueueItem[];
  isTransitioning: (deviceId: string, datapoint: string) => boolean;
}

const ActionContext = createContext<ActionContextType>({
  pendingActions: [],
  upcomingActions: [],
  processingActions: [],
  groupActionEvents: [],
  pendingGroupActionEvents: [],
  inProgressGroupActionEvents: [],
  completedGroupActionEvents: [],
  getActionsForDevice: () => [],
  getActionsForDeviceDatapoint: () => [],
  isTransitioning: () => false,
});

export const useActions = () => useContext(ActionContext);

export const ActionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [pendingActions, setPendingActions] = useState<ActionQueueItem[]>([]);
  const [upcomingActions, setUpcomingActions] = useState<ActionQueueItem[]>([]);
  const [processingActions, setProcessingActions] = useState<ActionQueueItem[]>([]);
  const [groupActionEvents, setGroupActionEvents] = useState<AnyActionEvent[]>([]);
  const [pendingGroupActionEvents, setPendingGroupActionEvents] = useState<AnyActionEvent[]>([]);
  const [inProgressGroupActionEvents, setInProgressGroupActionEvents] = useState<AnyActionEvent[]>([]);
  const [completedGroupActionEvents, setCompletedGroupActionEvents] = useState<AnyActionEvent[]>([]);
  const siteId = getSiteId();
  const { getValue } = useRealtime();

  // Fetch initial action queue data and subscribe to changes
  useEffect(() => {
    if (!siteId) {
      console.error('No site_id found for action queue subscription');
      return;
    }

    // Function to fetch actions that are pending or upcoming
    const fetchActions = async () => {
      const now = new Date().toISOString();

      // Fetch pending actions (execute_at has passed)
      const { data: pendingData, error: pendingError } = await supabase
        .from('action_queue')
        .select('*')
        .eq('site_id', siteId)
        .eq('status', 'pending')
        .lte('execute_at', now)
        .order('execute_at', { ascending: true });

      if (pendingError) {
        console.error('Error fetching pending actions:', pendingError);
      } else {
        setPendingActions(pendingData || []);
      }

      // Fetch upcoming actions (execute_at is in the future)
      const { data: upcomingData, error: upcomingError } = await supabase
        .from('action_queue')
        .select('*')
        .eq('site_id', siteId)
        .eq('status', 'pending')
        .gt('execute_at', now)
        .order('execute_at', { ascending: true });

      if (upcomingError) {
        console.error('Error fetching upcoming actions:', upcomingError);
      } else {
        setUpcomingActions(upcomingData || []);
      }
      
      // Fetch processing actions
      const { data: processingData, error: processingError } = await supabase
        .from('action_queue')
        .select('*')
        .eq('site_id', siteId)
        .eq('status', 'processing')
        .order('execute_at', { ascending: true });

      if (processingError) {
        console.error('Error fetching processing actions:', processingError);
      } else {
        setProcessingActions(processingData || []);
      }
    };

    // Initial fetch
    fetchActions();

    // Subscribe to changes in the action_queue table
    const channel = supabase
      .channel('action_queue_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'action_queue',
          filter: `site_id=eq.${siteId}`,
        },
        (payload) => {
          // Refresh the action lists when any change occurs
          fetchActions();
        }
      )
      .subscribe((status) => {
        console.log('Action queue subscription status:', status);
      });

    // Cleanup subscription on unmount
    return () => {
      console.log('Unsubscribing from action queue updates');
      channel.unsubscribe();
    };
  }, [siteId]);

  // Fetch MongoDB action events
  useEffect(() => {
    const fetchMongoDBActionEvents = async () => {
      try {
        const events = await fetchActionEvents();
        setGroupActionEvents(events);
        
        // Filter pending and completed events
        setPendingGroupActionEvents(events.filter(event => event.status === 'pending'));
        setInProgressGroupActionEvents(events.filter(event => event.status === 'in-progress'));
        setCompletedGroupActionEvents(events.filter(event => event.status === 'completed'));
      } catch (error) {
        console.error('Error fetching MongoDB action events:', error);
      }
    };

    // Initial fetch
    fetchMongoDBActionEvents();

    // Set up interval to refresh data every 30 seconds
    const intervalId = setInterval(fetchMongoDBActionEvents, 5000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  // Helper function to get actions for a specific device
  const getActionsForDevice = (deviceId: string): ActionQueueItem[] => {
    return [...pendingActions, ...processingActions, ...upcomingActions].filter(
      action => action.device_id === deviceId
    );
  };

  // Helper function to get actions for a specific device and datapoint
  const getActionsForDeviceDatapoint = (deviceId: string, datapoint: string): ActionQueueItem[] => {
    return [...pendingActions, ...processingActions, ...upcomingActions].filter(
      action => action.device_id === deviceId && action.datapoint === datapoint
    );
  };

  // Helper function to check if a device/datapoint is currently transitioning
  // (has pending actions that have passed their execute_at time AND the current read value doesn't match the action value)
  const isTransitioning = (deviceId: string, datapoint: string): boolean => {
    // If the datapoint is a write datapoint, get the corresponding read datapoint
    const readDatapoint = datapoint.includes('_write') ? datapoint.replace('_write', '_read') : datapoint;

    // Find actions for this device and datapoint from both pending and processing actions
    const relevantActions = [...pendingActions, ...processingActions].filter(
      action => action.device_id === deviceId && action.datapoint === datapoint
    );
    
    // If no actions, not transitioning
    if (relevantActions.length === 0) {
      return false;
    }
    
    // Get the latest action (assuming they're sorted by execute_at in ascending order)
    const latestAction = relevantActions[relevantActions.length - 1];
    
    // Get the current value from the realtime context
    const currentValue = getValue(deviceId, readDatapoint);
    
    // Consider it transitioning if the current value is different from the action value
    // or if we can't get the current value (null/undefined)
    return currentValue === null || currentValue === undefined || currentValue !== latestAction.value;
  };

  return (
    <ActionContext.Provider
      value={{
        pendingActions,
        upcomingActions,
        processingActions,
        groupActionEvents,
        pendingGroupActionEvents,
        inProgressGroupActionEvents,
        completedGroupActionEvents,
        getActionsForDevice,
        getActionsForDeviceDatapoint,
        isTransitioning,
      }}
    >
      {children}
    </ActionContext.Provider>
  );
};