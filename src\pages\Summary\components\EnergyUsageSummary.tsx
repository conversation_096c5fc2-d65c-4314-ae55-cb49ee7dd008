import React from "react";
import SystemSummary from "./SystemSummary";

interface EnergyUsageSummaryProps {
  plantPower: number;
  airPower: number;
  totalPower?: number;
}

const EnergyUsageSummary: React.FC<EnergyUsageSummaryProps> = ({
  plantPower = 0,
  airPower = 0,
  totalPower,
}) => {
  return (
    <SystemSummary
      title="Accumulated Energy"
      airSideValue={airPower}
      plantValue={plantPower}
      totalValue={totalPower}
      unit="kWh"
      decimalPlaces={0}
    />
  );
};

export default EnergyUsageSummary;
