import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from "@/components/ui/card";
import { DateTime } from "luxon";
import BoxPlot from "./BoxPlot";
import ScatterPlot from "./ScatterPlot";
import { useAuth } from "@/contexts/AuthContext";
import { WeatherDataItem, TemperatureData, DataPoint, OriginalDataPoint } from "@/types/summaryTypes";
import { fetchAggregatedData, fetchDailyWeatherData } from "@/services/timescaleService";
import { getSiteId } from "@/services/authService";
import { DateRange } from "react-day-picker";
import { Loader } from "@/components/ui/loader";

interface FilteredData {
  temperatureData: TemperatureData[];
  temperatureDates: string[];
  displayDates: string[];
  weatherData: WeatherDataItem[];
}

interface ComparisonByDateProps {
  temperatureData?: TemperatureData[];
  temperatureDates?: string[];
  scatterData?: OriginalDataPoint[]; // Note this is original format
  allDates?: string[];
  weatherData?: WeatherDataItem[]; // Add weather data from API
  dateRange?: DateRange; // Add dateRange prop
}

const ComparisonByDate: React.FC<ComparisonByDateProps> = ({
  temperatureData: initialTemperatureData = [],
  temperatureDates: initialTemperatureDates = [],
  scatterData: initialScatterData = [],
  allDates: initialAllDates = [],
  weatherData: initialWeatherData = [],
  dateRange,
}) => {
  const [filteredData, setFilteredData] = useState<FilteredData>({
    temperatureData: initialTemperatureData,
    temperatureDates: initialTemperatureDates,
    displayDates: initialAllDates.map((d) => d.split("-").slice(1).join("-")),
    weatherData: initialWeatherData,
  });
  
  // Get timezone from auth context
  const { site } = useAuth();
  const timezone = site?.timezone || 'Asia/Bangkok';
  const today = DateTime.now().setZone(timezone);
  const yesterday = today.minus({ days: 1 });

  // Use dateRange prop if provided, otherwise use default values
  const [startDate, setStartDate] = useState<Date | null>(
    dateRange?.from || yesterday.toJSDate()
  );
  const [endDate, setEndDate] = useState<Date | null>(
    dateRange?.to || today.toJSDate()
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Update local dates when dateRange prop changes
  useEffect(() => {
    if (dateRange?.from && dateRange?.to) {
      setStartDate(dateRange.from);
      setEndDate(dateRange.to);
      fetchData(dateRange.from, dateRange.to);
    }
  }, [dateRange]);
  
  // Local state for our fetched data - initialize arrays even if props are undefined
  const [temperatureData, setTemperatureData] = useState<TemperatureData[]>(initialTemperatureData || []);
  const [temperatureDates, setTemperatureDates] = useState<string[]>(initialTemperatureDates || []);
  const [scatterData, setScatterData] = useState<OriginalDataPoint[]>(initialScatterData || []);
  const [allDates, setAllDates] = useState<string[]>(initialAllDates || []);
  const [weatherData, setWeatherData] = useState<WeatherDataItem[]>(initialWeatherData || []);

  // Fetch data based on selected date range
  const fetchData = async (start: Date, end: Date) => {
    if (!start || !end) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const siteId = getSiteId() || '';
      
      // Convert dates to timezone-aware DateTime objects and then to strings
      const startDateTime = DateTime.fromJSDate(start).setZone(timezone).startOf('day');
      const endDateTime = DateTime.fromJSDate(end).setZone(timezone).endOf('day');
      const startIso = startDateTime.toString();
      const endIso = endDateTime.toString();
      
      // Fetch all data in parallel
      const [weatherResponse, efficiencyResponse, airResponse] = await Promise.all([
        // Weather data
        fetchDailyWeatherData({
          site_id: siteId,
          start_timestamp: startIso,
          end_timestamp: endIso
        }),
        // Efficiency and cooling rate data
        fetchAggregatedData({
          site_id: siteId,
          device_id: 'plant',
          datapoints: ['efficiency', 'cooling_rate'],
          start_timestamp: startIso,
          end_timestamp: endIso
        }),
        // Air side data
        fetchAggregatedData({
          site_id: siteId,
          device_id: 'air_distribution_system',
          datapoints: ['efficiency'],
          start_timestamp: startIso,
          end_timestamp: endIso
        })
      ]);
      
      // Process weather data for temperature box plot
      const newTemperatureData: TemperatureData[] = [];
      const newTemperatureDates: string[] = [];
      const newAllDates: string[] = [];
      
      if (weatherResponse.success && weatherResponse.status === 200 && weatherResponse.data.length > 0) {
        // Process weather data
        weatherResponse.data.forEach(weatherData => {
          const date = new Date(weatherData.timestamp);
          
          // Format date as YYYY-MM-DD
          const formattedDate = date.toISOString().split('T')[0];
          newAllDates.push(formattedDate);
          
          // Format date as MMM DD
          const month = date.toLocaleString('en-US', { month: 'short' });
          const day = date.getDate();
          newTemperatureDates.push(`${month} ${day}`);
          
          // Create temperature data from actual values
          newTemperatureData.push({
            min: weatherData.min_drybulb_temperature,
            q1: (weatherData.min_drybulb_temperature + weatherData.median_drybulb_temperature) / 2,
            median: weatherData.median_drybulb_temperature,
            q3: (weatherData.max_drybulb_temperature + weatherData.median_drybulb_temperature) / 2,
            max: weatherData.max_drybulb_temperature
          });
        });
      }
      
      // Process scatter plot data
      const newScatterData: OriginalDataPoint[] = [];
      
      if (efficiencyResponse.success) {
        const plantEfficiencyData = efficiencyResponse.data.find(item => item.datapoint === 'efficiency')?.values || [];
        const airEfficiencyData = airResponse.data.find(item => item.datapoint === 'efficiency')?.values || [];
        const coolingData = efficiencyResponse.data.find(item => item.datapoint === 'cooling_rate')?.values || [];
        
        // Create a map to match efficiency and cooling data by timestamp
        const dataByTimestamp = new Map();
        
        // First organize cooling data by timestamp
        coolingData.forEach((item: any) => {
          dataByTimestamp.set(item.timestamp, { 
            cooling: item.value,
            efficiency: 0
          });
        });
        
        // Then add plant efficiency data
        plantEfficiencyData.forEach((item: any) => {
          if (dataByTimestamp.has(item.timestamp)) {
            const data = dataByTimestamp.get(item.timestamp);
            data.efficiency += item.value; // Add plant efficiency
          } else {
            dataByTimestamp.set(item.timestamp, {
              cooling: 0,
              efficiency: item.value
            });
          }
        });
        
        // Then add air efficiency data
        airEfficiencyData.forEach((item: any) => {
          if (dataByTimestamp.has(item.timestamp)) {
            const data = dataByTimestamp.get(item.timestamp);
            data.efficiency += item.value; // Add air efficiency
          } else {
            dataByTimestamp.set(item.timestamp, {
              cooling: 0,
              efficiency: item.value
            });
          }
        });
        
        // Then create the scatter data points
        dataByTimestamp.forEach((data, timestamp) => {
          const date = new Date(timestamp);
          
          // Format as MM-DD
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const formattedDate = `${month}-${day}`;
          
          newScatterData.push({
            coolingLoad: data.cooling,
            tse: data.efficiency, // Use provided efficiency value
            date: formattedDate
          });
        });
      }
      
      // Update state with the new data
      setTemperatureData(newTemperatureData);
      setTemperatureDates(newTemperatureDates);
      setScatterData(newScatterData);
      setAllDates(newAllDates);
      setWeatherData(weatherResponse.success && weatherResponse.status === 200 ? weatherResponse.data : []);
      
      // Also update filtered data
      setFilteredData({
        temperatureData: newTemperatureData,
        temperatureDates: newTemperatureDates,
        displayDates: newAllDates.map((d) => d.split("-").slice(1).join("-")),
        weatherData: weatherResponse.success && weatherResponse.status === 200 ? weatherResponse.data : []
      });
      
    } catch (error) {
      console.error("Error fetching data:", error);
      setError("Failed to fetch data. Please try a smaller date range or try again later.");
    } finally {
      setLoading(false);
    }
  };

  // Filter original scatter data based on selected date range
  const filteredScatterData = useMemo(() => {
    if (!startDate || !endDate) return scatterData;
    
    // Convert start/end dates to Luxon DateTime objects for easier comparison
    const start = DateTime.fromJSDate(startDate).setZone(timezone);
    const end = DateTime.fromJSDate(endDate).setZone(timezone);
    
    return scatterData.filter((point) => {
      if (!point.date) return false;

      // Parse date from point (assuming format like "02-26")
      const parts = point.date.split("-");
      if (parts.length !== 2) return false;
      
      const month = parseInt(parts[0]); // 1-indexed month
      const day = parseInt(parts[1]);
      if (isNaN(month) || isNaN(day)) return false;

      // Create a Luxon DateTime object for comparison
      // Use the same year as startDate by default
      let pointDate = DateTime.local(start.year, month, day).setZone(timezone);
      
      // Handle cross-year scenarios (e.g., Dec 2023 to Jan 2024)
      // If the month is earlier than start date's month and end date is in a later year,
      // assume the point date is in the end date's year
      if (month < start.month && end.year > start.year) {
        pointDate = pointDate.set({ year: end.year });
      }
      
      // Handle year-end edge case when filtering Dec to Jan
      if (start.month > end.month && month <= end.month) {
        // If start month is later in year than end month, and point month is in early months,
        // assume it belongs to the following year
        pointDate = pointDate.set({ year: end.year });
      }
      
      // Compare the constructed date with start and end dates
      return pointDate > start.minus({ days: 1 }) && 
             pointDate < end.plus({ days: 1 });
    });
  }, [scatterData, startDate, endDate]);

  // Transform scatterData to the format expected by ScatterPlot component
  const transformedScatterData = useMemo(() => {
    // Group data by date
    const groupedByDate: Record<string, OriginalDataPoint[]> = {};
    
    filteredScatterData.forEach((point: OriginalDataPoint) => {
      const date = point.date || 'Unknown';
      if (!groupedByDate[date]) {
        groupedByDate[date] = [];
      }
      groupedByDate[date].push(point);
    });
    
    // Convert grouped data to DataPoint format (with arrays)
    return Object.entries(groupedByDate).map(([date, points]) => {
      const coolingLoadArray: number[] = points.map((p: OriginalDataPoint) => p.coolingLoad);
      const tseArray: number[] = points.map((p: OriginalDataPoint) => p.tse);
      
      return {
        date,
        coolingLoad: coolingLoadArray,
        tse: tseArray
      } as DataPoint;
    });
  }, [filteredScatterData]);

  // Fetch initial data on mount if no dateRange prop is provided
  useEffect(() => {
    // If we have empty data and no dateRange, fetch the last 2 days of data
    if (temperatureData.length === 0 && scatterData.length === 0 && !dateRange) {
      const end = new Date();
      const start = new Date();
      start.setDate(end.getDate() - 2); // Fetch just 2 days initially to avoid timeout
      
      fetchData(start, end);
    }
  }, []);

  // Filter data based on selected date range
  const getFilteredData = () => {
    // If dates are invalid, return all data
    if (!startDate || !endDate) {
      return {
        temperatureData,
        temperatureDates,
        displayDates: allDates.map((d) => d.split("-").slice(1).join("-")),
        weatherData,
      };
    }

    // Convert to Luxon DateTime for more robust date handling
    const start = DateTime.fromJSDate(startDate).setZone(timezone);
    const end = DateTime.fromJSDate(endDate).setZone(timezone);

    // Filter temperature dates (they are in format like "Feb 26")
    const months = [
      "Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec",
    ];

    // Ensure arrays before processing
    if (!Array.isArray(temperatureDates) || !Array.isArray(weatherData)) {
      return {
        temperatureData: [],
        temperatureDates: [],
        displayDates: [],
        weatherData: [],
      };
    }

    const filteredTempIndices = temperatureDates
      .map((dateStr, index) => {
        // Parse date like "Feb 26"
        const parts = dateStr.split(" ");
        if (parts.length !== 2) return -1;
        
        const month = months.indexOf(parts[0]) + 1; // Convert to 1-indexed month
        const day = parseInt(parts[1]);
        if (month === 0 || isNaN(day)) return -1;

        // Create a Luxon DateTime object for the temperature date
        // Default to the same year as start date
        let tempDate = DateTime.local(start.year, month, day).setZone(timezone);
        
        // Handle cross-year scenarios
        if (month < start.month && end.year > start.year) {
          tempDate = tempDate.set({ year: end.year });
        }
        
        // Handle year-end edge case (Dec to Jan)
        if (start.month > end.month && month <= end.month) {
          tempDate = tempDate.set({ year: end.year });
        }
        
        // Check if the date is within range with inclusive bounds
        if (tempDate > start.minus({ days: 1 }) && tempDate < end.plus({ days: 1 })) {
          return index;
        }
        
        return -1;
      })
      .filter((index) => index !== -1);

    const filteredTemperatureData = filteredTempIndices.map(
      (index) => temperatureData[index]
    );
    const filteredTemperatureDates = filteredTempIndices.map(
      (index) => temperatureDates[index]
    );

    // Get display dates for scatter plot - format from "YYYY-MM-DD" to "MM-DD"
    const filteredDates = allDates
      .filter((dateStr) => {
        if (!dateStr) return false;
        const parts = dateStr.split("-");
        if (parts.length !== 3) return false;
        
        // Parse full ISO date (YYYY-MM-DD)
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]);
        const day = parseInt(parts[2]);
        
        if (isNaN(year) || isNaN(month) || isNaN(day)) return false;
        
        // Create Luxon DateTime object from full date (with year info)
        const fullDate = DateTime.local(year, month, day).setZone(timezone);
        
        // Compare with start and end dates
        return fullDate > start.minus({ days: 1 }) && 
               fullDate < end.plus({ days: 1 });
      })
      .map((d) => d.split("-").slice(1).join("-"));

    // Filter weather data based on date range
    const filteredWeatherData = weatherData.filter((item) => {
      const itemDate = DateTime.fromISO(item.timestamp).setZone(timezone);
      return itemDate > start.minus({ days: 1 }) && 
             itemDate < end.plus({ days: 1 });
    });

    const result = {
      temperatureData:
        filteredTemperatureData.length > 0
          ? filteredTemperatureData
          : temperatureData,
      temperatureDates:
        filteredTemperatureDates.length > 0
          ? filteredTemperatureDates
          : temperatureDates,
      displayDates:
        filteredDates.length > 0
          ? filteredDates
          : allDates.map((d) => d.split("-").slice(1).join("-")),
      weatherData: filteredWeatherData.length > 0 ? filteredWeatherData : weatherData,
    };

    return result;
  };

  return (
    <div className="alto-card">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center flex-col md:flex-row space-y-2 md:space-y-0">
          <CardTitle className="text-sm font-medium">Historical Analysis</CardTitle>
          <div className="relative flex items-center">
            {loading && (
              <span className="absolute -left-24 text-xs text-blue-500">Loading...</span>
            )}
            {error && (
              <span className="absolute -left-48 text-xs text-red-500">{error}</span>
            )}
            <div className="text-xs text-gray-500">
              {startDate && endDate ? (
                `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
              ) : (
                "No date range selected"
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* BoxPlot Section */}
        <div className="pt-2 pb-4 border-b">
          <h3 className="text-xs font-medium mb-4">
            Distribution of Outdoor Dry Bulb Temperature by Date
          </h3>
          <div className="h-[300px]">
            {loading ? (
              <div className="w-full h-full flex items-center justify-center">
                <Loader size="md" />
              </div>
            ) : error ? (
              <div className="w-full h-full flex items-center justify-center text-red-500">
                {error}
              </div>
            ) : (
              <BoxPlot
                weatherData={weatherData}
                yAxisLabel="Outdoor Drybulb Temperature (°F)"
                temperatureUnit="°F"
                startDate={startDate}
                endDate={endDate}
              />
            )}
          </div>
        </div>

        {/* ScatterPlot Section */}
        <div className="pt-2 pb-6">
          <h3 className="text-xs font-medium mb-4">TSE vs. Cooling Load</h3>
          <div className="h-[280px]">
            {loading ? (
              <div className="w-full h-full flex items-center justify-center">
                <Loader size="md" />
              </div>
            ) : error ? (
              <div className="w-full h-full flex items-center justify-center text-red-500">
                {error}
              </div>
            ) : (
              <ScatterPlot
                data={transformedScatterData}
                dates={filteredData.displayDates}
                startDate={startDate}
                endDate={endDate}
              />
            )}
          </div>
        </div>
      </CardContent>
    </div>
  );
};

export default ComparisonByDate;
