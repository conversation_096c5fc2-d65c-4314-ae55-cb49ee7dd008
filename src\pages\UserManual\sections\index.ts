import { introductionContent } from './introduction/introduction';
import { autopilotContent } from './autopilot/autopilot';
import { plantMapContent } from './plant-map';
import { airSideContent } from './air-side/air-side';
import { dashboardContent } from './dashboard/dashboard';
import { historicalDataContent } from './historical-data/historical-data';
import { maintenanceContent } from './maintenance/maintenance';
import { analyticsContent } from './analytics/analytics';
import { settingsContent } from './settings';
import { savingReportContent } from './saving-report';
import { technicalReportContent } from './technical-report';
import { afddContent } from './afdd/afdd';
import { ManualChapter, ManualMultilingualContent } from './types';

// Add placeholder Thai translations to sections that don't have them yet
function ensureTranslations(chapter: ManualChapter): ManualChapter {
  if (typeof chapter.title === 'string') {
    chapter.title = {
      en: chapter.title,
      th: `[ไทย] ${chapter.title}`
    };
  }

  chapter.sections = chapter.sections.map(section => {
    if (typeof section.title === 'string') {
      section.title = {
        en: section.title,
        th: `[ไทย] ${section.title}`
      };
    }

    if (typeof section.content === 'string') {
      section.content = {
        en: section.content,
        th: `
          <div class="space-y-4">
            <p>
              [ไทย] เนื้อหาภาษาไทยอยู่ระหว่างการแปล กรุณาใช้เนื้อหาภาษาอังกฤษไปก่อน
            </p>
          </div>
        `
      };
    }

    if (section.subsections) {
      section.subsections = section.subsections.map(subsection => {
        if (typeof subsection.title === 'string') {
          subsection.title = {
            en: subsection.title,
            th: `[ไทย] ${subsection.title}`
          };
        }

        if (typeof subsection.content === 'string') {
          subsection.content = {
            en: subsection.content,
            th: `
              <div class="space-y-4">
                <p>
                  [ไทย] เนื้อหาภาษาไทยอยู่ระหว่างการแปล กรุณาใช้เนื้อหาภาษาอังกฤษไปก่อน
                </p>
              </div>
            `
          };
        }

        return subsection;
      });
    }

    return section;
  });

  return chapter;
}

// Apply translations to all sections that don't already have them
const sectionsWithTranslations = [
  introductionContent, // already has translations
  autopilotContent,    // already has translations
  ensureTranslations(plantMapContent),
  ensureTranslations(airSideContent),
  ensureTranslations(dashboardContent),
  ensureTranslations(historicalDataContent),
  ensureTranslations(maintenanceContent),
  ensureTranslations(analyticsContent),
  ensureTranslations(settingsContent),
  ensureTranslations(savingReportContent),
  ensureTranslations(technicalReportContent),
  ensureTranslations(afddContent)
];

// Export all section content
export const manualContent: ManualChapter[] = sectionsWithTranslations;

// Re-export all types
export * from './types'; 