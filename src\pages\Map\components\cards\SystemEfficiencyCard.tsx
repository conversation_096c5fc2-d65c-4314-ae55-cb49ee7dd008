import { useRealtime } from "@/contexts/RealtimeContext";

interface EfficiencyMetric {
  label: string;
  value: number;
  unit: string;
}

export function SystemEfficiencyCard() {
  // Mock data waiting for real-time API
  const { getValue } = useRealtime();
  const metrics: EfficiencyMetric[] = [
    { label: 'Chiller', value: getValue('plant', 'efficiency_chiller'), unit: 'kW/RT' },
    { label: 'CHP', value: getValue('plant', 'efficiency_pchp'), unit: 'kW/RT' },
    { label: 'CDP', value: getValue('plant', 'efficiency_cdp'), unit: 'kW/RT' },
    { label: 'CT', value: getValue('plant', 'efficiency_ct'), unit: 'kW/RT' },
  ];

  return (
    <div className="w-full h-full p-2 alto-card flex flex-col justify-between items-start">
      <div className="text-[#065BA9] text-sm font-semibold tracking-[0.01em]">
        System Efficiency
      </div>

      <div className="w-full grid grid-cols-2 sm:grid-cols-4 gap-1">
        {metrics.map((metric, index) => (
          <div 
            key={index}
            className="p-1.5 bg-[#F9FAFF] rounded-lg border border-[#EDEFF9] overflow-x-auto"
          >
            <div className="flex flex-col min-w-max">
              <div className="text-[#788796] text-[13px] font-normal">
                {metric.label}
              </div>
              <div className="text-[#0E7EE4] text-[14px] sm:text-base font-semibold tracking-[0.01em]">
                {metric.value == null ? '-' : metric.value.toFixed(3)}
              </div>
              <div className="text-[#788796] text-xs font-normal">
                {metric.unit}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}