export interface StatusConfig {
  color: string;
  label: string;
  temp?: string;
  angle?: string;
}

export const STATUS_MAPPING: Record<string, StatusConfig> = {
  off: {
    color: "#D1D5DB",
    label: "Off",
  },
  normal: {
    color: "#14B0BC",
    label: "Normal",
    temp: "(≤25°C)",
    angle: "(≤80%)",
  },
  warning: {
    color: "#DFCB28",
    label: "Warning",
    temp: "(25°C - 28°C)",
    angle: "(80% - 90%)",
  },
  alarm: {
    color: "#D73C31",
    label: "Alarm",
    temp: "(≥28°C)",
    angle: "(90% - 100%)",
  },
};
