import React from 'react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import TabNavigation from './TabNavigation';
import BaselineTab from './Tabs/BaselineTab';
import ReportingTab from './Tabs/ReportingTab';
import SummaryTab from './Tabs/SummaryTab';
import DataImport from './DataImport';

const MainDashboard: React.FC = () => {
  const { activeTab } = useSavingDashboard();

  return (
    <div className="w-full bg-white rounded-lg shadow-sm p-3">
      <div className="flex flex-col gap-2">
        <div className="flex justify-between items-center">
          <TabNavigation />
          <div className="flex items-center gap-2">
            <DataImport />
          </div>
        </div>
      </div>
      
      <div className="mt-3">
        {activeTab === 'baseline' && <BaselineTab />}
        {activeTab === 'reporting' && <ReportingTab />}
        {activeTab === 'summary' && <SummaryTab />}
      </div>
    </div>
  );
};

export default MainDashboard;