// Weather API response types from Django backend
export interface WeatherForecast {
  forecast_time: string;
  drybulb_temperature: number;
  humidity: number;
  rain_volume: number;
  weather_condition: number;
  weather_condition_text: string;
}

export interface WeatherLocation {
  type: string;
  coordinates: number[];
}

export interface WeatherMetadata {
  source: string;
  version: string;
  forecast_hours: number;
}

export interface WeatherData {
  forecasts: WeatherForecast[];
  location: WeatherLocation;
  metadata: WeatherMetadata;
  timestamp: string;
  site_id: string;
}

// Weather service response type
export interface WeatherResponse {
  weatherData: WeatherData | null;
  loading: boolean;
  error: string | null;
}

// Weather icon mapping type
export interface WeatherIconMapping {
  [code: number]: string;
}
