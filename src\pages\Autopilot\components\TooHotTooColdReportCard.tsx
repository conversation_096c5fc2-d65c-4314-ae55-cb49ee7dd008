import React, { useState } from 'react';
import { FiThermometer } from 'react-icons/fi';
import { RiSunLine, RiSnowflakeLine } from 'react-icons/ri';

interface FloorZoneOption {
  id: string;
  name: string;
}

const TooHotTooColdReportCard: React.FC = () => {
  const [step, setStep] = useState<number>(1); // 1: initial, 2: selection, 3: confirmation
  const [complaintType, setComplaintType] = useState<'hot' | 'cold' | null>(null);
  const [selectedFloor, setSelectedFloor] = useState<string>('');
  const [selectedZone, setSelectedZone] = useState<string>('');

  // Sample data for floors and zones
  const floors: FloorZoneOption[] = [
    { id: 'floor-1', name: 'Floor 1' },
    { id: 'floor-2', name: 'Floor 2' },
    { id: 'floor-3', name: 'Floor 3' },
  ];

  const zones: Record<string, FloorZoneOption[]> = {
    'floor-1': [
      { id: 'zone-1-1', name: 'Zone 1' },
      { id: 'zone-1-2', name: 'Zone 2' },
    ],
    'floor-2': [
      { id: 'zone-2-1', name: 'Zone 1' },
      { id: 'zone-2-2', name: 'Zone 2' },
      { id: 'zone-2-3', name: 'Zone 3' },
    ],
    'floor-3': [
      { id: 'zone-3-1', name: 'Zone 1' },
      { id: 'zone-3-2', name: 'Zone 2' },
    ],
  };

  const handleComplaintSelect = (type: 'hot' | 'cold') => {
    setComplaintType(type);
    setStep(2);
  };

  const handleSubmit = () => {
    if (selectedFloor && selectedZone) {
      // In a real app, you would send this data to an API
      console.log('Submitting complaint:', {
        type: complaintType,
        floor: selectedFloor,
        zone: selectedZone,
      });
      setStep(3);
      
      // Reset after a few seconds
      setTimeout(() => {
        resetForm();
      }, 3000);
    }
  };

  const resetForm = () => {
    setComplaintType(null);
    setSelectedFloor('');
    setSelectedZone('');
    setStep(1);
  };

  const availableZones = selectedFloor ? zones[selectedFloor] : [];

  return (
    <div className="w-full p-[10px] alto-card">
      <div className="flex flex-col">
        {/* Title Row */}
        <div className="w-full flex justify-between items-center mb-1">
          <h2 className="text-card-foreground text-md font-semibold tracking-[0.01em] text-[#065BA9]">
            Comfort Feedback
          </h2>
        </div>

        {/* Step 1: Select Too Hot or Too Cold */}
        {step === 1 && (
          <div className="flex flex-col gap-2">
            <div className="flex gap-2 mt-1">
              <button
                onClick={() => handleComplaintSelect('hot')}
                className="flex-1 py-2 px-3 rounded-md border border-[#E53E3E] bg-[#FFF5F5] text-[#E53E3E] flex items-center justify-center gap-2 hover:bg-[#FFEBEB] transition-colors"
              >
                <RiSunLine className="h-4 w-4" />
                <span className="font-medium">Too Hot</span>
              </button>
              <button
                onClick={() => handleComplaintSelect('cold')}
                className="flex-1 py-2 px-3 rounded-md border border-[#0E7EE4] bg-[#E5F2FF] text-[#0E7EE4] flex items-center justify-center gap-2 hover:bg-[#D9EAFF] transition-colors"
              >
                <RiSnowflakeLine className="h-4 w-4" />
                <span className="font-medium">Too Cold</span>
              </button>
            </div>
          </div>
        )}

        {/* Step 2: Select Floor and Zone */}
        {step === 2 && (
          <div className="flex flex-col gap-3">
            <p className="text-sm text-[#788796]">
              {complaintType === 'hot' ? 'Too Hot' : 'Too Cold'} complaint for:
            </p>
            <div className="flex gap-2">
              <div className="flex flex-col gap-2 flex-1">
                <label className="text-xs text-[#788796] font-medium">Floor</label>
                <select
                  value={selectedFloor}
                  onChange={(e) => {
                    setSelectedFloor(e.target.value);
                    setSelectedZone('');
                  }}
                  className="w-full py-1.5 px-2 rounded-md border border-[#DDDDDD] bg-white text-sm focus:outline-none focus:ring-1 focus:ring-[#0E7EE4]"
                  required
                >
                  <option value="">Select Floor</option>
                  {floors.map((floor) => (
                    <option key={floor.id} value={floor.id}>
                      {floor.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="flex flex-col gap-2 flex-1">
                <label className="text-xs text-[#788796] font-medium">Zone</label>
                <select
                  value={selectedZone}
                  onChange={(e) => setSelectedZone(e.target.value)}
                  className="w-full py-1.5 px-2 rounded-md border border-[#DDDDDD] bg-white text-sm focus:outline-none focus:ring-1 focus:ring-[#0E7EE4]"
                  disabled={!selectedFloor}
                  required
                >
                  <option value="">Select Zone</option>
                  {availableZones.map((zone) => (
                    <option key={zone.id} value={zone.id}>
                      {zone.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="flex gap-2 mt-2">
              <button
                onClick={resetForm}
                className="flex-1 py-1.5 px-3 rounded-md border border-[#DDDDDD] bg-white text-[#788796] hover:bg-[#F5F5F5] transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={!selectedFloor || !selectedZone}
                className={`flex-1 py-1.5 px-3 rounded-md border ${
                  !selectedFloor || !selectedZone
                    ? 'border-[#DDDDDD] bg-[#F5F5F5] text-[#AAAAAA] cursor-not-allowed'
                    : complaintType === 'hot'
                    ? 'border-[#FF7A00] bg-[#FF7A00] text-white hover:bg-[#E56E00]'
                    : 'border-[#0E7EE4] bg-[#0E7EE4] text-white hover:bg-[#0C70CC]'
                } transition-colors`}
              >
                Submit
              </button>
            </div>
          </div>
        )}

        {/* Step 3: Confirmation */}
        {step === 3 && (
          <div className="flex flex-col items-center py-4">
            <div 
              className={`w-10 h-10 rounded-full flex items-center justify-center mb-3 ${
                complaintType === 'hot' 
                  ? 'bg-[#FFF5E5] text-[#FF7A00]' 
                  : 'bg-[#E5F2FF] text-[#0E7EE4]'
              }`}
            >
              {complaintType === 'hot' ? (
                <RiSunLine className="h-6 w-6" />
              ) : (
                <RiSnowflakeLine className="h-6 w-6" />
              )}
            </div>
            <p className="text-sm font-semibold text-[#065BA9] mb-1">Thank You!</p>
            <p className="text-xs text-[#788796] text-center">
              Your feedback has been recorded and will be addressed by our automation system.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TooHotTooColdReportCard;
