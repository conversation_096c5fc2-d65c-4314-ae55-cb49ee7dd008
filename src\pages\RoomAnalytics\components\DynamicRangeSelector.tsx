import React from 'react';
import RangeSelector from './RangeSelector';

const AQI_METRIC_RANGES: Record<string, { min: number; max: number; step?: number; unit: string }> = {
  'CO2': { min: 400, max: 2000, step: 50, unit: 'ppm' },
  'Temperature': { min: 10, max: 40, step: 1, unit: '°C' },
  'PM2.5': { min: 0, max: 150, step: 5, unit: 'µg/m³' },
  'Humidity': { min: 0, max: 100, step: 5, unit: '%' },
  // Add more metrics as needed
};

const DynamicRangeSelector: React.FC = () => {
  const [selectedMetric, setSelectedMetric] = React.useState<keyof typeof AQI_METRIC_RANGES>('CO2');
  const rangeConfig = AQI_METRIC_RANGES[selectedMetric];

  const [range, setRange] = React.useState<[number, number]>([rangeConfig.min, rangeConfig.max]);

  // Reset range when metric changes
  React.useEffect(() => {
    setRange([rangeConfig.min, rangeConfig.max]);
  }, [selectedMetric]);

  return (
    <div className="space-y-4">
      <div>
        <label className="block font-medium mb-1">Select Metric</label>
        <select
          value={selectedMetric}
          onChange={(e) => setSelectedMetric(e.target.value as keyof typeof AQI_METRIC_RANGES)}
          className="border rounded px-3 py-2 text-sm bg-white"
        >
          {Object.keys(AQI_METRIC_RANGES).map((metric) => (
            <option key={metric} value={metric}>
              {metric}
            </option>
          ))}
        </select>
      </div>

      <RangeSelector
        label={`Select ${selectedMetric} range`}
        min={rangeConfig.min}
        max={rangeConfig.max}
        step={rangeConfig.step}
        value={range}
        unit={rangeConfig.unit}
        onChange={setRange}
        formatUnit={(value, unit) => `${value} ${unit}`}
      />
    </div>
  );
};

export default DynamicRangeSelector;
