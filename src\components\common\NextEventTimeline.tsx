import React, { useMemo, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useActions } from '@/contexts/ActionContext';
import ChillerSequenceEventCard from './ChillerSequenceEventCard';
import ScheduleEventCard from './ScheduleEventCard';
import { AnyActionEvent } from '@/services/actionService'; // Adjust path if necessary
import { format, parseISO, differenceInSeconds, isToday, isTomorrow, isThisYear } from 'date-fns';

// Interface for the processed event data passed down
interface ProcessedEventData {
  id: string;
  actionEvent: AnyActionEvent; // Pass the original event
  time: string;                 // Formatted time (HH:mm)
  remainingTime: string;        // Formatted remaining time (H:MM:SS)
  date: string;                 // Date display string (Today, Tomorrow, d MMM, etc.)
  showHourMarker: boolean;
  showDateSeparator: boolean;
  status: 'pending' | 'in-progress' | 'completed'; // Use specific statuses
}

interface NextEventTimelineProps {
  className?: string;
}

// Helper to format remaining time
const formatRemainingTime = (seconds: number): string => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return `${h}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
};

// Helper to format date display
const formatDateDisplay = (date: Date): string => {
  if (isToday(date)) return 'Today';
  if (isTomorrow(date)) return 'Tomorrow';
  if (isThisYear(date)) return format(date, 'd MMM');
  return format(date, 'd MMM yyyy');
};

// Function to transform a single ActionEvent into ProcessedEventData
const transformActionEvent = (event: AnyActionEvent, currentTime: Date): ProcessedEventData => {
  const scheduledTime = parseISO(event.scheduled_time);
  const remainingSeconds = Math.max(0, differenceInSeconds(scheduledTime, currentTime));
  const remainingTime = formatRemainingTime(remainingSeconds);
  const dateDisplay = formatDateDisplay(scheduledTime);

  // Ensure status fits the expected literal type
  const validStatus = ['pending', 'in-progress', 'completed'].includes(event.status)
    ? event.status as ProcessedEventData['status']
    : 'pending'; // Default to pending if status is invalid

  return {
    id: event.action_id,
    actionEvent: event, // Include the original event object
    time: format(scheduledTime, 'HH:mm'),
    remainingTime,
    date: dateDisplay,
    showHourMarker: false, // Will be calculated later
    showDateSeparator: false, // Will be calculated later
    status: validStatus,
  };
};

const NextEventTimeline: React.FC<NextEventTimelineProps> = ({ className }) => {
  const { pendingGroupActionEvents, inProgressGroupActionEvents } = useActions();
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Update current time every second for the countdown timer
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);
  
  // Combine, sort, transform, and limit events
  const initialEvents: ProcessedEventData[] = useMemo(() => {
    // Combine pending and in-progress events safely handling potential null/undefined
    const allEvents: AnyActionEvent[] = [
      ...(pendingGroupActionEvents || []),
      ...(inProgressGroupActionEvents || [])
    ];

    // Sort events by scheduled time (ascending)
    const sortedEvents = allEvents.sort((a, b) =>
      parseISO(a.scheduled_time).getTime() - parseISO(b.scheduled_time).getTime()
    );

    // Transform and take the next 15 events
    return sortedEvents
      .slice(0, 15)
      .map(event => transformActionEvent(event, currentTime));

  }, [pendingGroupActionEvents, inProgressGroupActionEvents, currentTime]);

  // Calculate hour markers and date separators based on the transformed events
  const processedDisplayData = useMemo(() => {
    const hoursSeen = new Set<string>();
    const datesSeen = new Set<string>();
    
    return initialEvents.map(event => {
      const hour = event.time.split(':')[0]; // Get hour part from HH:mm
      const isFirstOfHour = !hoursSeen.has(hour);
      const isFirstOfDate = !datesSeen.has(event.date);
      
      if (isFirstOfHour) hoursSeen.add(hour);
      if (isFirstOfDate) datesSeen.add(event.date);
      
      return {
        ...event,
        showHourMarker: isFirstOfHour,
        showDateSeparator: isFirstOfDate && event.date !== 'Today',
      };
    });
  }, [initialEvents]);

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="flex items-center gap-2 pb-4 border-b">
        <h3 className="text-base font-semibold text-primary">Upcoming Events</h3>
        {/* Arrow Icon */}
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-primary">
          <path fillRule="evenodd" clipRule="evenodd" d="M4.39181 12.2447C4.39181 12.0371 4.47426 11.8381 4.62103 11.6913C4.76779 11.5445 4.96685 11.4621 5.17441 11.4621H16.2749L11.9362 7.3299C11.7938 7.18442 11.7138 6.98922 11.713 6.78571C11.7122 6.58219 11.7907 6.38636 11.9318 6.23976C12.073 6.09316 12.2657 6.00732 12.4691 6.00045C12.6725 5.99357 12.8706 6.06621 13.0214 6.20295L18.7605 11.6812C18.8362 11.7542 18.8965 11.8417 18.9376 11.9386C18.9788 12.0354 19 12.1395 19 12.2447C19 12.3499 18.9788 12.454 18.9376 12.5508C18.8965 12.6476 18.8362 12.7352 18.7605 12.8082L13.0214 18.2864C12.9481 18.3613 12.8606 18.4207 12.764 18.4611C12.6673 18.5015 12.5636 18.5221 12.4589 18.5217C12.3541 18.5213 12.2505 18.4999 12.1542 18.4587C12.0579 18.4175 11.9709 18.3574 11.8982 18.282C11.8256 18.2065 11.7688 18.1173 11.7313 18.0195C11.6938 17.9217 11.6763 17.8174 11.6798 17.7127C11.6833 17.608 11.7078 17.5051 11.7519 17.4101C11.7959 17.3151 11.8586 17.2298 11.9362 17.1595L16.2749 13.0273H5.17441C4.96685 13.0273 4.76779 12.9448 4.62103 12.7981C4.47426 12.6513 4.39181 12.4522 4.39181 12.2447Z" fill="currentColor"/>
        </svg>
      </div>

      {/* Timeline Container */}
      <div className="relative flex-1 min-h-0 pt-4">
        {/* Timeline Line */}
        <div className="absolute grid grid-cols-[2.5rem,2rem,1fr] w-full h-full">
          <div className="col-start-2 w-[2px] h-[calc(100%-20px)] bg-[#8BC6FF] mx-auto" />
        </div>

        {/* Events List */}
        <div className="relative h-full space-y-4 overflow-y-auto pr-2">
          {processedDisplayData.length > 0 ? (
            <>
              {processedDisplayData.map((eventData, index) => {
                // Common props for BaseEventCard and its wrappers
                const commonProps = {
                  key: eventData.id,
                  eventStatus: eventData.status,
                  time: eventData.time,
                  remainingTime: eventData.remainingTime,
                  isActive: index === 0,
                  showHourMarker: eventData.showHourMarker,
                };

                let cardElement: React.ReactNode = null;

                // Render specific card based on actionEvent.action_type
                if (eventData.actionEvent.action_type === 'start_chiller_sequence' || eventData.actionEvent.action_type === 'stop_chiller_sequence') {
                  cardElement = (
                    <ChillerSequenceEventCard
                      {...commonProps}
                      event={eventData.actionEvent} // Pass the specific event object
                    />
                  );
                } else if (eventData.actionEvent.action_type === 'schedule') {
                  cardElement = (
                    <ScheduleEventCard
                      {...commonProps}
                      event={eventData.actionEvent} // Pass the specific event object
                    />
                  );
                } else {
                  // Optional: Render a fallback or null for unhandled types
                  // Cast back to AnyActionEvent to access action_type for logging
                  const actionEvent = eventData.actionEvent as AnyActionEvent;
                  console.warn("Timeline skipping render for unhandled event type:", actionEvent.action_type);
                }

                return (
                  <React.Fragment key={eventData.id}>
                    {eventData.showDateSeparator && (
                      <div className="grid grid-cols-[2.5rem,2rem,1fr] items-center my-4">
                        <div className="col-span-2"></div>
                        <div className="flex items-center w-full">
                          <div className="flex-grow h-px bg-border"></div>
                          <span className="px-3 text-xs font-medium text-primary bg-background">{eventData.date}</span>
                          <div className="flex-grow h-px bg-border"></div>
                        </div>
                      </div>
                    )}
                    {cardElement} {/* Render the determined card element */}
                  </React.Fragment>
                );
              })}
            </>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No upcoming events scheduled
            </div>
          )}
        </div>

        {/* Gradient Overlay */}
        <div className="h-[40%] bg-gradient-to-t from-background to-transparent absolute bottom-0 left-0 right-0 pointer-events-none" />
      </div>
    </div>
  );
};

export default NextEventTimeline;
