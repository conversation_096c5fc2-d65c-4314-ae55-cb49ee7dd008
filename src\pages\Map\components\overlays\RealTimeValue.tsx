import React from 'react';
import { cn } from '@/lib/utils';
import { useRealtime } from '@/contexts/RealtimeContext';

interface RealTimeValueProps {
  config: {
    id: string;
    type: string;
    label: string;
    properties: {
      title: string;
      deviceId: string;
      datapoint: string;
      unit: string;
      precision: number;
      backgroundColor: string;
    }
  };
  isEditMode?: boolean;
  onClick?: (e: React.MouseEvent) => void;
}

export const RealTimeValue: React.FC<RealTimeValueProps> = ({
  config,
  isEditMode,
  onClick,
}) => {
  const { getValue } = useRealtime();
  const value = getValue(config.properties.deviceId, config.properties.datapoint);

  const displayValue = value !== null && value !== undefined
    ? `${Number(value).toFixed(config.properties.precision)} ${config.properties.unit}`
    : '--';

  return (
    <div
      onClick={onClick}
      className={cn(
        "min-w-[38px] h-[33px] p-[3px] rounded-[5px] flex flex-col justify-center items-center gap-[1px] shadow-sm cursor-default",
        isEditMode && "cursor-move ring-2 ring-ring ring-offset-2",
      )}
      style={{
        backgroundColor: config.properties.backgroundColor || 'hsl(var(--primary))',
      }}
    >
      {/* Title */}
      <div className="text-center text-[8px] font-bold text-[#052745]">
        {config.properties.title}
      </div>

      {/* Value Container */}
      <div className="w-full px-[2px] py-[1px] bg-card rounded-[2px] flex justify-center items-center">
        <div className="text-center text-[8px] font-bold text-primary whitespace-nowrap">
          {displayValue}
        </div>
      </div>
    </div>
  );
}; 