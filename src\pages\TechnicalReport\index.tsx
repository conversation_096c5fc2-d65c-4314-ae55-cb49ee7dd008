import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getDashAppUrl } from '@/utils/dash';

const TechnicalReport: React.FC = () => {
  const { site } = useAuth();
  const dashboardAppUrl = getDashAppUrl('alto-automated-report', site?.id);

  return (
    <div className="w-full h-[calc(100vh-71px)] overflow-hidden bg-background">
      <iframe 
        src={dashboardAppUrl}
        className="w-full h-full border-none"
        title="Technical Report"
        sandbox="allow-scripts allow-forms allow-same-origin allow-downloads"
        allow="download *"
      />
    </div>
  );
};

export default TechnicalReport; 