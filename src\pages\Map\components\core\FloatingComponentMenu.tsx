import React from 'react';
import { AVAILABLE_COMPONENTS } from '@/pages/Map/components/types';
import { cn } from '@/lib/utils';

interface FloatingComponentMenuProps {
  onSelect: (type: string) => void;
  onClose: () => void;
}

export const FloatingComponentMenu: React.FC<FloatingComponentMenuProps> = ({
  onSelect,
  onClose,
}) => {
  return (
    <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 bg-background backdrop-blur-sm rounded-lg border border-border shadow-card min-w-[160px] overflow-hidden animate-in fade-in slide-in-from-bottom-2 duration-200">
      <div className="text-sm font-medium text-foreground mb-1 px-3 py-1.5">Add Component</div>
      <div className="border-t border-border">
        {AVAILABLE_COMPONENTS.map((component) => (
          <button
            key={component.type}
            onClick={() => {
              onSelect(component.type);
              onClose();
            }}
            className={cn(
              "w-full flex items-center gap-1 px-3 py-2 transition-all duration-150 text-xs",
              "text-muted-foreground hover:text-background",
              "hover:bg-accent active:bg-accent/80 group"
            )}
          >
            <span className="opacity-80 group-hover:opacity-100 transition-opacity">{component.icon}</span>
            <span>{component.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
}; 