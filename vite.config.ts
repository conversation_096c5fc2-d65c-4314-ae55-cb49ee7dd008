import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import svgr from 'vite-plugin-svgr'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    svgr()  // Add SVG support
  ],
  server: {
    host: '0.0.0.0',  // Explicitly listen on all interfaces
    port: 3000,
    strictPort: true, // Don't try other ports if 3000 is taken
  },
  preview: {
    port: 3000
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  }
})
