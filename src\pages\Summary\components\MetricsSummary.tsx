import React from "react";

interface MetricsSummaryProps {
  title: string;
  metrics: {
    label: string;
    value: number;
    decimalPlaces?: number;
    unit: string;
  }[];
}
const MetricsSummary: React.FC<MetricsSummaryProps> = ({ title, metrics }) => {
  return (
    <div className="flex w-full justify-start items-start flex-col gap-2.5 p-3 alto-card">
      <div className="flex w-full justify-start items-center flex-row gap-4">
        <span className="text-[#065BA9] text-base font-semibold">{title}</span>
      </div>
      <div className="flex w-full justify-start items-start flex-col gap-1 p-2.5 bg-[#F9FAFF] border-solid border-[#EDEFF9] border rounded-lg">
        <div className="flex w-full justify-start items-start flex-col gap-1 py-0.5">
          {metrics.map((metric, index) => (
            <div key={index} className="flex w-full justify-between items-center flex-row py-1">
              <span className="text-[#374151] text-xs">{metric.label}</span>
              <div className="flex justify-start items-center flex-row gap-1">
                <span className="text-[#065BA9] text-sm text-center font-medium">
                  {metric.decimalPlaces !== undefined 
                    ? (isNaN(metric.value) ? '0' : metric.value.toLocaleString(undefined, { 
                        minimumFractionDigits: metric.decimalPlaces,
                        maximumFractionDigits: metric.decimalPlaces
                      })) 
                    : (isNaN(metric.value) ? '0' : metric.value.toLocaleString(undefined, { maximumFractionDigits: 0 }))}
                </span>
                <span className="text-[#788796] text-xs">{metric.unit}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MetricsSummary; 