import React, { useState, useEffect } from 'react';
import UserManualSidebar from './UserManualSidebar';
import { manualContent } from './sections';
import { LanguageProvider, useLanguage } from './LanguageContext';
import LanguageSwitcher from './LanguageSwitcher';
import { useLocation } from 'react-router-dom';

const UserManualContent: React.FC = () => {
  const [activeChapter, setActiveChapter] = useState<string>('introduction');
  const [activeSection, setActiveSection] = useState<string>('introduction-overview');
  const [activeSubsection, setActiveSubsection] = useState<string | null>(null);
  const { language } = useLanguage();
  const location = useLocation();

  useEffect(() => {
    // Find the introduction chapter and overview section
    const introChapter = manualContent.find(ch => ch.id === 'introduction');
    const introSection = introChapter?.sections.find(sec => sec.id === 'introduction-overview');
    
    // Ensure we have default content selected when the component mounts
    if (introChapter && introSection) {
      setActiveChapter('introduction');
      setActiveSection('introduction-overview');
      setActiveSubsection(null);
    }
  }, [location.key]); // React to changes in location key (which changes when navigation occurs)

  // Find the active content based on selection
  const activeChapterData = manualContent.find(ch => ch.id === activeChapter);
  const activeSectionData = activeChapterData?.sections.find(sec => sec.id === activeSection);
  const activeSubsectionData = activeSectionData?.subsections?.find(sub => sub.id === activeSubsection);

  // Get the content for the current language
  const contentToDisplay = activeSubsectionData?.content?.[language] || activeSectionData?.content?.[language];
  const title = activeSubsectionData 
    ? `${activeSectionData?.title[language]} - ${activeSubsectionData.title[language]}` 
    : activeSectionData?.title[language];

  const chapterTitle = activeChapterData?.title[language];

  return (
    <div className="mx-4 px-[0px] py-6">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Sidebar Navigation */}
        <div className="w-[20%]">
          <UserManualSidebar
            manualContent={manualContent}
            activeChapter={activeChapter}
            activeSection={activeSection}
            activeSubsection={activeSubsection || ''}
            setActiveChapter={setActiveChapter}
            setActiveSection={setActiveSection}
            setActiveSubsection={(subsectionId) => setActiveSubsection(subsectionId)}
            language={language}
          />
        </div>
        
        {/* Main Content */}
        <div className="w-full">
          {/* Header and Language Switcher */}
          <div className="mb-3">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">{chapterTitle}</h1>
                <p className="text-gray-600">
                  {language === 'en' ? 'User Manual & Documentation' : 'คู่มือการใช้งานและเอกสาร'}
                </p>
              </div>
              <LanguageSwitcher />
            </div>
          </div>

          <div className="border-t border-gray-200 my-4"></div>
          
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">{title}</h2>
          <div className="prose max-w-none">
            {typeof contentToDisplay === 'string' ? (
              <div dangerouslySetInnerHTML={{ __html: contentToDisplay as string }} />
            ) : (
              contentToDisplay
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const UserManual: React.FC = () => {
  return (
    <LanguageProvider>
      <UserManualContent />
    </LanguageProvider>
  );
};

export default UserManual;
