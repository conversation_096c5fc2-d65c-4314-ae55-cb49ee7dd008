import React, { ReactNode, useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { FileDown } from 'lucide-react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface ExportButtonProps {
  selectedYear?: number;
  selectedMonth?: number;
  variant?: 'default' | 'outline' | 'ghost';
  className?: string;
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children?: ReactNode;
  thbPerKwh?: number;
}

const ExportButton: React.FC<ExportButtonProps> = ({
  selectedYear,
  selectedMonth = 0,
  variant = 'outline',
  className = 'flex items-center gap-1',
  size,
  children,
  thbPerKwh = 4.37,
}) => {
  const {
    baselineData,
    reportingData,
    regressionResult,
    baselineRange,
    reportingRange,
    variableSelection,
    summaryStats,
    getCurrentElectricityRate
  } = useSavingDashboard();

  const [logoSvg, setLogoSvg] = useState<string>('');

  // Get the current electricity rate values for the selected year/month
  const currentRateValues = useMemo(() => {
    // Default to current year and month if not provided
    const currentYear = new Date().getFullYear();
    return getCurrentElectricityRate(
      selectedYear || currentYear,
      selectedMonth || 0
    );
  }, [getCurrentElectricityRate, selectedYear, selectedMonth]);

  useEffect(() => {
    fetch('/src/assets/alto_logo.svg')
      .then(response => response.text())
      .then(svgText => {
        const base64SVG = btoa(svgText);
        setLogoSvg(`data:image/svg+xml;base64,${base64SVG}`);
      })
      .catch(error => {
        console.error('Error loading SVG:', error);
        setLogoSvg('');
      });
  }, []);

  const year = selectedYear || new Date().getFullYear();

  const handleExport = () => {
    console.log('Export button clicked');

    if (!baselineData.length || !reportingData.length) {
      toast.error('No data available to export');
      return;
    }

    try {
      const printFrame = document.createElement('iframe');
      printFrame.style.position = 'fixed';
      printFrame.style.right = '0';
      printFrame.style.bottom = '0';
      printFrame.style.width = '0';
      printFrame.style.height = '0';
      printFrame.style.border = 'none';
      document.body.appendChild(printFrame);

      const frameDoc = printFrame.contentWindow?.document;
      if (!frameDoc) {
        toast.error('Failed to create document for export');
        return;
      }

      frameDoc.open();

      const formattedBaselineRange = baselineRange.start && baselineRange.end
        ? `${format(new Date(baselineRange.start), 'MMMM d, yyyy')} to ${format(new Date(baselineRange.end), 'MMMM d, yyyy')}`
        : 'Not set';

      const formattedReportingRange = reportingRange.start && reportingRange.end
        ? `${format(new Date(reportingRange.start), 'MMMM d, yyyy')} to ${format(new Date(reportingRange.end), 'MMMM d, yyyy')}`
        : 'Not set';

      const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      const reportPeriodText = selectedMonth === 0
        ? `Year: ${year}`
        : `Month: ${format(new Date(year, selectedMonth-1, 1), 'MMMM yyyy')}`;

      frameDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta http-equiv="X-UA-Compatible" content="ie=edge">
          <meta name="color-scheme" content="light">
          <meta name="forced-colors" content="none">
          <title>Energy Savings Report - ${year}</title>
          <style>
            @page {
              size: A4 landscape;
              margin: 1cm;
              @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 8pt;
                color: #666;
              }
              @top-right {
                content: "Energy Savings Report";
                font-size: 8pt;
                color: #666;
              }
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
              line-height: 1.5;
              color: #333;
              margin: 0;
              padding: 0;
              counter-reset: page;
              font-size: 10pt;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
              color-adjust: exact !important;
              max-width: 100%;
              overflow-x: hidden;
            }

            @media print {
              body {
                counter-increment: page;
                counter-reset: page 2;
              }

              .page {
                page: auto;
                margin: 0;
              }

              .columns {
                max-height: 8cm;
                overflow: hidden;
              }
            }
            thead {
              background-color: #EEEFF9 !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            th {
              background-color: #EEEFF9 !important;
              color: #788796 !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            tbody tr:nth-child(odd) {
              background-color: white !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            tbody tr:nth-child(even) {
              background-color: #F9FAFF !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .report-header {
              text-align: center;
              border-bottom: 1pt solid #42536e;
              padding-bottom: 0.01cm;
              margin-bottom: 0.01cm;
              position: relative;
            }
            .logo {
              position: absolute;
              top: 0.1cm;
              left: 0.1cm;
              width: 1cm;
              height: auto;
            }
            .report-header h1 {
              font-size: 11pt;
              color: #42536e;
              margin-bottom: 0.05cm;
              font-weight: 500;
              margin-top: 0;
            }
            .report-header h2 {
              font-size: 9pt;
              color: #333;
              font-weight: 400;
              margin: 0;
            }
            .report-meta {
              display: flex;
              justify-content: space-between;
              margin-top: 0.05cm;
              font-size: 6pt;
              color: #666;
            }
            .page {
              padding: 0;
              margin-bottom: 0.5cm;
              position: relative;
              break-inside: avoid;
            }
            .page-title {
              font-size: 11pt;
              color: #42536e;
              margin-bottom: 0.2cm;
              border-bottom: 1pt solid #eee;
              padding-bottom: 0.1cm;
            }
            .data-table {
              width: 100%;
              border-collapse: separate;
              border-spacing: 0;
              margin: 0.1cm 0 0.2cm 0;
              font-size: 5pt;
              border: 1pt solid #DBE4FF;
              border-radius: 0.1cm;
              overflow: hidden;
            }
            .data-table th:first-child {
              border-top-left-radius: 0.1cm;
            }
            .data-table th:last-child {
              border-top-right-radius: 0.1cm;
            }
            .data-table tr:last-child td:first-child {
              border-bottom-left-radius: 0.1cm;
            }
            .data-table tr:last-child td:last-child {
              border-bottom-right-radius: 0.1cm;
            }
            .data-table th, .data-table td {
              padding: 0.01cm 0.05cm;
              text-align: center;
              border-right: 1pt solid #DBE4FF;
              border-bottom: 1pt solid #DBE4FF;
            }
            .data-table th:last-child, .data-table td:last-child {
              border-right: none;
            }
            .data-table tr:last-child td {
              border-bottom: none;
            }
            .data-table thead th {
              background-color: #EEEFF9 !important;
              font-weight: 500;
              color: #788796;
              font-size: 5pt;
              height: 10pt;
              border-bottom: 1pt solid #DBE4FF;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .data-table td {
              font-size: 5pt;
              color: #212529;
              padding-top: 0.01cm;
              padding-bottom: 0.01cm;
              height: 8pt;
            }
            .data-table tbody tr:nth-of-type(odd) {
              background-color: white !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .data-table tbody tr:nth-of-type(even) {
              background-color: #F9FAFF !important;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .data-table th.separator, .data-table td.separator {
              border-right: 2pt solid #DBE4FF;
            }
            .data-table thead tr:first-child th {
              text-align: center;
              background-color: #EEEFF9;
            }
            .variables-section {
              display: flex;
              gap: 1cm;
              margin-bottom: 0.5cm;
            }
            .variable-item {
              font-size: 10pt;
              margin-bottom: 0.2cm;
            }
            .variable-value {
              font-weight: 500;
              color: #42536e;
            }
            .key-value {
              margin-bottom: 0.3cm;
            }
            .key-value .key {
              font-weight: 500;
              margin-right: 0.2cm;
            }
            .key-value .value {
              color: #42536e;
            }
            h3 {
              color: #42536e;
              font-size: 9pt;
              margin-top: 0.1cm;
              margin-bottom: 0.1cm;
            }
            .columns {
              display: flex;
              gap: 0.3cm;
              page-break-inside: avoid;
              break-inside: avoid;
              max-height: 19cm;
            }
            .column {
              flex: 1;
              gap: 0.3cm;
              max-height: 19cm;
              overflow: hidden;
            }
            .table-note {
              font-size: 5pt;
              color: #666;
              font-style: italic;
              margin-top: 0.05cm;
              margin-bottom: 0.05cm;
            }
          </style>
        </head>
        <body>
          <svg style="display: none;">
            <symbol id="check-circle" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
              <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
            </symbol>
            <symbol id="x-circle" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
              <path d="M15 9l-6 6M9 9l6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </symbol>
          </svg>

          <div class="page" style="break-after: page;">
            <div class="report-header">
              <div style="position: absolute;">
                <img src="${logoSvg}" class="logo" alt="Alto Logo">
              </div>
              <h1>Energy Performance Report</h1>
              <h2>${reportPeriodText}</h2>
              <div class="report-meta">
                <span><strong>Generated:</strong> ${currentDate}</span>
                <span><strong>Baseline Period:</strong> ${formattedBaselineRange}</span>
                <span><strong>Reporting Period:</strong> ${formattedReportingRange}</span>
              </div>
            </div>

            <h2 class="page-title">Baseline Model Details</h2>
      `);

      if (regressionResult) {
        const criteriaPassed =
          (regressionResult.statistics.r2 >= 0.75) &&
          (regressionResult.statistics.cvrmse <= 20);

        frameDoc.write(`
            <div class="variables-section">
              <div>
                <div class="variable-item">
                  <strong>Dependent Variable:</strong>
                  <span class="variable-value">${variableSelection.dependent || 'Not selected'}</span>
                </div>
              </div>
              <div>
                <div class="variable-item">
                  <strong>Independent Variables:</strong>
                  <span class="variable-value">${variableSelection.independent.join(', ') || 'None selected'}</span>
                </div>
              </div>
            </div>

            <table class="data-table">
              <thead style="background-color: #EEEFF9 !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                <tr>
                  <th class="separator">Statistic</th>
                  <th>Value</th>
                  <th>IPMVP Criteria</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                  <td class="separator">R²</td>
                  <td>${(regressionResult.statistics.r2 || 0).toFixed(4)}</td>
                  <td>≥ 0.75</td>
                  <td style="color: ${(regressionResult.statistics.r2 || 0) >= 0.75 ? '#22c55e' : '#ef4444'}; font-weight: bold;">
                    ${(regressionResult.statistics.r2 || 0) >= 0.75 ? 'PASS' : 'FAIL'}
                  </td>
                </tr>
                <tr style="background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                  <td class="separator">CV(RMSE)</td>
                  <td>${(regressionResult.statistics.cvrmse).toFixed(1)}%</td>
                  <td>≤ 20%</td>
                  <td style="color: ${regressionResult.statistics.cvrmse <= 20 ? '#22c55e' : '#ef4444'}; font-weight: bold;">
                    ${regressionResult.statistics.cvrmse <= 20 ? 'PASS' : 'FAIL'}
                  </td>
                </tr>
                <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                  <td class="separator">NMBE (Normalized Mean Bias Error)</td>
                  <td>${((regressionResult.statistics.nmbe || 0) * 100).toExponential(1)} %</td>
                  <td>≤ 0.5%</td>
                  <td style="color: ${((regressionResult.statistics.nmbe || 0) * 100) <= 0.5 ? '#22c55e' : '#ef4444'}; font-weight: bold;">
                    ${((regressionResult.statistics.nmbe || 0) * 100) <= 0.5 ? 'PASS' : 'FAIL'}
                  </td>
                </tr>
              </tbody>
            </table>

            <h3>Coefficient Details</h3>
            <table class="data-table">
              <thead style="background-color: #EEEFF9 !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                <tr>
                  <th class="separator">Variable</th>
                  <th>Coefficient</th>
                  <th>t-value (abs ≤ 2)</th>
                  <th>p-value (≤ 0.05)</th>
                </tr>
              </thead>
              <tbody>
                <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                  <td class="separator">Intercept</td>
                  <td>${(regressionResult.coefficients.intercept || 0).toFixed(4)}</td>
                  <td>${(regressionResult.statistics.tStatistics?.intercept || 0).toFixed(4)}</td>
                  <td>${(regressionResult.statistics.pValues?.intercept || 0).toExponential(2)}</td>
                </tr>
                ${Object.entries(regressionResult.coefficients || {})
                  .filter(([key]) => key !== 'intercept')
                  .map(([key, value], index) => `
                    <tr style="background-color: ${index % 2 === 0 ? '#F9FAFF' : 'white'} !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">${key}</td>
                      <td>${(value || 0).toFixed(4)}</td>
                      <td>${(regressionResult.statistics.tStatistics?.[key] || 0).toFixed(4)}</td>
                      <td>${(regressionResult.statistics.pValues?.[key] || 0).toExponential(2)}</td>
                    </tr>
                  `).join('')}
              </tbody>
            </table>

            <h3>Model Equation</h3>
            <div style="background-color: #f9fafb; padding: 0.3cm; border-radius: 0.2cm; border: 1pt solid #ddd; margin-bottom: 0.5cm;">
              <p style="font-family: monospace; font-size: 9pt; margin: 0; white-space: pre-wrap; word-break: break-word; color: #374151;">${regressionResult.equation}</p>
            </div>

            <div style="margin-top: 0.5cm; padding: 0.3cm; border: 1pt solid ${criteriaPassed ? 'green' : 'red'}; border-radius: 0.2cm; background-color: ${criteriaPassed ? 'rgba(0,128,0,0.1)' : 'rgba(255,0,0,0.1)'}; font-size: 9pt;">
              <h3 style="margin-top: 0; color: ${criteriaPassed ? 'green' : 'red'}; font-size: 11pt;">
                Model Validation: ${criteriaPassed ? 'PASSED' : 'FAILED'}
              </h3>
              <p style="margin: 0;">
                The baseline model ${criteriaPassed ? 'meets' : 'does not meet'} IPMVP criteria for statistical validity.
                ${!criteriaPassed ? 'The model should have R² ≥ 0.75 and CVRMSE ≤ 20%.' : ''}
              </p>
            </div>
        `);
      } else {
        frameDoc.write(`
            <p>No regression model available. Please run the model to view details.</p>
        `);
      }

      frameDoc.write(`
          </div>

          <div class="page" style="break-after: avoid; margin-top: 0;">
            <div class="report-header">
              <div style="position: absolute;">
                <img src="${logoSvg}" class="logo" alt="Alto Logo">
              </div>
              <h1>Energy Savings Summary</h1>
              <h2>${reportPeriodText}</h2>
              <div class="report-meta">
                <span><strong>Generated:</strong> ${currentDate}</span>
                <span><strong>Baseline Period:</strong> ${formattedBaselineRange}</span>
                <span><strong>Reporting Period:</strong> ${formattedReportingRange}</span>
              </div>
            </div>
      `);

      if (summaryStats) {
        frameDoc.write(`
            <div class="columns">
              <div class="column">
                <h3 style="margin-top: 0.3cm; margin-bottom: 0.1cm; font-size: 9pt;">Daily Energy Consumption</h3>
                <div>
        `);

        if (reportingData && reportingData.length > 0) {
          const dailyData = reportingData
            .filter(row => {
              if (!row.date || !row.baseline_kwh || !row.actual_kwh) return false;

              const date = new Date(row.date);
              if (isNaN(date.getTime())) return false;

              const rowYear = date.getFullYear();
              const rowMonth = date.getMonth() + 1;

              if (rowYear !== year) return false;
              if (selectedMonth !== 0 && rowMonth !== selectedMonth) return false;

              return true;
            })
            .sort((a, b) => {
              const dateA = new Date(a.date || '');
              const dateB = new Date(b.date || '');
              return dateA.getTime() - dateB.getTime();
            });

          // Limit to 31 rows (31 days)
          const maxRows = 31;
          const displayData = dailyData.slice(0, maxRows);

          if (displayData.length > 0) {
            frameDoc.write(`
              <table class="data-table">
                <thead style="background-color: #EEEFF9 !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                  <tr>
                    <th class="separator">Date</th>
                    <th>Baseline (kWh)</th>
                    <th>Actual (kWh)</th>
                    <th>Savings (kWh)</th>
                    <th>Savings (THB)</th>
                  </tr>
                </thead>
                <tbody>
            `);

            // Calculate totals for the summary row
            let totalBaseline = 0;
            let totalActual = 0;
            let totalSavings = 0;
            let totalSavingsTHB = 0;

            displayData.forEach((row, index) => {
              const date = new Date(row.date || '');
              const baseline = row.baseline_kwh || 0;
              const actual = row.actual_kwh || 0;
              const savings = baseline - actual;
              const rowClass = index % 2 === 0
                ? 'background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;'
                : 'background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;';

              // Get month-specific electricity rate for this day
              const dayMonth = date.getMonth() + 1;
              const dayYear = date.getFullYear();
              const dayRate = getCurrentElectricityRate(dayYear, dayMonth);
              const dayUnitRate = dayRate.totalConsumption > 0 ?
                (dayRate.energyCharge + dayRate.ftCharge) / dayRate.totalConsumption : thbPerKwh;

              // Calculate savings in THB using the day-specific rate
              const savingsTHB = savings * dayUnitRate;

              // Add to totals
              totalBaseline += baseline;
              totalActual += actual;
              totalSavings += savings;
              totalSavingsTHB = (totalSavingsTHB || 0) + savingsTHB;

              frameDoc.write(`
                <tr style="${rowClass}">
                  <td class="separator">${format(date, 'MMM dd, yyyy')}</td>
                  <td>${baseline.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                  <td>${actual.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                  <td>${savings.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                  <td style="color: ${savings >= 0 ? '#22c55e' : '#ef4444'}">${savingsTHB.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                </tr>
              `);
            });

            // Add summary row with emphasized background
            frameDoc.write(`
                <tr style="background-color: #E2E8F0 !important; font-weight: bold; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                  <td class="separator">Total</td>
                  <td>${totalBaseline.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                  <td>${totalActual.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                  <td>${totalSavings.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                  <td style="color: ${totalSavings >= 0 ? '#22c55e' : '#ef4444'}">${totalSavingsTHB.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                </tr>
            `);

            frameDoc.write(`
                </tbody>
              </table>
              <div style="background-color: #f8f9fa; padding: 0.2cm; border-radius: 0.1cm; margin: 0.1cm 0; border: 1pt solid #e2e8f0;">
                <p style="font-size: 7pt; margin: 0.05cm 0; font-weight: bold; color: #1e40af;">
                  Electricity Unit Rate Calculation (THB/kWh) -
                  ${(selectedMonth || 0) > 0 ?
                    `${format(new Date(selectedYear || new Date().getFullYear(), (selectedMonth || 1)-1, 1), 'MMMM yyyy')}` :
                    `${selectedYear || new Date().getFullYear()} (All Months)`}
                </p>
                <hr style="border: none; height: 1px; background-color: #cbd5e1; margin: 0.05cm 0;"/>

                ${(selectedMonth || 0) > 0 ? `
                <!-- Single Month Rate Calculation -->
                <p style="font-size: 7pt; margin: 0.05cm 0; font-style: italic;">Formula: [Energy Charge + Fuel Adjustment Charge (Ft)] / Total Energy Consumption</p>

                <table style="width: 100%; border-collapse: collapse; font-size: 7pt; margin-top: 0.05cm;">
                  <tr>
                    <td style="width: 60%; padding: 0.02cm 0;">
                      <span style="display: inline-block; width: 65%;">Total Energy Consumption (kWh):</span>
                      <span style="color: #22c55e; font-weight: bold;">${currentRateValues.totalConsumption.toLocaleString()}</span>
                    </td>
                    <td style="width: 40%; padding: 0.02cm 0;">
                      <span style="display: inline-block; width: 40%;">Calculation:</span>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 0.02cm 0;">
                      <span style="display: inline-block; width: 65%;">Energy Charge (THB):</span>
                      <span style="color: #ef4444; font-weight: bold;">${currentRateValues.energyCharge.toLocaleString(undefined, {maximumFractionDigits: 2})}</span>
                    </td>
                    <td style="padding: 0.02cm 0;">
                      <span style="color: #ef4444;">${currentRateValues.energyCharge.toLocaleString(undefined, {maximumFractionDigits: 2})}</span> <span>+</span>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 0.02cm 0;">
                      <span style="display: inline-block; width: 65%;">Fuel Adjustment Charge (Ft) (THB):</span>
                      <span style="color: #f59e0b; font-weight: bold;">${currentRateValues.ftCharge.toLocaleString(undefined, {maximumFractionDigits: 2})}</span>
                    </td>
                    <td style="padding: 0.02cm 0;">
                      <span style="color: #f59e0b;">${currentRateValues.ftCharge.toLocaleString(undefined, {maximumFractionDigits: 2})}</span>
                    </td>
                  </tr>
                  <tr>
                    <td></td>
                    <td style="padding: 0.02cm 0;">
                      <span style="display: inline-block; width: 95%; border-bottom: 1px solid #64748b;"></span>
                    </td>
                  </tr>
                  <tr>
                    <td></td>
                    <td style="padding: 0.02cm 0;">
                      <span style="color: #22c55e;">${currentRateValues.totalConsumption.toLocaleString()}</span>
                    </td>
                  </tr>
                  <tr>
                    <td></td>
                    <td style="padding: 0.02cm 0;">
                      <span style="display: inline-block; width: 95%; border-bottom: 1px solid #64748b;"></span>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 0.02cm 0;">
                      <span style="display: inline-block; width: 65%;">Unit Rate (THB/kWh):</span>
                      <span style="color: #1e40af; font-weight: bold; font-size: 8pt; background-color: #dbeafe; padding: 0.02cm 0.1cm; border-radius: 0.05cm;">
                        ${(currentRateValues.totalConsumption > 0 ?
                          (currentRateValues.energyCharge + currentRateValues.ftCharge) / currentRateValues.totalConsumption :
                          thbPerKwh).toFixed(4)}
                      </span>
                    </td>
                    <td style="padding: 0.02cm 0;">
                      <span style="color: #1e40af; font-weight: bold;">
                        ${(currentRateValues.totalConsumption > 0 ?
                          (currentRateValues.energyCharge + currentRateValues.ftCharge) / currentRateValues.totalConsumption :
                          thbPerKwh).toFixed(4)}
                      </span>
                    </td>
                  </tr>
                </table>
                ` : `
                <!-- All Months Rate Table -->
                <p style="font-size: 7pt; margin: 0.05cm 0; font-style: italic;">Monthly electricity rates used for calculations:</p>

                <table style="width: 100%; border-collapse: collapse; font-size: 7pt; margin-top: 0.05cm; border: 1px solid #e2e8f0;">
                  <thead>
                    <tr style="background-color: #f1f5f9;">
                      <th style="padding: 0.05cm; text-align: left; border: 1px solid #e2e8f0;">Month</th>
                      <th style="padding: 0.05cm; text-align: right; border: 1px solid #e2e8f0;">Energy Charge (THB)</th>
                      <th style="padding: 0.05cm; text-align: right; border: 1px solid #e2e8f0;">Ft Charge (THB)</th>
                      <th style="padding: 0.05cm; text-align: right; border: 1px solid #e2e8f0;">Consumption (kWh)</th>
                      <th style="padding: 0.05cm; text-align: right; border: 1px solid #e2e8f0;">Unit Rate (THB/kWh)</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${(() => {
                      // Get the reporting period months
                      const reportingStartDate = reportingRange?.start ? new Date(reportingRange.start) : null;
                      const reportingEndDate = reportingRange?.end ? new Date(reportingRange.end) : null;

                      let monthsHtml = '';

                      if (reportingStartDate && reportingEndDate &&
                          !isNaN(reportingStartDate.getTime()) && !isNaN(reportingEndDate.getTime())) {

                        const startYear = reportingStartDate.getFullYear();
                        const startMonth = reportingStartDate.getMonth() + 1;
                        const endYear = reportingEndDate.getFullYear();
                        const endMonth = reportingEndDate.getMonth() + 1;

                        // Generate rows for each month in the reporting period
                        for (let year = startYear; year <= endYear; year++) {
                          const monthStart = year === startYear ? startMonth : 1;
                          const monthEnd = year === endYear ? endMonth : 12;

                          for (let month = monthStart; month <= monthEnd; month++) {
                            const monthRate = getCurrentElectricityRate(year, month);
                            const monthUnitRate = monthRate.totalConsumption > 0 ?
                              (monthRate.energyCharge + monthRate.ftCharge) / monthRate.totalConsumption : thbPerKwh;

                            const rowStyle = (month % 2 === 0) ? 'background-color: #ffffff;' : 'background-color: #f8fafc;';

                            monthsHtml += `
                              <tr style="${rowStyle}">
                                <td style="padding: 0.05cm; border: 1px solid #e2e8f0;">${format(new Date(year, month-1, 1), 'MMM yyyy')}</td>
                                <td style="padding: 0.05cm; text-align: right; border: 1px solid #e2e8f0;">${monthRate.energyCharge.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                                <td style="padding: 0.05cm; text-align: right; border: 1px solid #e2e8f0;">${monthRate.ftCharge.toLocaleString(undefined, {maximumFractionDigits: 2})}</td>
                                <td style="padding: 0.05cm; text-align: right; border: 1px solid #e2e8f0;">${monthRate.totalConsumption.toLocaleString()}</td>
                                <td style="padding: 0.05cm; text-align: right; border: 1px solid #e2e8f0; font-weight: bold; color: #1e40af;">${monthUnitRate.toFixed(4)}</td>
                              </tr>
                            `;
                          }
                        }
                      }

                      return monthsHtml;
                    })()}
                  </tbody>
                </table>
                `}

              </div>
            `);
          } else {
            frameDoc.write(`
              <p>No daily data available for the selected period.</p>
            `);
          }
        } else {
          frameDoc.write(`
            <p>No daily data available.</p>
          `);
        }

        frameDoc.write(`
                </div>
              </div>

              <div class="column">
        `);

        if (reportingData && reportingData.length > 0) {
          const monthMap = new Map<string, { baseline: number; actual: number; count: number }>();

          // Parse reporting range dates
          const startDate = reportingRange?.start ? new Date(reportingRange.start) : null;
          const endDate = reportingRange?.end ? new Date(reportingRange.end) : null;

          // Only proceed if we have valid reporting range dates
          if (startDate && endDate && !isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
            const startYear = startDate.getFullYear();
            const startMonth = startDate.getMonth() + 1;
            const endYear = endDate.getFullYear();
            const endMonth = endDate.getMonth() + 1;

            // Filter data based on reporting period
            const filteredData = reportingData.filter(row => {
              if (!row.date || !row.baseline_kwh || !row.actual_kwh) return false;

              const date = new Date(row.date);
              if (isNaN(date.getTime())) return false;

              const rowYear = date.getFullYear();
              const rowMonth = date.getMonth() + 1;

              // Check if the date is within the reporting period
              if (rowYear < startYear || (rowYear === startYear && rowMonth < startMonth)) {
                return false; // Before start date
              }

              if (rowYear > endYear || (rowYear === endYear && rowMonth > endMonth)) {
                return false; // After end date
              }

              return true; // Within reporting period
            });

            filteredData.forEach(row => {
              const date = new Date(row.date);
              const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

              const existingData = monthMap.get(monthKey) || { baseline: 0, actual: 0, count: 0 };
              monthMap.set(monthKey, {
                baseline: existingData.baseline + (row.baseline_kwh || 0),
                actual: existingData.actual + (row.actual_kwh || 0),
                count: existingData.count + 1
              });
            });
          }

          frameDoc.write(`
                <h3 style="margin-top: 0.3cm; margin-bottom: 0.1cm; font-size: 9pt;">Monthly Summary</h3>
                <table class="data-table">
                  <thead style="background-color: #EEEFF9 !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                    <tr>
                      <th class="separator">Month</th>
                      <th>Baseline (kWh)</th>
                      <th>Actual (kWh)</th>
                      <th>Savings (kWh)</th>
                      <th>Savings (%)</th>
                      <th>Savings (THB)</th>
                      <th>AltoTech ${currentRateValues.altoTechSharePercentage || 70}% (THB)</th>
                      <th>${currentRateValues.customerName || 'Customer'} ${currentRateValues.customerSharePercentage || 30}% (THB)</th>
                    </tr>
                  </thead>
                  <tbody>
          `);

          // Get the reporting period months
          const reportingStartDate = reportingRange?.start ? new Date(reportingRange.start) : null;
          const reportingEndDate = reportingRange?.end ? new Date(reportingRange.end) : null;

          // We'll use the reporting range dates to determine which months to show

          // Always show all 12 months with empty cells for months without data
          for (let month = 1; month <= 12; month++) {
            const monthKey = `${year}-${String(month).padStart(2, '0')}`;
            const data = monthMap.get(monthKey) || { baseline: 0, actual: 0, count: 0 };

            const monthName = format(new Date(year, month - 1, 1), 'MMM yyyy');
            const rowClass = (month-1) % 2 === 0
              ? 'background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;'
              : 'background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;';

            // Check if this month is within the reporting period
            const monthDate = new Date(year, month - 1, 15); // Middle of the month
            const isInReportingPeriod = reportingStartDate && reportingEndDate ?
                                       (monthDate >= reportingStartDate && monthDate <= reportingEndDate) : true;

            // If there's no data for this month or it's not in the reporting period, display empty cells
            if ((data.baseline === 0 && data.actual === 0) || !isInReportingPeriod) {
              frameDoc.write(`
                <tr style="${rowClass}">
                  <td class="separator">${monthName}</td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>
              `);
            } else {
              // If there's data, display it
              const baseline = data.baseline;
              const actual = data.actual;
              const savings = baseline - actual;
              const savingsPercentage = baseline !== 0 ? (savings / baseline) * 100 : 0;

              // Get month-specific electricity rate
              const monthRate = getCurrentElectricityRate(year, month);
              const monthUnitRate = monthRate.totalConsumption > 0 ?
                (monthRate.energyCharge + monthRate.ftCharge) / monthRate.totalConsumption : thbPerKwh;

              frameDoc.write(`
                <tr style="${rowClass}">
                  <td class="separator">${monthName}</td>
                  <td>${baseline.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                  <td>${actual.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                  <td>${savings.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                  <td style="color: ${savingsPercentage >= 0 ? '#22c55e' : '#ef4444'}">${savingsPercentage.toFixed(2)}%</td>
                  <td>${(savings * monthUnitRate).toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                  <td>${(savings * monthUnitRate * (monthRate.altoTechSharePercentage || 70) / 100).toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                  <td>${(savings * monthUnitRate * (monthRate.customerSharePercentage || 30) / 100).toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                </tr>
              `);
            }
          }

          frameDoc.write(`
                  </tbody>
                </table>
          `);
        }

        const yearData = new Map<string, { baseline: number; actual: number }>();

        // Parse reporting range dates
        const reportingStartDate = reportingRange?.start ? new Date(reportingRange.start) : null;
        const reportingEndDate = reportingRange?.end ? new Date(reportingRange.end) : null;

        // Filter data based on reporting period
        const filteredYearlyData = reportingData.filter(row => {
          if (!row.date || !row.baseline_kwh || !row.actual_kwh) return false;

          const date = new Date(row.date);
          if (isNaN(date.getTime())) return false;

          // Check if the date is within the reporting period
          if (reportingStartDate && reportingEndDate) {
            return date >= reportingStartDate && date <= reportingEndDate;
          }

          return true; // If no valid reporting range, include all data
        });

        filteredYearlyData.forEach(row => {
          if (!row.date || !row.baseline_kwh || !row.actual_kwh) return;

          const date = new Date(row.date);
          if (!isNaN(date.getTime())) {
            const yearKey = date.getFullYear().toString();

            const existingData = yearData.get(yearKey) || { baseline: 0, actual: 0 };
            yearData.set(yearKey, {
              baseline: existingData.baseline + (row.baseline_kwh || 0),
              actual: existingData.actual + (row.actual_kwh || 0)
            });
          }
        });

        if (yearData.size > 0) {
          frameDoc.write(`
                <h3 style="margin-top: 0.1cm; margin-bottom: 0.1cm; font-size: 9pt;">Yearly Summary</h3>
                <table class="data-table">
                  <thead style="background-color: #EEEFF9 !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                    <tr>
                      <th class="separator">Year</th>
                      <th>Baseline (kWh)</th>
                      <th>Actual (kWh)</th>
                      <th>Savings (kWh)</th>
                      <th>Savings (THB)</th>
                      <th>AltoTech ${currentRateValues.altoTechSharePercentage || 70}% (THB)</th>
                      <th>${currentRateValues.customerName || 'Customer'} ${currentRateValues.customerSharePercentage || 30}% (THB)</th>
                    </tr>
                  </thead>
                  <tbody>
          `);

          Array.from(yearData.entries())
            .sort((a, b) => parseInt(a[0]) - parseInt(b[0]))
            .slice(-3)
            .forEach(([year, data], index) => {
              const baseline = data.baseline;
              const actual = data.actual;
              const savings = baseline - actual;
              const rowClass = index % 2 === 0
                ? 'background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;'
                : 'background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;';

              // Get year-specific electricity rate (month 0 means all months in that year)
              const yearRate = getCurrentElectricityRate(parseInt(year), 0);
              const yearUnitRate = yearRate.totalConsumption > 0 ?
                (yearRate.energyCharge + yearRate.ftCharge) / yearRate.totalConsumption : thbPerKwh;

              frameDoc.write(`
                    <tr style="${rowClass}">
                      <td class="separator">${year}</td>
                      <td>${baseline.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                      <td>${actual.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                      <td>${savings.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                      <td>${(savings * yearUnitRate).toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                      <td>${(savings * yearUnitRate * (yearRate.altoTechSharePercentage || 70) / 100).toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                      <td>${(savings * yearUnitRate * (yearRate.customerSharePercentage || 30) / 100).toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                    </tr>
              `);
            });

          frameDoc.write(`
                  </tbody>
                </table>
          `);
        }

        if (year > 2020 && (selectedMonth !== 0 || yearData.size <= 2)) {
          frameDoc.write(`
                <h3 style="margin-top: 0.1cm; margin-bottom: 0.1cm; font-size: 9pt;">Comparative</h3>
                <table class="data-table">
                  <thead style="background-color: #EEEFF9 !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                    <tr>
                      <th rowspan="2" class="separator">Month</th>
                      <th colspan="1" style="text-align: center; border-bottom: 1pt solid #DBE4FF; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">${year-1}</th>
                      <th colspan="1" style="text-align: center; border-bottom: 1pt solid #DBE4FF; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">${year}</th>
                    </tr>
                    <tr>
                      <th style="-webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">kWh</th>
                      <th style="-webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">kWh</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Jan</td>
                      <td>572,476</td>
                      <td>410,810</td>
                    </tr>
                    <tr style="background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Feb</td>
                      <td>629,752</td>
                      <td>491,385</td>
                    </tr>
                    <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Mar</td>
                      <td>708,447</td>
                      <td>624,284</td>
                    </tr>
                    <tr style="background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Apr</td>
                      <td>772,741</td>
                      <td>-</td>
                    </tr>
                    <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">May</td>
                      <td>774,199</td>
                      <td>-</td>
                    </tr>
                    <tr style="background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Jun</td>
                      <td>712,326</td>
                      <td>-</td>
                    </tr>
                    <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Jul</td>
                      <td>698,541</td>
                      <td>-</td>
                    </tr>
                    <tr style="background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Aug</td>
                      <td>727,417</td>
                      <td>-</td>
                    </tr>
                    <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Sep</td>
                      <td>708,868</td>
                      <td>-</td>
                    </tr>
                    <tr style="background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Oct</td>
                      <td>706,513</td>
                      <td>-</td>
                    </tr>
                    <tr style="background-color: white !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Nov</td>
                      <td>630,140</td>
                      <td>-</td>
                    </tr>
                    <tr style="background-color: #F9FAFF !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important;">
                      <td class="separator">Dec</td>
                      <td>548,823</td>
                      <td>-</td>
                    </tr>
                  </tbody>
                </table>
          `);
        }

        frameDoc.write(`
                <div style="margin-top: 0.5cm; page-break-inside: avoid;">
                  <div style="display: flex; justify-content: space-between; margin-top: 0.3cm;">
                    <div style="width: 45%;">
                      <div style="height: 1.5cm;"></div>
                      <div style="border-top: 1pt solid #333; padding-top: 0.2cm;">
                        <div style="display: flex; justify-content: space-between; font-size: 8pt; color: #666;">
                          <span>Name:</span>
                        </div>
                        <p style="margin: 0.2cm 0 0 0; font-size: 9pt; text-align: center;">Reporter (AltoTech Global)</p>
                      </div>
                    </div>
                    <div style="width: 45%;">
                      <div style="height: 1.5cm;"></div>
                      <div style="border-top: 1pt solid #333; padding-top: 0.2cm;">
                        <div style="display: flex; justify-content: space-between; font-size: 8pt; color: #666;">
                          <span>Name:</span>
                        </div>
                        <p style="margin: 0.2cm 0 0 0; font-size: 9pt; text-align: center;">Approver (CPN)</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
        `);
      } else {
        frameDoc.write(`
            <p>No summary statistics available. Please generate a report to view summary details.</p>
            </div>
        `);
      }

      frameDoc.write(`
        </body>
        </html>
      `);

      frameDoc.close();

      setTimeout(() => {
        printFrame.contentWindow?.focus();
        printFrame.contentWindow?.print();

        setTimeout(() => {
          document.body.removeChild(printFrame);
        }, 1000);

        toast.success('PDF export initiated');
      }, 500);
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export PDF - please check console for details');
    }
  };

  return (
    <Button
      variant={variant}
      className={className}
      onClick={handleExport}
      disabled={!baselineData.length || !reportingData.length}
      size={size}
    >
      {children || (
        <>
          <FileDown size={16} />
          Export PDF
        </>
      )}
    </Button>
  );
};

export default ExportButton;
