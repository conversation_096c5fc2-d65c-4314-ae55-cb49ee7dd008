'use client';

import React, { useState } from 'react';
import { DateRangePicker } from '../../components/ui/DateRangePicker';
import RoomFilterDropdown from './components/RoomFilterDropDown';
import AQIFilterDropdown from './components/AQIFilterDropDown';
import { BUILDING_CONFIG } from '@/building-mock-data';
import { Heatmap } from './components/Heatmap';
import RangeSelector from './components/RangeSelector';
import DynamicRangeSelector from './components/DynamicRangeSelector';

const DATA_FILE_NAME = 'mock-hotel-config.ts';

const RoomAnalytics: React.FC = () => {
  const [selectedMetric, setSelectedMetric] = useState<string>('co2');
  const [selectedRoomType, setSelectedRoomType] = useState<string>('All');
  const [selectedCell, setSelectedCell] = useState<{ row: number; col: number } | null>(null);
  const [valueRange, setValueRange] = useState<[number, number]>([0, 100]); // range state

  const handleCellClick = (roomId: string, value: number | null) => {
    if (!value) return;
    const floor = parseInt(roomId.slice(0, -2), 10) - 1;
    const room = parseInt(roomId.slice(-2), 10) - 1;
    setSelectedCell({ row: floor, col: room });
  };

  return (
    <div className="w-full h-[calc(100vh-71px)] bg-background p-6 overflow-hidden">
      <div className="flex h-full gap-4">
        {/* Left side */}
        <div className="flex flex-col w-9/12 gap-4">
          <div className="flex justify-end">
            <DateRangePicker />
          </div>

          <div className="bg-white rounded-xl shadow p-6 flex-1 overflow-auto">
            {selectedCell ? (
              <>
                <h2 className="text-xl font-medium mb-4">
                  Room Floor {selectedCell.row + 1}, Room {selectedCell.col + 1}
                </h2>
                <p className="text-gray-600">
                  You selected <strong>Floor {selectedCell.row + 1}</strong>,{' '}
                  <strong>Room {selectedCell.col + 1}</strong>.
                </p>
              </>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-400 text-lg">
                Click a cell in the heatmap to see details.
              </div>
            )}
          </div>
        </div>

        {/* Configuration panel */}
        <div className="w-3/12 bg-white rounded-xl shadow p-4 overflow-visible space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-medium">Configuration</h2>
            <RoomFilterDropdown
              selectedRoomType={selectedRoomType}
              onSelectRoomType={setSelectedRoomType}
            />
          </div>

          <div className="flex items-center gap-4">
           <div>
              <h2 className="text-xl font-bold mb-4">AQI Filter</h2>
              <DynamicRangeSelector />
            </div>

          </div>

          {/* Heatmap */}
          <div>
            <Heatmap
              buildingConfig={BUILDING_CONFIG}
              floorsCount={14}
              roomsPerFloor={30}
              dataFileName={DATA_FILE_NAME}
              selectedMetric={selectedMetric}
              selectedRoomType={selectedRoomType}
              onCellClick={handleCellClick}
              valueRange={valueRange} // dynamic range
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomAnalytics;
