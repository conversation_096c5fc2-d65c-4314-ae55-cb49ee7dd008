'use client';

import React, { useState, useEffect } from 'react';
import { DateRangePicker } from '../../components/ui/DateRangePicker';
import RoomFilterDropdown from './components/RoomFilterDropDown';
import { BUILDING_CONFIG } from '@/building-mock-data';
import { Heatmap } from './components/Heatmap';
import ThresholdSelector from './components/ThresholdSelector';

const DATA_FILE_NAME = 'mock-hotel-config.ts';

// Define metric ranges configuration for useEffect
const IAQ_METRIC_RANGES: Record<string, { min: number; max: number; step?: number; unit: string }> = {
  'co2': { min: 400, max: 2000, step: 50, unit: 'ppm' },
  'temperature': { min: 10, max: 40, step: 1, unit: '°C' },
  'pm25': { min: 0, max: 150, step: 5, unit: 'µg/m³' },
  'humidity': { min: 0, max: 100, step: 5, unit: '%' },
  'noise': { min: 0, max: 100, step: 5, unit: 'dB' },
  'illuminance': { min: 0, max: 1000, step: 50, unit: 'lux' },
  'pmv': { min: -3, max: 3, step: 0.1, unit: '' },
};

const RoomAnalytics: React.FC = () => {
  const [selectedMetric, setSelectedMetric] = useState<string>('co2');
  const [selectedRoomType, setSelectedRoomType] = useState<string>('All');
  const [selectedCell, setSelectedCell] = useState<{ row: number; col: number } | null>(null);
  const [valueRange, setValueRange] = useState<[number, number]>([400, 2000]); // Initialize with CO2 range

  // Update range when metric changes
  useEffect(() => {
    const rangeConfig = IAQ_METRIC_RANGES[selectedMetric];
    if (rangeConfig) {
      setValueRange([rangeConfig.min, rangeConfig.max]);
    }
  }, [selectedMetric]);

  const handleMetricChange = (metric: string) => {
    setSelectedMetric(metric);
  };

  const handleCellClick = (roomId: string, value: number | null) => {
    if (!value) return;
    const floor = parseInt(roomId.slice(0, -2), 10) - 1;
    const room = parseInt(roomId.slice(-2), 10) - 1;
    setSelectedCell({ row: floor, col: room });
  };

  return (
    <div className="w-full h-[calc(100vh-71px)] bg-background p-6 overflow-hidden">
      <div className="flex h-full gap-4">
        {/* Left side */}
        <div className="flex flex-col w-9/12 gap-4">
          <div className="flex justify-end">
            <DateRangePicker />
          </div>

          <div className="bg-white rounded-xl shadow p-6 flex-1 overflow-auto">
            {selectedCell ? (
              <>
                <h2 className="text-xl font-medium mb-4">
                  Room Floor {selectedCell.row + 1}, Room {selectedCell.col + 1}
                </h2>
                <p className="text-gray-600">
                  You selected <strong>Floor {selectedCell.row + 1}</strong>,{' '}
                  <strong>Room {selectedCell.col + 1}</strong>.
                </p>
              </>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-400 text-lg">
                Click a cell in the heatmap to see details.
              </div>
            )}
          </div>
        </div>

        {/* Configuration panel */}
        <div className="w-3/12 bg-white rounded-xl shadow p-4 overflow-visible space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-medium">Configuration</h2>
            <RoomFilterDropdown
              selectedRoomType={selectedRoomType}
              onSelectRoomType={setSelectedRoomType}
            />
          </div>

          <ThresholdSelector
            selectedMetric={selectedMetric}
            valueRange={valueRange}
            onMetricChange={handleMetricChange}
            onRangeChange={setValueRange}
          />

          {/* Heatmap */}
          <div>
            <Heatmap
              buildingConfig={BUILDING_CONFIG}
              floorsCount={14}
              roomsPerFloor={30}
              dataFileName={DATA_FILE_NAME}
              selectedMetric={selectedMetric}
              selectedRoomType={selectedRoomType}
              onCellClick={handleCellClick}
              valueRange={valueRange} // dynamic range
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomAnalytics;
