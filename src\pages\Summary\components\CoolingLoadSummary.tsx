import React from "react";
import MetricsSummary from "./MetricsSummary";

interface CoolingLoadSummaryProps {
  plantLoad: number;
}

const CoolingLoadSummary: React.FC<CoolingLoadSummaryProps> = ({
  plantLoad = 0, // Default to plant load since airside doesn't exist
}) => {
  // Create metrics directly without using SystemSummary component
  const metrics = [
    {
      label: "Total Cooling Load",
      value: plantLoad,
      precision: 0,
      unit: "RT"
    }
  ];

  return (
    <MetricsSummary 
      title="Cooling Load"
      metrics={metrics}
    />
  );
};

export default CoolingLoadSummary;
