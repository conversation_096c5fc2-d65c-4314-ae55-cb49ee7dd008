import { api } from "./api";

// Interfaces for API responses and data
export interface ZonesApiResponse {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: {
    floor: ZoneData[];
  };
}

export interface ZoneData {
  id: number;
  device_id: string;
  name: string;
  metadata: {
    description?: string;
    created_from?: string;
  };
  devices: DeviceApiData[];
}

export interface DeviceApiData {
  id: number;
  device_id: string;
  name: string;
  model: string;
  metadata: {
    unit?: string;
    zone?: string;
    floor?: string;
    config?: any;
    format?: string;
    created_from?: string;
  };
  autopilot?: {
    id: number;
    autopilot_status: string;
    updated_by: null | string;
    updated_at: string;
  };
}
// TODO This device interface is duplicate of the DeviceApiData interface. Need to refactor and merge them. But add this just to avoid breaking old code.
// Simplified device interface for dropdown lists and basic usage
export interface SimpleDevice {
  id: string;
  name: string;
  model: string;
  zone?: string;
  floor?: string;
  unit?: string;
}

export interface DeviceData {
  id: number;
  status: "normal" | "off" | "warning" | "alarm";
  deviceName: string;
  deviceId: string;
  floor: string;
  zone: string;
  unit: string;
  model: string;
  autopilotStatus: string;
  lastUpdated: string;
}

export interface GroupsData {
  [key: string]: DeviceData[];
}

export interface ProcessedDeviceData {
  groupedData: GroupsData;
  floorNames: string[];
}

// API response wrapper interface
export interface ApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: T;
}

/**
 * Map autopilot status to UI status types
 */
export const mapAutopilotStatusToStatus = (
  autopilotStatus: string | undefined
): "normal" | "off" | "warning" | "alarm" => {
  switch (autopilotStatus) {
    case "active":
      return "normal";
    case "inactive":
      return "off";
    case "warning":
      return "warning";
    case "error":
      return "alarm";
    default:
      return "off";
  }
};

/**
 * Check if a device should be included in the heat map
 */
const shouldIncludeDevice = (device: DeviceApiData): boolean => {
  // Check if it's a VAV, AHU, or VSD_AHU device by model
  if (
    device.model?.toLowerCase() === "vav" ||
    device.model?.toLowerCase() === "ahu" ||
    device.model?.toLowerCase() === "vsd_ahu"
  ) {
    return true;
  }

  // Check floor number
  const floor = device.metadata?.floor;
  if (!floor) return false;

  // Filter out 'default_floor'
  if (floor === "default_floor") return false;

  // Check if floor is a number between 7-18
  const floorNum = parseInt(floor);
  if (!isNaN(floorNum) && floorNum >= 7 && floorNum <= 18) {
    return true;
  }

  return false;
};

/**
 * Process API data into the format needed by the component
 */
export const processApiData = (zonesData: ZoneData[]): ProcessedDeviceData => {
  const groupedData: GroupsData = {};
  const floorSet = new Set<string>();
  let totalDevices = 0;
  let includedDevices = 0;

  // Iterate through zones and their devices
  zonesData.forEach((zone) => {
    // Skip if zone has no devices
    if (!Array.isArray(zone.devices) || zone.devices.length === 0) {
      return;
    }

    // Process each device in the zone
    zone.devices.forEach((device) => {
      totalDevices++;

      // Skip devices that don't match our filter criteria
      if (!shouldIncludeDevice(device)) {
        return;
      }

      includedDevices++;

      if (!device.metadata) {
        console.warn("Device missing metadata:", device);
        return;
      }

      const floor = device.metadata.floor || "Unknown";
      floorSet.add(floor);

      if (!groupedData[`floor-${floor}`]) {
        groupedData[`floor-${floor}`] = [];
      }

      const processedDevice: DeviceData = {
        id: device.id,
        status: mapAutopilotStatusToStatus(device.autopilot?.autopilot_status),
        deviceName: device.name,
        deviceId: device.device_id,
        floor: floor,
        zone: device.metadata.zone || zone.name || "Unknown",
        unit: device.metadata.unit || "Unknown",
        model: device.model || "Unknown",
        autopilotStatus: device.autopilot?.autopilot_status || "inactive",
        lastUpdated: device.autopilot?.updated_at || new Date().toISOString(),
      };

      groupedData[`floor-${floor}`].push(processedDevice);
    });
  });

  // Sort floors and convert to array of names
  const floorNames = Array.from(floorSet)
    .sort((a, b) => {
      // Try to sort numerically if possible
      const numA = parseInt(a);
      const numB = parseInt(b);
      if (!isNaN(numA) && !isNaN(numB)) {
        return numA - numB;
      }
      // Fall back to string comparison
      return a.localeCompare(b);
    })
    .map((floor) => `Floor ${floor}`);

  console.log(
    `Device filtering: ${includedDevices} of ${totalDevices} devices included (${Math.round(
      (includedDevices / totalDevices) * 100
    )}%)`
  );
  console.log(`Floors found:`, Array.from(floorSet));

  return { groupedData, floorNames };
};

/**
 * Fetch all devices with their zones
 */
export const getDevicesWithZones = async (): Promise<ProcessedDeviceData> => {
  try {
    // Use the correct API endpoint
    const response = await api.get("/zones/?expand=devices&group_by_type=true");
    const responseData = response.data as ZonesApiResponse;

    if (
      responseData.success &&
      responseData.data &&
      Array.isArray(responseData.data.floor)
    ) {
      console.log("API Response:", responseData);
      // Process and group the data by floor
      return processApiData(responseData.data.floor);
    }

    console.error("Invalid API response format:", responseData);
    throw new Error("Invalid data format received from API");
  } catch (error) {
    console.error("Error fetching device zone data:", error);
    throw error;
  }
};

/**
 * Fetch a simplified list of all devices
 * Useful for dropdowns and select lists
 */
export const getDevices = async (): Promise<SimpleDevice[]> => {
  try {
    const response = await api.get<ApiResponse<DeviceApiData[]>>("/devices/");

    if (!response.data.success || !Array.isArray(response.data.data)) {
      console.error("Invalid API response format:", response.data);
      throw new Error("Invalid data format received from API");
    }

    const devices = response.data.data;

    // Transform to simplified format
    return devices.map((device) => ({
      id: device.device_id,
      name: device.name,
      model: device.model,
      zone: device.metadata?.zone,
      floor: device.metadata?.floor,
      unit: device.metadata?.unit,
    }));
  } catch (error) {
    console.error("Error fetching devices:", error);
    throw error;
  }
};
