import React, { useMemo, useEffect, useRef } from 'react';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { CardContent } from '@/components/ui/card';
import { DataPoint } from '@/types/summaryTypes';
import { DateTime } from 'luxon';
import { useAuth } from '@/contexts/AuthContext';

interface ScatterPlotProps {
  data?: DataPoint[];
  dates?: string[]; // For coloring and legend
  xAxisLabel?: string;
  yAxisLabel?: string;
  startDate?: Date | null; // For filtering
  endDate?: Date | null; // For filtering
}

const ScatterPlot: React.FC<ScatterPlotProps> = ({ 
  data = [],
  xAxisLabel = 'Cooling Load (RT)',
  yAxisLabel = 'TSE (kW/RT)',
  startDate = null,
  endDate = null
}) => {
  // Get timezone from auth context
  const { site } = useAuth();
  const timezone = site?.timezone || 'Asia/Bangkok';

  const chartRef = useRef<ReactECharts>(null);
  
  // Ensure proper cleanup on unmount
  useEffect(() => {
    return () => {
      try {
        chartRef.current?.getEchartsInstance()?.dispose();
      } catch (e) {
        console.warn('Failed to dispose chart:', e);
      }
    };
  }, []);

  // Filter data based on date range
  const filteredData = useMemo(() => {
    // Ensure data is an array before using filter
    if (!Array.isArray(data)) return [];
    if (!startDate || !endDate || data.length === 0) return data;
    
    // Convert start and end dates to Luxon DateTime objects with timezone
    const start = DateTime.fromJSDate(startDate).setZone(timezone).startOf('day');
    const end = DateTime.fromJSDate(endDate).setZone(timezone).endOf('day');
    
    return data.filter(point => {
      // Try to use timestamp if available
      if (point.timestamp) {
        const pointDate = DateTime.fromISO(point.timestamp).setZone(timezone);
        return pointDate >= start && pointDate <= end;
      }
      
      // Fallback to date string (assuming format MM-DD or similar)
      if (point.date) {
        // We need to assume a year - let's use the startDate's year
        const year = start.year;
        const [month, day] = point.date.split('-').map(Number);
        
        if (!isNaN(month) && !isNaN(day)) {
          // Create a Luxon DateTime object
          const pointDate = DateTime.local(year, month, day).setZone(timezone);
          return pointDate >= start && pointDate <= end;
        }
      }
      
      // If we can't determine the date, include the point by default
      return true;
    });
  }, [data, startDate, endDate, timezone]);
  
  // Process data into scatter plot format
  const processScatterData = useMemo(() => {
    const result: Record<string, Array<[number, number]>> = {};
    
    if (!Array.isArray(filteredData)) return result;
    
    filteredData.forEach(point => {
      const date = point.date || 'Unknown';
      
      if (!result[date]) {
        result[date] = [];
      }
      
      // Make sure both arrays have the same length and handle possible undefined/null values
      if (!Array.isArray(point.coolingLoad) || !Array.isArray(point.tse)) {
        // Skip this point if either array is not actually an array
        return;
      }
      
      const length = Math.min(point.coolingLoad.length, point.tse.length);
      
      // Create paired points
      for (let i = 0; i < length; i++) {
        const x = point.coolingLoad[i];
        const y = point.tse[i];
        
        // Skip invalid values
        if (x === undefined || y === undefined || isNaN(x) || isNaN(y)) {
          continue;
        }
        
        result[date].push([x, y]);
      }
    });
    
    return result;
  }, [filteredData]);
  
  const getOption = (): EChartsOption => {
    // Default values if no data is available
    if (!filteredData || filteredData.length === 0 || Object.keys(processScatterData).length === 0) {
      return {
        backgroundColor: '#ffffff',
        grid: {
          left: '10%',
          right: '5%',
          bottom: '15%',
          top: '30px',
          containLabel: true
        },
        xAxis: { 
          type: 'value',
          name: xAxisLabel,
          nameLocation: 'middle',
          nameGap: 30,
          min: 0,
          max: 100,
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#ddd'
            }
          }
        },
        yAxis: { 
          type: 'value',
          name: yAxisLabel,
          nameLocation: 'middle',
          nameGap: 35,
          min: 0,
          max: 2
        },
        series: [{
          type: 'scatter',
          data: [],
          name: 'No Data'
        }]
      };
    }
    
    try {
      // Calculate min and max values across all data points for proper axis scaling
      let allX: number[] = [];
      let allY: number[] = [];
      
      Object.values(processScatterData).forEach(points => {
        points.forEach(point => {
          if (point && point.length >= 2) {
            allX.push(point[0]);
            allY.push(point[1]);
          }
        });
      });
      
      // Default values if no valid data points
      if (allX.length === 0 || allY.length === 0) {
        return {
          backgroundColor: '#ffffff',
          grid: {
            left: '10%',
            right: '5%',
            bottom: '15%',
            top: '30px',
            containLabel: true
          },
          xAxis: { 
            type: 'value',
            name: xAxisLabel,
            nameLocation: 'middle',
            nameGap: 30,
            min: 0,
            max: 100
          },
          yAxis: { 
            type: 'value',
            name: yAxisLabel,
            nameLocation: 'middle',
            nameGap: 35,
            min: 0,
            max: 2
          },
          series: [{
            type: 'scatter',
            data: [],
            name: 'No Valid Data'
          }]
        };
      }
      
      const minLoad = Math.floor(Math.min(...allX) / 10) * 10;
      const maxLoad = Math.ceil(Math.max(...allX) / 10) * 10;
      // Ensure minimum TSE is never below 0
      const minTse = Math.max(Math.floor(Math.min(...allY) * 10) / 10, 0.4);
      // Limit the maximum TSE to 2
      const maxTse = Math.min(Math.ceil(Math.max(...allY) * 10) / 10, 2);
      
      // const colors = [
      //   '#5470c6', // Blue
      //   '#ee6666', // Red
      //   '#3ba272', // Green
      //   '#fac858', // Yellow/Orange
      //   '#9a60b4', // Purple
      //   '#fc8452'  // Orange
      // ];
      
      return {
        backgroundColor: '#ffffff',
        tooltip: {
          trigger: 'item',
          formatter: function(params: any) {
            return `
              <div style="padding: 3px;">
                <div>${params.seriesName}</div>
                <div>${xAxisLabel}: ${params.data[0].toFixed(1)}</div>
                <div>${yAxisLabel}: ${params.data[1].toFixed(3)}</div>
              </div>
            `;
          }
        },
        grid: {
          left: '10%',
          right: '5%',
          bottom: '15%',
          top: '30px',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: xAxisLabel,
          nameLocation: 'middle',
          nameGap: 30,
          min: Number.isFinite(minLoad) ? minLoad : 0,
          max: Number.isFinite(maxLoad) ? maxLoad : 100,
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#ddd'
            }
          },
          nameTextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        yAxis: {
          type: 'value',
          name: yAxisLabel,
          nameLocation: 'middle',
          nameGap: 35,
          min: Number.isFinite(minTse) ? minTse : 0,
          max: Number.isFinite(maxTse) ? maxTse : 2,
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#ddd'
            }
          },
          nameTextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        legend: {
          data: Object.keys(processScatterData),
          right: 10,
          top: 0,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 10
          }
        },
        series: Object.entries(processScatterData).map(([date, points], index) => ({
          name: date,
          type: 'scatter',
          symbolSize: 5,
          itemStyle: {
            // color: colors[index % colors.length]
          },
          data: points,
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }))
      };
    } catch (e) {
      console.warn('Error generating chart options:', e);
      return {
        backgroundColor: '#ffffff',
        grid: {
          left: '10%',
          right: '5%',
          bottom: '15%',
          top: '30px',
          containLabel: true
        },
        xAxis: { 
          type: 'value',
          name: xAxisLabel
        },
        yAxis: { 
          type: 'value',
          name: yAxisLabel
        },
        series: [{
          type: 'scatter',
          data: [],
          name: 'Error'
        }]
      };
    }
  };
  
  return (
    <CardContent>
      <ReactECharts
        ref={chartRef}
        option={getOption()}
        style={{ height: '300px', width: '100%' }}
        notMerge={true}
        lazyUpdate={true}
        opts={{ renderer: 'canvas' }}
        onEvents={{}}
      />
    </CardContent>
  );
};

export default ScatterPlot; 