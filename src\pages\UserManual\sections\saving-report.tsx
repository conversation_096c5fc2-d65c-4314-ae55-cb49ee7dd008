import { Vegan } from 'lucide-react';
import { ManualChapter } from './types';

export const savingReportContent: ManualChapter = {
  id: 'saving-report',
  title: {
    en: 'Saving Report',
    th: 'Saving Report'
  },
  icon: <Vegan size={18} />,
  sections: [
    {
      id: 'saving-report-overview',
      title: {
        en: 'Saving Report',
        th: 'รายงานการประหยัด'
      },
      content: {
        en: `
          <div class="space-y-4">
            <p>
              The Saving Report module provides detailed analysis of energy savings achieved through system optimization.
              It compares current performance against established baselines to quantify improvements.
            </p>
            
            <h3 class="text-lg font-medium mt-4">Report Components</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li>Energy consumption reduction</li>
              <li>Cost savings calculations</li>
              <li>Environmental impact metrics</li>
              <li>Historical comparison charts</li>
              <li>Optimization effectiveness analysis</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">Report Generation</h3>
            <p>
              The Saving Report module allows you to generate customized reports:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>Time Period Selection</strong>: Choose the reporting period</li>
              <li><strong>Baseline Selection</strong>: Select appropriate baseline data for comparison</li>
              <li><strong>Metric Selection</strong>: Include relevant metrics and KPIs</li>
              <li><strong>Format Options</strong>: Generate reports in various formats (PDF, Excel, CSV)</li>
              <li><strong>Scheduled Reports</strong>: Set up automatic report generation and distribution</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">Savings Calculation Methods</h3>
            <p>
              The system uses multiple methods to calculate energy savings:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>Before/After Comparison</strong>: Direct comparison with pre-optimization data</li>
              <li><strong>Weather-Normalized Comparison</strong>: Accounts for weather differences between periods</li>
              <li><strong>Regression-Based Analysis</strong>: Uses statistical models to predict baseline consumption</li>
              <li><strong>Degree Day Analysis</strong>: Normalizes for heating and cooling degree days</li>
            </ul>
          </div>
        `,
        th: `
          <div class="space-y-4">
            <p>
              โมดูลรายงานการประหยัดให้การวิเคราะห์โดยละเอียดของการประหยัดพลังงานที่เกิดขึ้นผ่านการเพิ่มประสิทธิภาพของระบบ
              เปรียบเทียบประสิทธิภาพปัจจุบันกับเส้นฐานที่กำหนดไว้เพื่อวัดการปรับปรุง
            </p>
            
            <h3 class="text-lg font-medium mt-4">องค์ประกอบของรายงาน</h3>
            <ul class="list-disc pl-6 space-y-2">
              <li>การลดการใช้พลังงาน</li>
              <li>การคำนวณการประหยัดต้นทุน</li>
              <li>เมตริกผลกระทบต่อสิ่งแวดล้อม</li>
              <li>กราฟเปรียบเทียบประวัติ</li>
              <li>การวิเคราะห์ประสิทธิผลของการเพิ่มประสิทธิภาพ</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">การสร้างรายงาน</h3>
            <p>
              โมดูลรายงานการประหยัดช่วยให้คุณสร้างรายงานที่กำหนดเอง:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>การเลือกช่วงเวลา</strong>: เลือกช่วงเวลาการรายงาน</li>
              <li><strong>การเลือกเส้นฐาน</strong>: เลือกข้อมูลเส้นฐานที่เหมาะสมสำหรับการเปรียบเทียบ</li>
              <li><strong>การเลือกเมตริก</strong>: รวมเมตริกและ KPI ที่เกี่ยวข้อง</li>
              <li><strong>ตัวเลือกรูปแบบ</strong>: สร้างรายงานในรูปแบบต่างๆ (PDF, Excel, CSV)</li>
              <li><strong>รายงานตามกำหนดเวลา</strong>: ตั้งค่าการสร้างและแจกจ่ายรายงานอัตโนมัติ</li>
            </ul>
            
            <h3 class="text-lg font-medium mt-6">วิธีการคำนวณการประหยัด</h3>
            <p>
              ระบบใช้หลายวิธีในการคำนวณการประหยัดพลังงาน:
            </p>
            <ul class="list-disc pl-6 space-y-2">
              <li><strong>การเปรียบเทียบก่อน/หลัง</strong>: เปรียบเทียบโดยตรงกับข้อมูลก่อนการเพิ่มประสิทธิภาพ</li>
              <li><strong>การเปรียบเทียบที่ปรับตามสภาพอากาศ</strong>: คำนึงถึงความแตกต่างของสภาพอากาศระหว่างช่วงเวลา</li>
              <li><strong>การวิเคราะห์ตามการถดถอย</strong>: ใช้แบบจำลองทางสถิติเพื่อทำนายการบริโภคขั้นพื้นฐาน</li>
              <li><strong>การวิเคราะห์วันองศา</strong>: ปรับให้เป็นมาตรฐานสำหรับวันองศาความร้อนและความเย็น</li>
            </ul>
          </div>
        `
      }
    }
  ]
}; 