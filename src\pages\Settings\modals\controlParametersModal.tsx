// Control Parameters Modal Component
// Manages the configuration of chiller control parameters including delay settings
// and lead-lag control functionality

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

export interface ControlParametersModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  offDelay: number;
  onDelay: number;
  leadLagEnabled: boolean;
  // Define specific handlers based on AutomationTabContent
  onOnDelayChange: (value: number) => void;
  onOffDelayChange: (value: number) => void;
  onLeadLagChange: (value: boolean) => void;
}

export function ControlParametersModal({
  open,
  onOpenChange,
  offDelay,
  onDelay,
  leadLagEnabled,
  onOnDelayChange,
  onOffDelayChange,
  onLeadLagChange,
}: ControlParametersModalProps) {

  // Local state to manage edits within the modal if needed,
  // or directly call the passed handlers on change.
  // Example using direct handlers:

  const handleSave = () => {
    // Potentially save settings via API call here
    console.log('Saving control parameters...');
    // Callbacks are already updating the parent state, so just close.
    onOpenChange(false);
  };

  // Helper to handle number input changes
  const handleNumberChange = (setter: (value: number) => void, value: string) => {
    const num = parseInt(value, 10);
    if (!isNaN(num) && num >= 0) {
      setter(num);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Control Parameters</DialogTitle>
        </DialogHeader>
        <div className="py-4 space-y-4">
          {/* On Delay */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="onDelay" className="text-right">On Delay (seconds)</Label>
            <Input 
              id="onDelay" 
              type="number" 
              value={onDelay.toString()} 
              onChange={(e) => handleNumberChange(onOnDelayChange, e.target.value)} 
              className="col-span-3" 
              min="0"
            />
          </div>
          {/* Off Delay */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="offDelay" className="text-right">Off Delay (seconds)</Label>
            <Input 
              id="offDelay" 
              type="number" 
              value={offDelay.toString()} 
              onChange={(e) => handleNumberChange(onOffDelayChange, e.target.value)} 
              className="col-span-3" 
              min="0"
            />
          </div>
          {/* Lead/Lag Switch */}
          <div className="flex items-center justify-between space-x-2 py-2">
            <Label htmlFor="leadLagSwitch" className="flex flex-col space-y-1">
              <span>Lead/Lag Rotation</span>
              <span className="font-normal leading-snug text-muted-foreground">
                Enable automatic rotation of lead chiller.
              </span>
            </Label>
            <Switch
              id="leadLagSwitch"
              checked={leadLagEnabled}
              onCheckedChange={onLeadLagChange}
            />
          </div>
          {/* Add other controls here as needed (e.g., rotation interval if lead/lag enabled) */}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSave}>Save Parameters</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}