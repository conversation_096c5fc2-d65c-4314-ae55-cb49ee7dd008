import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { set, cloneDeep } from 'lodash';
import { TableSettings } from '../types';


interface TableConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  settings: TableSettings;
  onSave: (settings: TableSettings) => void;
}

// Component for configuring table settings
export const TableConfigDialog: React.FC<TableConfigDialogProps> = ({
  open,
  onOpenChange,
  settings,
  onSave,
}) => {
  const [localSettings, setLocalSettings] = useState<TableSettings>(settings);

  // Update localSettings when settings prop changes
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  const handleSettingsChange = (path: string, value: any) => {
    const pathParts = path.split('.');
    const newSettings = cloneDeep(localSettings);
    
    let current: any = newSettings;
    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i];
      
      // Initialize missing objects in the path
      if (!current[part]) {
        current[part] = {};
      }
      
      current = current[part];
    }
    
    const lastPart = pathParts[pathParts.length - 1];
    current[lastPart] = value;
    
    setLocalSettings(newSettings);
  };

  const handleSave = () => {
    onSave(localSettings);
    onOpenChange(false);
  };

  const handleAddRiserRow = () => {
    const rows = localSettings.riserTable?.rows || [];
    const newId = rows.length > 0 ? String.fromCharCode(rows[rows.length - 1].id.charCodeAt(0) + 1) : 'A';
    const newDisplayName = `Riser ${newId}`;
    
    handleSettingsChange('riserTable.rows', [
      ...rows,
      {
        id: newId,
        displayName: newDisplayName,
        deviceId: `riser_${newId}`,
        controlDeviceId: `riser_${newId}`,
      }
    ]);
  };

  const handleRemoveRiserRow = (index: number) => {
    const rows = [...(localSettings.riserTable?.rows || [])];
    rows.splice(index, 1);
    handleSettingsChange('riserTable.rows', rows);
  };

  const handleUpdateRiserRow = (index: number, field: string, value: string) => {
    const rows = [...(localSettings.riserTable?.rows || [])];
    
    // If display name is updated, update the ID to match (extract last character)
    if (field === 'displayName') {
      const match = value.match(/([A-Za-z])$/);
      if (match && match[1]) {
        const newId = match[1].toUpperCase();
        rows[index] = { 
          ...rows[index], 
          [field]: value,
          id: newId
        };
      } else {
        rows[index] = { ...rows[index], [field]: value };
      }
    } else {
      rows[index] = { ...rows[index], [field]: value };
    }
    
    handleSettingsChange('riserTable.rows', rows);
  };

  const handleAddIAQRow = () => {
    const rows = localSettings.indoorAirQualityTable?.rows || [];
    const newId = rows.length > 0 ? rows[rows.length - 1].id + 1 : 1;
    const newDisplayName = `Floor ${newId}`;
    
    handleSettingsChange('indoorAirQualityTable.rows', [
      ...rows,
      {
        id: newId,
        displayName: newDisplayName,
        deviceId: `floor_${newId}`,
      }
    ]);
  };

  const handleRemoveIAQRow = (index: number) => {
    const rows = [...(localSettings.indoorAirQualityTable?.rows || [])];
    rows.splice(index, 1);
    handleSettingsChange('indoorAirQualityTable.rows', rows);
  };

  const handleUpdateIAQRow = (index: number, field: string, value: string | number) => {
    const rows = [...(localSettings.indoorAirQualityTable?.rows || [])];
    
    // If display name is updated, try to extract floor number
    if (field === 'displayName') {
      const match = value.toString().match(/(\d+)/);
      if (match && match[1]) {
        const newId = parseInt(match[1], 10);
        rows[index] = { 
          ...rows[index], 
          [field]: value.toString(), // Ensure string type for displayName
          id: newId
        };
      } else {
        rows[index] = { ...rows[index], [field]: value.toString() }; // Ensure string type
      }
    } else {
      // Handle different field types appropriately
      if (field === 'id') {
        rows[index] = { ...rows[index], [field]: typeof value === 'string' ? parseInt(value, 10) : value };
      } else {
        rows[index] = { ...rows[index], [field]: value.toString() }; // Ensure string type for other fields
      }
    }
    
    handleSettingsChange('indoorAirQualityTable.rows', rows);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Table Configuration</DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="riser">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="riser">Riser Table</TabsTrigger>
            <TabsTrigger value="iaq">Indoor Air Quality Table</TabsTrigger>
          </TabsList>
          
          <TabsContent value="riser" className="space-y-6 mt-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="riser-enabled"
                checked={localSettings.riserTable?.enabled ?? false}
                onCheckedChange={(checked) => handleSettingsChange('riserTable.enabled', checked)}
              />
              <Label htmlFor="riser-enabled">Show Riser Table</Label>
            </div>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Riser Rows</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleAddRiserRow}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" /> Add Row
                </Button>
              </div>
              
              <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2">
                {localSettings.riserTable?.rows?.map((row, index) => (
                  <div key={index} className="grid grid-cols-12 gap-3 items-center p-3 border border-border rounded-md bg-card/50">
                    <div className="col-span-4">
                      <Label htmlFor={`riser-name-${index}`} className="text-xs text-muted-foreground">Display Name</Label>
                      <Input 
                        id={`riser-name-${index}`}
                        value={row.displayName}
                        onChange={(e) => handleUpdateRiserRow(index, 'displayName', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="col-span-4">
                      <Label htmlFor={`riser-device-${index}`} className="text-xs text-muted-foreground">Device ID</Label>
                      <Input 
                        id={`riser-device-${index}`}
                        value={row.deviceId}
                        onChange={(e) => handleUpdateRiserRow(index, 'deviceId', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="col-span-3">
                      <Label htmlFor={`riser-control-${index}`} className="text-xs text-muted-foreground">Control Device ID</Label>
                      <Input 
                        id={`riser-control-${index}`}
                        value={row.controlDeviceId}
                        onChange={(e) => handleUpdateRiserRow(index, 'controlDeviceId', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="col-span-1 flex justify-end items-end">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleRemoveRiserRow(index)}
                        className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                
                {(!localSettings.riserTable?.rows || localSettings.riserTable.rows.length === 0) && (
                  <div className="text-center py-4 text-muted-foreground">
                    No rows configured. Click "Add Row" to add a riser.
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Datapoints</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="riser-flow" className="text-muted-foreground">Flow Datapoint</Label>
                  <Input 
                    id="riser-flow"
                    value={localSettings.riserTable?.datapoints?.flow || ''}
                    onChange={(e) => handleSettingsChange('riserTable.datapoints.flow', e.target.value)}
                    placeholder="flow"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="riser-pressure" className="text-muted-foreground">Pressure Datapoint</Label>
                  <Input 
                    id="riser-pressure"
                    value={localSettings.riserTable?.datapoints?.pressure || ''}
                    onChange={(e) => handleSettingsChange('riserTable.datapoints.pressure', e.target.value)}
                    placeholder="pressure"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="riser-temperature" className="text-muted-foreground">Temperature Datapoint</Label>
                  <Input 
                    id="riser-temperature"
                    value={localSettings.riserTable?.datapoints?.temperature || ''}
                    onChange={(e) => handleSettingsChange('riserTable.datapoints.temperature', e.target.value)}
                    placeholder="temperature"
                  />
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="iaq" className="space-y-6 mt-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="iaq-enabled"
                checked={localSettings.indoorAirQualityTable?.enabled ?? false}
                onCheckedChange={(checked) => handleSettingsChange('indoorAirQualityTable.enabled', checked)}
              />
              <Label htmlFor="iaq-enabled">Show Indoor Air Quality Table</Label>
            </div>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Floor Rows</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleAddIAQRow}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" /> Add Row
                </Button>
              </div>
              
              <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2">
                {localSettings.indoorAirQualityTable?.rows?.map((row, index) => (
                  <div key={index} className="grid grid-cols-12 gap-3 items-center p-3 border border-border rounded-md bg-card/50">
                    <div className="col-span-6">
                      <Label htmlFor={`iaq-name-${index}`} className="text-xs text-muted-foreground">Display Name</Label>
                      <Input 
                        id={`iaq-name-${index}`}
                        value={row.displayName}
                        onChange={(e) => handleUpdateIAQRow(index, 'displayName', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="col-span-5">
                      <Label htmlFor={`iaq-device-${index}`} className="text-xs text-muted-foreground">Device ID</Label>
                      <Input 
                        id={`iaq-device-${index}`}
                        value={row.deviceId}
                        onChange={(e) => handleUpdateIAQRow(index, 'deviceId', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="col-span-1 flex justify-end items-end">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleRemoveIAQRow(index)}
                        className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                
                {(!localSettings.indoorAirQualityTable?.rows || localSettings.indoorAirQualityTable.rows.length === 0) && (
                  <div className="text-center py-4 text-muted-foreground">
                    No rows configured. Click "Add Row" to add a floor.
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Datapoints</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="iaq-temperature" className="text-muted-foreground">Temperature Datapoint</Label>
                  <Input 
                    id="iaq-temperature"
                    value={localSettings.indoorAirQualityTable?.datapoints?.temperature || ''}
                    onChange={(e) => handleSettingsChange('indoorAirQualityTable.datapoints.temperature', e.target.value)}
                    placeholder="temperature"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="iaq-humidity" className="text-muted-foreground">Humidity Datapoint</Label>
                  <Input 
                    id="iaq-humidity"
                    value={localSettings.indoorAirQualityTable?.datapoints?.humidity || ''}
                    onChange={(e) => handleSettingsChange('indoorAirQualityTable.datapoints.humidity', e.target.value)}
                    placeholder="humidity"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="iaq-co2" className="text-muted-foreground">CO2 Datapoint</Label>
                  <Input 
                    id="iaq-co2"
                    value={localSettings.indoorAirQualityTable?.datapoints?.co2 || ''}
                    onChange={(e) => handleSettingsChange('indoorAirQualityTable.datapoints.co2', e.target.value)}
                    placeholder="co2"
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-end space-x-2 mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
