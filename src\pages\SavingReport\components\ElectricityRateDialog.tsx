import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format, parse, isValid } from 'date-fns';
import { toast } from "sonner";
import { ElectricityRateMap, getElectricityRateKey } from '../contexts/SavingDashboardContext';

export interface ElectricityRateValues {
  totalConsumption: number;
  energyCharge: number;
  ftCharge: number;
  year: number;
  month: number; // 1-12 for specific month, 0 for all months in a year
  altoTechSharePercentage: number; // Percentage of savings for AltoTech (0-100)
  customerSharePercentage: number; // Percentage of savings for the customer (0-100)
  customerName: string; // Name of the customer
}

interface MonthData {
  year: number;
  month: number;
  monthName: string;
  values: ElectricityRateValues;
  unitRate: number;
}

interface ElectricityRateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  electricityRateMap: ElectricityRateMap;
  onSave: (values: ElectricityRateValues) => void;
  reportingRange: { start: string; end: string };
}

const ElectricityRateDialog: React.FC<ElectricityRateDialogProps> = ({
  open,
  onOpenChange,
  electricityRateMap,
  onSave,
  reportingRange
}) => {
  // State to store the table data
  const [monthsData, setMonthsData] = useState<MonthData[]>([]);
  const [editingCell, setEditingCell] = useState<{rowIndex: number, field: string} | null>(null);

  // Generate months data from reporting range when dialog opens
  useEffect(() => {
    if (open) {
      const data: MonthData[] = [];

      // Parse reporting range dates
      const startDate = parse(reportingRange.start, 'yyyy-MM-dd', new Date());
      const endDate = parse(reportingRange.end, 'yyyy-MM-dd', new Date());

      if (isValid(startDate) && isValid(endDate)) {
        const startYear = startDate.getFullYear();
        const startMonth = startDate.getMonth() + 1; // JavaScript months are 0-indexed
        const endYear = endDate.getFullYear();
        const endMonth = endDate.getMonth() + 1;

        // Generate data for each month in the reporting range
        for (let year = startYear; year <= endYear; year++) {
          const monthStart = year === startYear ? startMonth : 1;
          const monthEnd = year === endYear ? endMonth : 12;

          for (let month = monthStart; month <= monthEnd; month++) {
            // Get existing values from the map or create default values
            const key = getElectricityRateKey(year, month);
            const defaultValues: ElectricityRateValues = {
              totalConsumption: 2501976,
              energyCharge: 8767734.83,
              ftCharge: 918725.59,
              year,
              month,
              altoTechSharePercentage: 70, // Default 70% for AltoTech
              customerSharePercentage: 30, // Default 30% for customer
              customerName: 'Customer' // Default customer name
            };

            const values = electricityRateMap.get(key) || defaultValues;
            const unitRate = values.totalConsumption > 0 ?
              (values.energyCharge + values.ftCharge) / values.totalConsumption : 0;

            data.push({
              year,
              month,
              monthName: format(new Date(year, month - 1, 1), 'MMMM yyyy'),
              values,
              unitRate
            });
          }
        }
      }

      setMonthsData(data);
    }
  }, [open, electricityRateMap, reportingRange]);

  // Handle input change for a specific field in a specific row
  const handleInputChange = (rowIndex: number, field: keyof ElectricityRateValues, value: string) => {
    setMonthsData(prevData => {
      const newData = [...prevData];
      const row = {...newData[rowIndex]};
      const values = {...row.values};

      // Handle string fields differently
      if (field === 'customerName') {
        values[field] = value;
      } else {
        // For numeric fields
        const numValue = parseFloat(value);
        if (!isNaN(numValue)) {
          values[field] = numValue as any;
        } else {
          return prevData; // Return unchanged if invalid number
        }
      }

      // Recalculate unit rate
      const unitRate = values.totalConsumption > 0 ?
        (values.energyCharge + values.ftCharge) / values.totalConsumption : 0;

      newData[rowIndex] = {
        ...row,
        values,
        unitRate
      };

      return newData;
    });
  };

  // Save all changes
  const handleSave = () => {
    // Validate all inputs
    for (const data of monthsData) {
      if (data.values.totalConsumption <= 0) {
        toast.error(`Total consumption for ${data.monthName} must be greater than zero`);
        return;
      }

      if (data.values.energyCharge < 0 || data.values.ftCharge < 0) {
        toast.error(`Charges for ${data.monthName} cannot be negative`);
        return;
      }

      // Validate sharing percentages
      const altoTechShare = data.values.altoTechSharePercentage || 0;
      const customerShare = data.values.customerSharePercentage || 0;

      if (altoTechShare < 0 || altoTechShare > 100) {
        toast.error(`AltoTech share percentage for ${data.monthName} must be between 0 and 100`);
        return;
      }

      if (customerShare < 0 || customerShare > 100) {
        toast.error(`Customer share percentage for ${data.monthName} must be between 0 and 100`);
        return;
      }

      if (altoTechShare + customerShare !== 100) {
        toast.error(`Total share percentages for ${data.monthName} must equal 100%`);
        return;
      }
    }

    // Save each month's data
    for (const data of monthsData) {
      onSave(data.values);
    }

    onOpenChange(false);
    toast.success("Electricity rate values updated");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Electricity Rate Configuration</DialogTitle>
          <DialogDescription>
            Configure electricity rates for each month in the reporting period.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 max-h-[500px] overflow-y-auto">
          {/* Customer Name Input */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">Customer Name:</label>
            <Input
              type="text"
              value={monthsData.length > 0 ? monthsData[0].values.customerName || 'Customer' : 'Customer'}
              onChange={(e) => {
                // Update customer name for all months
                setMonthsData(prevData => {
                  return prevData.map(data => ({
                    ...data,
                    values: {
                      ...data.values,
                      customerName: e.target.value
                    }
                  }));
                });
              }}
              className="w-full"
            />
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[150px]">Month</TableHead>
                <TableHead>Total Consumption (kWh)</TableHead>
                <TableHead>Energy Charge (THB)</TableHead>
                <TableHead>Ft Charge (THB)</TableHead>
                <TableHead>Unit Rate (THB/kWh)</TableHead>
                <TableHead>AltoTech Share (%)</TableHead>
                <TableHead>Customer Share (%)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {monthsData.map((data, index) => (
                <TableRow key={`${data.year}-${data.month}`}>
                  <TableCell className="font-medium">{data.monthName}</TableCell>

                  <TableCell>
                    {editingCell?.rowIndex === index && editingCell?.field === 'totalConsumption' ? (
                      <Input
                        type="number"
                        value={data.values.totalConsumption}
                        onChange={(e) => handleInputChange(index, 'totalConsumption', e.target.value)}
                        onBlur={() => setEditingCell(null)}
                        autoFocus
                        min="1"
                        step="1"
                      />
                    ) : (
                      <div
                        className="p-2 hover:bg-gray-100 rounded cursor-pointer"
                        onClick={() => setEditingCell({ rowIndex: index, field: 'totalConsumption' })}
                      >
                        {data.values.totalConsumption.toLocaleString()}
                      </div>
                    )}
                  </TableCell>

                  <TableCell>
                    {editingCell?.rowIndex === index && editingCell?.field === 'energyCharge' ? (
                      <Input
                        type="number"
                        value={data.values.energyCharge}
                        onChange={(e) => handleInputChange(index, 'energyCharge', e.target.value)}
                        onBlur={() => setEditingCell(null)}
                        autoFocus
                        min="0"
                        step="0.01"
                      />
                    ) : (
                      <div
                        className="p-2 hover:bg-gray-100 rounded cursor-pointer"
                        onClick={() => setEditingCell({ rowIndex: index, field: 'energyCharge' })}
                      >
                        {data.values.energyCharge.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                      </div>
                    )}
                  </TableCell>

                  <TableCell>
                    {editingCell?.rowIndex === index && editingCell?.field === 'ftCharge' ? (
                      <Input
                        type="number"
                        value={data.values.ftCharge}
                        onChange={(e) => handleInputChange(index, 'ftCharge', e.target.value)}
                        onBlur={() => setEditingCell(null)}
                        autoFocus
                        min="0"
                        step="0.01"
                      />
                    ) : (
                      <div
                        className="p-2 hover:bg-gray-100 rounded cursor-pointer"
                        onClick={() => setEditingCell({ rowIndex: index, field: 'ftCharge' })}
                      >
                        {data.values.ftCharge.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                      </div>
                    )}
                  </TableCell>

                  <TableCell>
                    <div className="font-bold text-blue-700 bg-blue-50 p-2 rounded">
                      {data.unitRate.toFixed(4)}
                    </div>
                  </TableCell>

                  <TableCell>
                    {editingCell?.rowIndex === index && editingCell?.field === 'altoTechSharePercentage' ? (
                      <Input
                        type="number"
                        value={data.values.altoTechSharePercentage || 70}
                        onChange={(e) => handleInputChange(index, 'altoTechSharePercentage', e.target.value)}
                        onBlur={() => setEditingCell(null)}
                        autoFocus
                        min="0"
                        max="100"
                        step="1"
                      />
                    ) : (
                      <div
                        className="p-2 hover:bg-gray-100 rounded cursor-pointer"
                        onClick={() => setEditingCell({ rowIndex: index, field: 'altoTechSharePercentage' })}
                      >
                        {(data.values.altoTechSharePercentage || 70).toFixed(0)}%
                      </div>
                    )}
                  </TableCell>

                  <TableCell>
                    {editingCell?.rowIndex === index && editingCell?.field === 'customerSharePercentage' ? (
                      <Input
                        type="number"
                        value={data.values.customerSharePercentage || 30}
                        onChange={(e) => handleInputChange(index, 'customerSharePercentage', e.target.value)}
                        onBlur={() => setEditingCell(null)}
                        autoFocus
                        min="0"
                        max="100"
                        step="1"
                      />
                    ) : (
                      <div
                        className="p-2 hover:bg-gray-100 rounded cursor-pointer"
                        onClick={() => setEditingCell({ rowIndex: index, field: 'customerSharePercentage' })}
                      >
                        {(data.values.customerSharePercentage || 30).toFixed(0)}%
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {monthsData.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              No months in the selected reporting period.
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ElectricityRateDialog;
