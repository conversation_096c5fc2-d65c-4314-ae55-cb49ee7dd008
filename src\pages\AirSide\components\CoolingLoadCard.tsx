import { useRealtime } from "@/contexts/RealtimeContext";

export function CoolingLoadCard() {
  // Mock data waiting for real-time API
  const { getValue } = useRealtime();
  const coolingLoad = getValue("plant", "cooling_rate");
  const unit = "RT";
  const partLoadPercentage = getValue("plant", "part_load_percentage") ?? 0;

  return (
    <div className="w-full h-full p-2 bg-white rounded-xl border border-[#EDEFF9] shadow-card flex justify-between gap-4">
      {/* Left Section */}
      <div className="flex-1 h-full flex flex-col justify-between items-start pr-2 overflow-hidden">
        <div className="w-full flex justify-start items-center overflow-x-auto">
          <div className="flex-shrink-0">
            <div className="text-[#065BA9] text-sm font-semibold tracking-[0.01em] whitespace-nowrap">
              Cooling Load
            </div>
          </div>
        </div>

        <div className="w-full flex justify-start items-start overflow-x-auto">
          <div className="flex-shrink-0">
            <div className="text-[#0E7EE4] text-[28px] sm:text-[32px] font-semibold whitespace-nowrap">
              {coolingLoad !== null
                ? coolingLoad?.toFixed(0).toLocaleString()
                : "-"}
            </div>
            <div className="text-[#788796] text-[13px] font-normal whitespace-nowrap">
              {unit}
            </div>
          </div>
        </div>
      </div>

      {/* Right Section */}
      <div className="flex-1 h-full py-2 px-2 bg-[#F9FAFF] rounded-md border border-[#EDEFF9] flex flex-col justify-between items-start overflow-hidden">
        <div className="w-full flex justify-start items-center overflow-x-auto">
          <div className="flex-shrink-0 text-[#788796] text-[10px] font-semibold whitespace-nowrap">
            Part-Load
          </div>
        </div>

        <div className="w-full flex justify-start items-center gap-1 overflow-x-auto">
          <div className="text-[#0E7EE4] text-[18px] sm:text-[22px] font-semibold whitespace-nowrap">
            {partLoadPercentage !== null ? partLoadPercentage?.toFixed(1) : "-"}
          </div>
          <div className="text-[#788796] text-[13px] font-normal flex-shrink-0">
            %
          </div>
        </div>

        <div className="w-full h-1.5">
          <div className="w-full h-full bg-[#EDEFF9] rounded-sm overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] rounded-sm"
              style={{ width: `${partLoadPercentage}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default CoolingLoadCard;
