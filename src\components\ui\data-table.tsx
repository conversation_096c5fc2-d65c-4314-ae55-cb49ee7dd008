import React from 'react';
import { cn } from '@/lib/utils';

export interface TableColumn {
  key: string;
  header: React.ReactNode;
  width?: number;
  parent?: string; // Parent header key for grouped columns
  colSpan?: number; // Number of columns this header spans
  textAlign?: 'left' | 'center' | 'right'; // Text alignment for the column
  separator?: boolean; // Whether to show a vertical separator after this column
  formatCell?: (value: any, row: Record<string, any>) => { 
    textColor?: string;
    bgColor?: string;
    content?: React.ReactNode;
  }; // Function to format cell content and styling
}

export interface TableProps {
  columns: TableColumn[];
  data: Record<string, any>[];
  className?: string;
  headerGroups?: Record<string, any>[]; // Optional header groups for multi-layered headers
}

export const DataTable: React.FC<TableProps> = ({
  columns,
  data,
  className,
  headerGroups,
}) => {
  return (
    <div className={cn(
      "rounded-[6px] overflow-hidden border border-[#DBE4FF]",
      className
    )}>
      {/* Header Groups (if provided) */}
      {headerGroups && headerGroups.length > 0 && (
        <div>
          {headerGroups.map((headerRow, groupIndex) => (
            <div key={`header-group-${groupIndex}`} className="flex">
              {Object.entries(headerRow).map(([key, value], cellIndex) => {
                const colSpan = value.colSpan || 1;
                // Calculate width based on colSpan
                const width = colSpan === 1 ? 
                  (cellIndex === 0 ? "w-[120px]" : "flex-1") : 
                  `w-[${120 + (colSpan-1) * 100}px]`;
                
                return (
                  <div
                    key={`header-group-${groupIndex}-${key}`}
                    className={cn(
                      "h-[22px] px-2 bg-[#EDEFF9] border-b-2 border-[#DBE4FF] flex items-center justify-center",
                      value.className || "",
                      colSpan > 1 ? `col-span-${colSpan}` : "",
                      value.width || width,
                      value.separator ? "border-r-2 border-r-[#DBE4FF]" : ""
                    )}
                    style={{ 
                      flex: colSpan > 1 ? colSpan : undefined,
                      backgroundColor: value.bgColor || "#EDEFF9" 
                    }}
                  >
                    <div className="text-[#788796] text-[9px] font-normal">
                      {value.label}
                    </div>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      )}

      {/* Standard Header Row */}
      <div className="flex">
        {columns.map((column, index) => (
          <div
            key={column.key}
            className={cn(
              "h-[22px] px-2 bg-[#EDEFF9] border-b-2 border-[#DBE4FF] flex items-center",
              column.textAlign === 'center' ? "justify-center" : "gap-2",
              index === 0 ? "w-[120px]" : "flex-1",
              column.separator ? "border-r-2 border-r-[#DBE4FF]" : ""
            )}
          >
            <div className="text-[#788796] text-[9px] font-normal">
              {column.header}
            </div>
          </div>
        ))}
      </div>

      {/* Data Rows */}
      {data.map((row, rowIndex) => (
        <div key={rowIndex} className="flex">
          {columns.map((column, colIndex) => {
            // Format cell if formatCell function is provided
            const cellValue = row[column.key];
            const formatting = column.formatCell 
              ? column.formatCell(cellValue, row)
              : {};

            return (
              <div
                key={`${rowIndex}-${column.key}`}
                className={cn(
                  "h-[22px] px-2 flex items-center border-b border-[#EDEFF9]",
                  column.textAlign === 'center' ? "justify-center" : "gap-2",
                  column.textAlign === 'right' ? "justify-end" : "",
                  rowIndex % 2 === 0 ? "bg-[#F9FAFF]" : "bg-white",
                  colIndex === 0 ? "w-[120px]" : "flex-1",
                  column.separator ? "border-r-2 border-r-[#DBE4FF]" : ""
                )}
                style={{
                  backgroundColor: formatting.bgColor,
                }}
              >
                {formatting.content !== undefined ? (
                  formatting.content
                ) : (
                  typeof cellValue === 'string' || typeof cellValue === 'number' ? (
                    <div 
                      className={cn(
                        "text-[#212529] text-[11px] font-normal",
                        column.textAlign === 'center' ? "text-center w-full" : "",
                        column.textAlign === 'right' ? "text-right w-full" : ""
                      )}
                      style={{ 
                        color: formatting.textColor,
                      }}
                    >
                      {cellValue}
                    </div>
                  ) : (
                    cellValue
                  )
                )}
              </div>
            );
          })}
        </div>
      ))}
    </div>
  );
}; 