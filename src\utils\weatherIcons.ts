import { WeatherIconMapping } from '../types/weather';

// Import all weather icons
import clear from '../assets/weather_icons/day_clear.png';
import partlyCloudy from '../assets/weather_icons/day_partly_cloudy.png';
import cloudy from '../assets/weather_icons/day_cloudy.png';
import overcast from '../assets/weather_icons/day_cloudy.png';
import lightRain from '../assets/weather_icons/day_light_rain.png';
import moderateRain from '../assets/weather_icons/day_moderate_rain.png';
import heavyRain from '../assets/weather_icons/day_heavy_rain.png';
import thunderStorm from '../assets/weather_icons/thunder_storm.png';

// Map weather condition codes to our local weather icons
export const weatherIconMap: WeatherIconMapping = {
  // 1: Clear
  1: clear,
  // 2: Partly Cloudy
  2: partlyCloudy,
  // 3: Cloudy
  3: cloudy,
  // 4: Overcast
  4: overcast,
  // 5: Light Rain
  5: lightRain,
  // 6: Moderate Rain
  6: moderateRain,
  // 7: Heavy Rain
  7: heavyRain,
  // 8: Thunderstorm
  8: thunderStorm
};

// Default fallback for any condition code not in the map
const defaultIcon = cloudy;

/**
 * Get the appropriate weather icon based on condition code
 */
export const getWeatherIcon = (conditionCode: number): string => {
  return weatherIconMap[conditionCode] || defaultIcon;
};
