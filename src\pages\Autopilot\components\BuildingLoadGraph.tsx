import { useEffect, useRef, useState } from "react";
import { Loader } from "@/components/ui/loader";
import * as echarts from "echarts";
import { useAuth } from "@/contexts/AuthContext";
import { fetchAggregatedData } from "@/services/timescaleService";
import { getSiteId } from "@/services/authService";
import { DateTime } from 'luxon';

// Define the interface for the transformed data structure that matches what the component expects
interface TransformedData {
  bucket: string;
  avg_value: number;
  site_id: string;
  device_id: string;
  model: string;
  datapoint: string;
}

const BuildingLoadGraph: React.FC = () => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<echarts.ECharts | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { site } = useAuth();

  // Initialize chart with default configuration
  const initializeChart = () => {
    if (!chartRef.current) return;
    
    // Make sure any existing chart is disposed first
    if (chartInstanceRef.current) {
      chartInstanceRef.current.dispose();
    }
    
    // Check if the container has proper dimensions
    if (chartRef.current.clientWidth === 0 || chartRef.current.clientHeight === 0) {
      console.log("Chart container has no dimensions yet, will retry");
      setTimeout(initializeChart, 50);
      return;
    }

    const chartInstance = echarts.init(chartRef.current);
    chartInstanceRef.current = chartInstance;

    // Get timezone from site or default to Asia/Bangkok
    const timezone = site?.timezone || 'Asia/Bangkok';
    
    // Use Luxon for proper timezone handling
    const startDatetime = DateTime.now().setZone(timezone).startOf('day');
    const endDateTime = startDatetime.plus({ days: 1 });

    // Create empty data arrays for initial display
    const emptyData = Array.from({ length: 24 }, (_, i) => [
      startDatetime.plus({ hours: i }).toJSDate().getTime(),
      0
    ]);

    chartInstance.setOption({
      tooltip: {
        trigger: "axis",
        formatter: function (params: any[]) {
          const date = new Date(params[0].data[0]);
          const dateString = date.toLocaleString("en-GB", {
            day: "2-digit",
            month: "short",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
            timeZone: site?.timezone || "Asia/Bangkok",
          });

          const tooltipItems = params
            .map((param: any) => {
              const name = param.seriesName.replace(" (Predicted)", "");
              const unit = param.seriesName.includes("Cooling") ? " RT" : " kW";
              return `<span style="color: ${param.color}; font-size: 16px;">●</span> ${name}: <b>${param.value[1].toFixed(1)}${unit}</b>`;
            })
            .join("<br/>");
          return `${dateString}<br/>${tooltipItems}`;
        },
        backgroundColor: '#fff',
        borderColor: '#ddd',
        borderWidth: 1,
        textStyle: { color: '#333' }
      },
      legend: {
        data: [
          { name: "Cooling Load (RT)", icon: 'circle' },
          { name: "Power (kW)", icon: 'circle' }
        ],
        top: 0,
        right: 10,
        orient: "horizontal",
        textStyle: {
          fontSize: 12,
        },
        itemGap: 24,
      },
      xAxis: {
        type: "time",
        splitNumber: 8,
        min: startDatetime.toJSDate().toISOString(),
        max: endDateTime.toJSDate().toISOString(),
        axisLabel: {
          formatter: function (params: number) {
            const date = new Date(params);
            return String(date.getHours()).padStart(2, "0");
          },
          margin: 12,
          fontSize: 12,
          color: "#788796",
          interval: 2 * 3600 * 1000,
        },
        axisTick: { show: false },
        axisLine: {
          lineStyle: { color: '#ddd' }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#eee',
            type: 'solid'
          }
        }
      },
      yAxis: {
        type: "value",
        name: "",
        min: 0,
        max: 1000,
        interval: 200,
        axisLabel: {
          fontSize: 12,
          formatter: (value: number) => value.toFixed(0),
        },
        nameTextStyle: {
          fontSize: 12,
          padding: [0, 0, 0, 0],
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#eee',
            type: 'solid'
          }
        }
      },
      series: [
        {
          name: "Cooling Load",
          type: "line",
          showSymbol: false,
          symbolSize: 0,
          data: emptyData,
          color: "#14B8B4",
          lineStyle: {
            width: 2,
            type: "solid"
          },
          smooth: true,
          z: 2,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(20, 184, 180, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(20, 184, 180, 0.1)",
                },
              ],
            },
          },
        },
        {
          name: "Power",
          type: "line",
          showSymbol: false,
          symbolSize: 0,
          data: emptyData,
          color: "#3b82f6",
          lineStyle: {
            width: 2,
            type: "solid"
          },
          smooth: true,
          z: 2,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(59, 130, 246, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(59, 130, 246, 0.1)",
                },
              ],
            },
          },
        }
      ],
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        top: "15%",
        containLabel: true,
      },
    });
  };

  const updateChartWithData = (powerData: TransformedData[], coolingLoadData: TransformedData[]) => {
    if (!chartInstanceRef.current) return;

    const chartInstance = chartInstanceRef.current;

    const transformData = (data: TransformedData[]) => {
      return data.map(item => [
        new Date(item.bucket).getTime(),
        item.avg_value
      ]);
    };

    const powerChartData = transformData(powerData);
    const coolingLoadChartData = transformData(coolingLoadData);

    // Calculate max values for y-axis scaling
    const maxPowerValue = Math.max(...powerChartData.map(data => data[1]), 1);
    const maxLoadValue = Math.max(...coolingLoadChartData.map(data => data[1]), 1);
    
    // Find the overall maximum value
    const maxValue = Math.max(maxPowerValue, maxLoadValue);
    
    // Calculate appropriate y-axis max and interval
    const yAxisMax = Math.ceil((maxValue * 1.2) / 100) * 100;
    
    // Calculate appropriate interval
    let yAxisInterval;
    
    if (yAxisMax <= 500) {
      yAxisInterval = 100;
    } else if (yAxisMax <= 1000) {
      yAxisInterval = 200;
    } else {
      yAxisInterval = 400;
    }

    chartInstance.setOption({
      yAxis: {
        max: yAxisMax,
        interval: yAxisInterval
      },
      series: [
        {
          name: "Cooling Load (RT)",
          data: coolingLoadChartData,
          showSymbol: false,
          symbolSize: 4,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(20, 184, 180, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(20, 184, 180, 0.1)",
                },
              ],
            },
          },
        },
        {
          name: "Power (kW)",
          data: powerChartData,
          showSymbol: false,
          symbolSize: 4,
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(59, 130, 246, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(59, 130, 246, 0.1)",
                },
              ],
            },
          },
        }
      ]
    });
  };

  useEffect(() => {
    console.log('BuildingLoadGraph component mounted');
    
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize();
      }
    };

    const initializeAndFetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Initialize chart first
        initializeChart();

        // Get site ID
        const siteId = getSiteId() || '';
        
        // Get timezone from site or default to Asia/Bangkok
        const timezone = site?.timezone || 'Asia/Bangkok';
        
        // Set up time range for today using Luxon for proper timezone handling
        const startTime = DateTime.now().setZone(timezone).startOf('day');
        const endTime = startTime.plus({ days: 1 });

        // Fetch power and cooling rate data using fetchAggregatedData
        const query = {
          site_id: siteId,
          device_id: 'plant',
          datapoints: ['power', 'cooling_rate'],
          start_timestamp: startTime.toString(),
          end_timestamp: endTime.toString(),
          resampling: '30min'
        };

        const response = await fetchAggregatedData(query);
        
        // Transform the data to match the expected format
        const powerData = response.data
          .filter(item => item.datapoint === 'power')
          .flatMap(item => item.values.map(value => ({
            bucket: value.timestamp,
            avg_value: value.value,
            site_id: item.site_id,
            device_id: item.device_id,
            model: item.model,
            datapoint: item.datapoint
          })));
        
        const coolingLoadData = response.data
          .filter(item => item.datapoint === 'cooling_rate')
          .flatMap(item => item.values.map(value => ({
            bucket: value.timestamp,
            avg_value: value.value,
            site_id: item.site_id,
            device_id: item.device_id,
            model: item.model,
            datapoint: item.datapoint
          })));

        // Try updating chart with a small delay to ensure container is ready
        setTimeout(() => {
          if (!chartInstanceRef.current) {
            initializeChart();
          }
          updateChartWithData(powerData, coolingLoadData);
        }, 200);

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    // Initial load with a delay to ensure DOM is ready
    const initTimer = setTimeout(initializeAndFetchData, 300);
    
    // Add resize event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      console.log('BuildingLoadGraph component unmounting');
      clearTimeout(initTimer);
      window.removeEventListener('resize', handleResize);
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
        chartInstanceRef.current = null;
      }
    };
  }, []);

  return (
    <div className="p-2.5 alto-card">
      <div className="text-[#065BA9] text-sm font-semibold mb-2">Building Load</div>
      {loading ? (
        <div className="flex items-center justify-center h-[300px]">
          <Loader/>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center h-[300px] text-red-500">
          {error}
        </div>
      ) : (
        <div ref={chartRef} style={{ width: "100%", height: "250px" }} />
      )}
    </div>
  );
};

export default BuildingLoadGraph;