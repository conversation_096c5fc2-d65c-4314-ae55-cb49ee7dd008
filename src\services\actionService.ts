import { api } from './api';

// Base interface with common properties
export interface ActionEventBase {
  action_id: string;
  description: string | null;
  last_attempt_time: string | null;
  retry_count: number;
  scheduled_time: string;
  scheduled_unix_timestamp: number;
  source: string;
  status: string;
  target_agent: string;
}

// Payload structure for Start/Stop Chiller Sequence actions
interface ChillerSequencePayload {
  stage?: string;
  chiller_id?: string | null; // Can be null if priority_index is used
  group_equipment?: string[];
  priority_index?: number;
  post_circulation?: boolean;
  post_circulation_delay?: number; // e.g., 1800 seconds
  post_circulation_start_timestamp?: number | null;
  chiller_stop_timestamp?: number | null;
  chiller_stopping_delay?: number; // Delay in seconds
}

// Payload structure for Schedule actions
interface ScheduleActionPayload {
  device_datapoint_pair_list: [string, string][]; // e.g., [["mvch_3", "status_write"], ["pchp_3", "status_write"]]
  value: boolean | number | string; // The value to be written
  end_time: string; // ISO 8601 format string
  end_unix_timestamp: number;
}

// Specific Event Interface for Start Chiller Sequence
export interface StartChillerSequenceEvent extends ActionEventBase {
  action_type: 'start_chiller_sequence';
  payload: ChillerSequencePayload;
}

// Specific Event Interface for Stop Chiller Sequence
export interface StopChillerSequenceEvent extends ActionEventBase {
  action_type: 'stop_chiller_sequence';
  payload: ChillerSequencePayload;
}

// Specific Event Interface for Schedule Action
export interface ScheduleActionEvent extends ActionEventBase {
  action_type: 'schedule'; // Assuming 'schedule' is the action_type string
  payload: ScheduleActionPayload;
}

// Discriminated Union Type for any possible Action Event
export type AnyActionEvent =
  | StartChillerSequenceEvent
  | StopChillerSequenceEvent
  | ScheduleActionEvent;

/**
 * Fetches action events from the MongoDB backend
 * @returns Promise with array of ActionEvent objects
 */
export const fetchActionEvents = async (): Promise<AnyActionEvent[]> => {
  try {
    const response = await api.get('/mongodb/action-events/');
    return response.data as AnyActionEvent[];
  } catch (error) {
    console.error('Error fetching action events:', error);
    return [];
  }
};

/**
 * Gets pending action events (status === 'pending')
 * @returns Promise with array of pending ActionEvent objects
 */
export const getPendingActionEvents = async (): Promise<AnyActionEvent[]> => {
  const events = await fetchActionEvents();
  return events.filter(event => event.status === 'pending');
};

/**
 * Gets completed action events (status === 'completed')
 * @returns Promise with array of completed ActionEvent objects
 */
export const getCompletedActionEvents = async (): Promise<AnyActionEvent[]> => {
  const events = await fetchActionEvents();
  return events.filter(event => event.status === 'completed');
};

/**
 * Gets action events for a specific chiller
 * @param chillerId The ID of the chiller to filter by
 * @returns Promise with array of ActionEvent objects for the specified chiller
 */
export const getActionEventsForChiller = async (chillerId: string): Promise<AnyActionEvent[]> => {
  const events = await fetchActionEvents();
  return events.filter(event => 
    (event.action_type === 'start_chiller_sequence' || event.action_type === 'stop_chiller_sequence') &&
    event.payload.chiller_id === chillerId
  );
};

/**
 * Gets action events of a specific type
 * @param actionType The type of action to filter by
 * @returns Promise with array of ActionEvent objects of the specified type
 */
export const getActionEventsByType = async (actionType: string): Promise<AnyActionEvent[]> => {
  const events = await fetchActionEvents();
  return events.filter(event => event.action_type === actionType);
};

/**
 * Checks if a chiller has any pending actions
 * @param chillerId The ID of the chiller to check
 * @returns Promise with boolean indicating if the chiller has pending actions
 */
export const hasChillerPendingActions = async (chillerId: string): Promise<boolean> => {
  const events = await fetchActionEvents();
  return events.some(event => 
    event.status === 'pending' &&
    (event.action_type === 'start_chiller_sequence' || event.action_type === 'stop_chiller_sequence') &&
    event.payload.chiller_id === chillerId
  );
};