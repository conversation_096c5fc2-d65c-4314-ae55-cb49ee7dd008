import { Badge } from "@/components/ui/badge";
import { Clock, Plus, Settings } from 'lucide-react';
import { Button } from "@/components/ui/button";

// Define the API schedule interface (should match with AutomationTabContent)
interface ApiSchedule {
  id: string;
  name: string;
  description: string;
  active: boolean;
  controls: ControlPair[];
  weekly_schedule: {
    monday: TimeRange[];
    tuesday: TimeRange[];
    wednesday: TimeRange[];
    thursday: TimeRange[];
    friday: TimeRange[];
    saturday: TimeRange[];
    sunday: TimeRange[];
  };
  special_days: any[];
  created_at?: string;
  updated_at?: string;
}

interface TimeRange {
  start_time: string; // "HH:MM" format
  end_time: string;   // "HH:MM" format
  value: boolean | number | string;
}

interface ControlPair {
  device_id: string;
  datapoint: string;
}

interface ChillerScheduleCardProps {
  schedules: ApiSchedule[]; 
  onScheduleClick: (schedule: ApiSchedule) => void;
  onScheduleConfigClick: (schedule: ApiSchedule) => void;
  onControlParamsClick: () => void;
  onChillerSettingsClick: () => void;
  onAddScheduleClick: () => void; 
}

export function ChillerScheduleCard({
  schedules,
  onScheduleClick,
  onScheduleConfigClick,
  onControlParamsClick,
  onChillerSettingsClick,
  onAddScheduleClick,
}: ChillerScheduleCardProps) {
  // Helper function to count active time ranges across days
  const countTimeRanges = (schedule: ApiSchedule): number => {
    return Object.values(schedule.weekly_schedule).reduce((total, dayRanges) => {
      return total + dayRanges.length;
    }, 0);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-[#0E7EE4]" />
          <h3 className="text-[#065BA9] text-sm font-semibold">Operating Schedules</h3>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={onAddScheduleClick}
            className="h-8 px-3 text-xs font-medium text-[#065BA9] border-[#DBE4FF] hover:bg-[#F9FAFF] hover:border-[#0E7EE4]"
          >
            <Plus className="w-4 h-4 mr-2 text-[#0E7EE4]" />
            Add Schedule
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          {schedules.map((schedule) => (
            <div 
              key={schedule.id}
              className="flex items-center justify-between p-3 bg-white border border-[#DBE4FF] rounded-lg hover:border-[#0E7EE4] transition-colors"
            >
              <div className="flex items-center gap-2">
                <span className="text-[#065BA9] text-sm font-semibold">
                  {schedule.name}
                </span>
                <Badge variant={schedule.active ? "success" : "secondary"} className="text-xs">
                  {schedule.active ? "Active" : "Inactive"}
                </Badge>
                <span className="text-gray-500 text-xs">
                  {countTimeRanges(schedule)} time ranges
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => onScheduleConfigClick(schedule)}
                  className="h-7 px-2 text-xs text-[#065BA9] hover:bg-[#F9FAFF]"
                >
                  Configure
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => onScheduleClick(schedule)}
                  className="h-7 px-2 text-xs text-[#065BA9] hover:bg-[#F9FAFF]"
                >
                  Edit
                </Button>
              </div>
            </div>
          ))}
          {schedules.length === 0 && (
            <div className="text-center text-gray-500 text-sm p-4 border border-dashed border-[#DBE4FF] rounded-lg">
              No schedules defined yet. Click "Add Schedule" to create one.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}