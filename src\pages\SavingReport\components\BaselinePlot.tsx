import React, { useState, useEffect, useRef, useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import * as echarts from 'echarts';
import type { EChartsOption } from 'echarts';
import 'echarts-gl';

type PlotType = 'scatter' | 'time' | 'bar';
type BarGrouping = 'daily' | 'monthly';
type MonthlyAggregation = 'sum' | 'average';

interface BaselinePlotProps {
  type: PlotType;
  isFullscreenComponent?: boolean;
}

const BaselinePlot: React.FC<BaselinePlotProps> = ({ type, isFullscreenComponent = false }) => {
  const {
    baselineData,
    variableSelection,
    regressionResult,
    baselineRange,
    weekdayFilterEnabled,
    excludedBaselineRowIds
  } = useSavingDashboard();
  const [selectedPlotType, setSelectedPlotType] = useState<PlotType>(type);
  const [barGrouping, setBarGrouping] = useState<BarGrouping>('daily');
  const [monthlyAggregation, setMonthlyAggregation] = useState<MonthlyAggregation>('average');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chartKey, setChartKey] = useState<string>('chart-0');
  const chartRef = useRef<ReactECharts>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Update selected plot type when the type prop changes
  useEffect(() => {
    setSelectedPlotType(type);
    // Update chart key when plot type changes
    setChartKey(`${type}-${Date.now()}`);
  }, [type]);

  // Update chart key when variables change to force re-render
  useEffect(() => {
    setChartKey(`${selectedPlotType}-${variableSelection.independent.join('-')}-${variableSelection.dependent}-${Date.now()}`);
  }, [variableSelection.independent, variableSelection.dependent, selectedPlotType]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreenNow = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullscreen(isFullscreenNow);
      
      if (chartRef.current) {
        const echartsInstance = chartRef.current.getEchartsInstance();
        setTimeout(() => {
          echartsInstance.resize();
        }, 100);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  const toggleFullscreen = () => {
    if (!containerRef.current) return;

    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      } else if ((containerRef.current as any).webkitRequestFullscreen) {
        (containerRef.current as any).webkitRequestFullscreen();
      } else if ((containerRef.current as any).mozRequestFullScreen) {
        (containerRef.current as any).mozRequestFullScreen();
      } else if ((containerRef.current as any).msRequestFullscreen) {
        (containerRef.current as any).msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen();
      } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
      }
    }
  };

  // Filter data based on baseline range, weekday filter, AND exclusion status
  const filteredData = useMemo(() => {
    return baselineData.filter(row => {
      // Apply date range filter
      if (baselineRange.start && baselineRange.end) {
        const rowDate = new Date(row.date);
        const startDate = new Date(baselineRange.start);
        const endDate = new Date(baselineRange.end);
        if (rowDate < startDate || rowDate > endDate) {
          return false;
        }
      }
      
      // Apply weekday filter
      if (weekdayFilterEnabled) {
        const dayOfWeek = new Date(row.date).getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          return false;
        }
      }
      
      // Apply exclusion filter
      if (row.id !== undefined && excludedBaselineRowIds.has(row.id)) {
        return false;
      }
      
      // If all checks pass, include the row
      return true;
    });
  }, [baselineData, baselineRange, weekdayFilterEnabled, excludedBaselineRowIds]);

  // Handle missing values in the dependent variable
  const handleMissingValues = (data: any[], dependentVar: string) => {
    // Calculate the mean of non-null values
    const validValues = data
      .map(row => row[dependentVar])
      .filter(val => val !== null && val !== undefined && !isNaN(val));
    
    const mean = validValues.length > 0 
      ? validValues.reduce((sum, val) => sum + val, 0) / validValues.length 
      : 0;

    // Replace missing values with the mean
    return data.map(row => ({
      ...row,
      [dependentVar]: row[dependentVar] === null || row[dependentVar] === undefined || isNaN(row[dependentVar])
        ? mean
        : row[dependentVar]
    }));
  };

  if (filteredData.length === 0) {
    return (
      <div className="p-3 text-center text-gray-500 text-sm">
        No data available{weekdayFilterEnabled ? ' for weekdays' : ''}. Please import data first.
      </div>
    );
  }

  const toggleBarGrouping = () => {
    setBarGrouping(prev => prev === 'daily' ? 'monthly' : 'daily');
  };

  const toggleMonthlyAggregation = () => {
    setMonthlyAggregation(prev => prev === 'average' ? 'sum' : 'average');
  };

  // Color palette based on design system
  const colorPalette = [
    '#4e79a7', // Blue for primary
    '#f28e2c', // Orange
    '#59a14f', // Green
    '#e15759', // Red
    '#76b7b2', // Teal
    '#edc949', // Yellow
    '#af7aa1', // Purple
    '#ff9da7', // Pink
    '#9c755f', // Brown
  ];

  // Generate options based on the plot type
  const getOptions = () => {
    switch (selectedPlotType) {
      case 'scatter':
        if (variableSelection.independent.length > 2) {
          return getTooManyVariablesMessage();
        } else if (variableSelection.independent.length === 2) {
          return get3DScatterOptions();
        } else {
          return getScatterOptions();
        }
      case 'time':
        return getTimeSeriesOptions();
      case 'bar':
        return getBarOptions();
      default:
        return getScatterOptions();
    }
  };

  // Message when too many variables are selected for scatter plot
  const getTooManyVariablesMessage = () => {
    return {
      title: {
        text: 'Cannot generate scatter plot',
        subtext: 'Too many independent variables selected (more than 2)',
        left: 'left',
        top: 'top',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        },
        subtextStyle: {
          fontSize: 14
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '10%',
        bottom: '10%'
      }
    };
  };

  // 2D scatter plot options
  const getScatterOptions = () => {
    // For scatter plot, we need at least one independent variable
    if (variableSelection.independent.length === 0) {
      return {
        title: {
          text: 'Please select at least one independent variable',
          left: 'left',
          top: 'top',
          textStyle: {
            fontSize: 14
          }
        }
      };
    }
    
    const independent = variableSelection.independent[0];
    const dependent = variableSelection.dependent;
    
    // Handle missing values before creating the data array
    const processedData = handleMissingValues(filteredData, dependent);
    
    const data = processedData.map(row => ({
      value: [row[independent], row[dependent]],
      date: row.date
    }));
    
    // Calculate R² for the specific variable
    let r2Text = '';
    if (regressionResult) {
      const correlation = calculateCorrelation(
        processedData.map(d => d[independent]),
        processedData.map(d => d[dependent])
      );
      r2Text = `${independent} (R²: ${(correlation * correlation).toFixed(3)})`;
    }

    return {
      title: {
        text: `${dependent} vs ${independent}${weekdayFilterEnabled ? ' (Weekdays Only)' : ''}`,
        left: 'left',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          return `Date: ${new Date(params.data.date).toLocaleDateString()}<br/>${independent}: ${params.value[0].toFixed(2)}<br/>${dependent}: ${params.value[1].toFixed(2)}`;
        }
      },
      xAxis: {
        type: 'value',
        name: independent,
        nameLocation: 'middle',
        nameGap: 25,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value',
        name: dependent,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 10
        }
      },
      series: [
        {
          type: 'scatter',
          symbolSize: 8,
          data: data,
          itemStyle: {
            color: colorPalette[0]
          }
        }
      ],
      grid: {
        left: '10%',
        right: '5%',
        top: '15%',
        bottom: '15%'
      }
    };
  };

  // 3D scatter plot options
  const get3DScatterOptions = () => {
    if (variableSelection.independent.length !== 2) {
      return getScatterOptions();
    }
    
    const xVar = variableSelection.independent[0];
    const yVar = variableSelection.independent[1];
    const zVar = variableSelection.dependent;
    
    // Handle missing values before creating the data array
    const processedData = handleMissingValues(filteredData, zVar);
    
    // Create dataset
    const data = processedData.map(row => ({
      value: [row[xVar], row[yVar], row[zVar]],
      date: row.date
    }));
    
    return {
      title: {
        text: `3D Scatter Plot: ${zVar} vs ${xVar} & ${yVar}${weekdayFilterEnabled ? ' (Weekdays Only)' : ''}`,
        left: 'center',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 14
        }
      },
      tooltip: {
        formatter: function(params: any) {
          return `Date: ${new Date(params.data.date).toLocaleDateString()}<br/>`
            + `${xVar}: ${params.value[0].toFixed(2)}<br/>`
            + `${yVar}: ${params.value[1].toFixed(2)}<br/>`
            + `${zVar}: ${params.value[2].toFixed(2)}`;
        }
      },
      grid3D: {
        viewControl: {
          projection: 'perspective',
          autoRotate: true,
          autoRotateSpeed: 10,
          distance: 200
        }
      },
      xAxis3D: {
        name: xVar,
        type: 'value',
        nameTextStyle: {
          fontSize: 12
        }
      },
      yAxis3D: {
        name: yVar,
        type: 'value',
        nameTextStyle: {
          fontSize: 12
        }
      },
      zAxis3D: {
        name: zVar,
        type: 'value',
        nameTextStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          type: 'scatter3D',
          data: data,
          symbolSize: 6,
          itemStyle: {
            color: colorPalette[0],
            opacity: 0.8
          },
          emphasis: {
            itemStyle: {
              color: colorPalette[0],
              opacity: 1
            }
          }
        }
      ]
    };
  };

  const getTimeSeriesOptions = () => {
    const dependent = variableSelection.dependent;
    // Handle missing values before sorting
    const processedData = handleMissingValues(filteredData, dependent);
    
    // Sort data by date
    const sortedData = [...processedData].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    const dates = sortedData.map(row => row.date);
    const values = sortedData.map(row => row[dependent]);
    
    // Generate predicted values if regression model is available
    let predictedValues: number[] = [];
    if (regressionResult) {
      predictedValues = sortedData.map(row => {
        const predictors: { [key: string]: number } = {};
        variableSelection.independent.forEach(variable => {
          predictors[variable] = row[variable];
        });
        return regressionResult.predictionFunction(predictors);
      });
    }

    return {
      title: {
        text: `Baseline Time Series${weekdayFilterEnabled ? ' (Weekdays Only)' : ''}`,
        left: 'left',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          let result = params[0].axisValue + '<br/>';
          params.forEach((param: any) => {
            result += `${param.seriesName}: ${param.value.toFixed(2)}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: ['Actual Values', 'Predicted Values'],
        right: 10,
        top: 10,
        textStyle: {
          fontSize: 12
        }
      },
      xAxis: {
        type: 'category',
        data: dates,
        nameLocation: 'middle',
        nameGap: 25,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          rotate: 45,
          fontSize: 10,
          formatter: function(value: string) {
            return value.substring(0, 10);
          }
        }
      },
      yAxis: {
        type: 'value',
        name: dependent,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 10
        }
      },
      series: [
        {
          name: 'Actual Values',
          type: 'line',
          data: values,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: colorPalette[0]
          }
        },
        ...(regressionResult ? [
          {
            name: 'Predicted Values',
            type: 'line',
            data: predictedValues,
            symbol: 'circle',
            symbolSize: 5,
            lineStyle: {
              type: 'dashed'
            },
            itemStyle: {
              color: colorPalette[1]
            }
          }
        ] : [])
      ],
      grid: {
        left: '10%',
        right: '5%',
        top: '15%',
        bottom: '15%'
      }
    };
  };

  const getBarOptions = () => {
    const dependent = variableSelection.dependent;
    // Handle missing values before sorting
    const processedData = handleMissingValues(filteredData, dependent);
    
    // Sort data by date
    const sortedData = [...processedData].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    let dates: string[];
    let values: number[];

    if (barGrouping === 'monthly') {
      // Group data by month for monthly view
      const monthlyData: { [key: string]: number[] } = {};
      
      sortedData.forEach(row => {
        const date = new Date(row.date);
        const monthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
        
        if (!monthlyData[monthYear]) {
          monthlyData[monthYear] = [];
        }
        
        monthlyData[monthYear].push(row[dependent]);
      });
      
      dates = Object.keys(monthlyData).sort();
      values = dates.map(month => {
        const monthValues = monthlyData[month];
        if (monthlyAggregation === 'sum') {
          return monthValues.reduce((sum, val) => sum + val, 0);
        } else {
          return monthValues.reduce((sum, val) => sum + val, 0) / monthValues.length;
        }
      });
    } else {
      // Use daily data
      dates = sortedData.map(row => row.date);
      values = sortedData.map(row => row[dependent]);
    }

    return {
      title: {
        text: `${barGrouping === 'monthly' ? 'Monthly' : 'Daily'} ${dependent} ${barGrouping === 'monthly' ? `(${monthlyAggregation === 'average' ? 'Average' : 'Sum'})` : ''}${weekdayFilterEnabled ? ' (Weekdays Only)' : ''}`,
        left: 'left',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          return `${params[0].name.substring(0, 10)}<br/>${params[0].seriesName}: ${params[0].value.toFixed(2)}`;
        }
      },
      xAxis: {
        type: 'category',
        data: dates,
        nameLocation: 'middle',
        nameGap: 25,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          rotate: 45,
          fontSize: 10,
          formatter: function(value: string) {
            return value.substring(0, 10);
          }
        }
      },
      yAxis: {
        type: 'value',
        name: dependent,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 10
        }
      },
      series: [
        {
          name: dependent,
          type: 'bar',
          data: values,
          itemStyle: {
            color: colorPalette[0],
            borderRadius: [4, 4, 0, 0]
          }
        }
      ],
      grid: {
        left: '10%',
        right: '5%',
        top: '15%',
        bottom: '15%'
      }
    };
  };

  // Helper function to calculate correlation coefficient
  const calculateCorrelation = (x: number[], y: number[]): number => {
    const n = x.length;
    let sumX = 0;
    let sumY = 0;
    let sumXY = 0;
    let sumX2 = 0;
    let sumY2 = 0;
    
    for (let i = 0; i < n; i++) {
      sumX += x[i];
      sumY += y[i];
      sumXY += x[i] * y[i];
      sumX2 += x[i] * x[i];
      sumY2 += y[i] * y[i];
    }
    
    return (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
  };

  const chartOptions = getOptions();

  // Determine chart height based on fullscreen status
  const chartHeight = isFullscreen || isFullscreenComponent ? '100%' : '320px';

  return (
    <div className={isFullscreenComponent ? 'h-[90vh]' : 'h-auto'} ref={containerRef}>
      <div className="flex justify-end mb-1 space-x-2">
        {selectedPlotType === 'bar' && (
          <>
            <Button
              onClick={toggleBarGrouping}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              {barGrouping === 'daily' ? 'Switch to Monthly' : 'Switch to Daily'}
            </Button>
            
            {barGrouping === 'monthly' && (
              <Button
                onClick={toggleMonthlyAggregation}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                {monthlyAggregation === 'average' ? 'Switch to Sum' : 'Switch to Average'}
              </Button>
            )}
          </>
        )}
      </div>
      
      {selectedPlotType === 'scatter' && variableSelection.independent.length > 2 ? (
        <div className="flex flex-col items-center justify-center h-[300px] bg-gray-50 border rounded-md p-4">
          <AlertCircle className="h-10 w-10 text-amber-500 mb-2" />
          <h3 className="text-lg font-medium text-gray-800">Cannot display scatter plot</h3>
          <p className="text-gray-600 text-center mt-1">
            Too many independent variables selected (more than 2).<br />
            Please select 1 variable for 2D or 2 variables for 3D scatter plot.
          </p>
        </div>
      ) : (
        <ReactECharts
          ref={chartRef}
          option={chartOptions as any}
          style={{ height: chartHeight }}
          opts={{ renderer: 'canvas' }}
          key={chartKey}
        />
      )}
    </div>
  );
};

export default BaselinePlot;