import React, { useState, useEffect } from "react";
import { DeviceData } from "@/services/deviceService";
import { Switch } from "@/components/ui/switch";
import { useRealtime } from "@/contexts/RealtimeContext";
import { useAutopilot } from "@/contexts/AutopilotContext";
import { useDevice, Device } from "@/contexts/DeviceContext";
import { sendControl, updateAutopilotStatus } from "@/services/controlService";
import { toast } from "@/components/ui/use-toast";
import { Check, X, ChevronUp, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Loader } from "@/components/ui/loader";
import manual_icon from '@/assets/manual_icon.svg';
import maintenance_icon from '@/assets/maintenance_icon.svg';

interface AHUControlPanelProps {
  ahu: DeviceData;
  openSettings?: (device: DeviceData) => void;
  onStatusChange?: (deviceId: string, newStatus: string) => void;
}

interface ConfirmationModalProps {
  isOpen: boolean;
  action: "start" | "stop";
  onConfirm: () => Promise<void>;
  onCancel: () => void;
}

interface EditingState {
  field: "temperature" | "vsd" | "pressure" | null;
  value: number;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  action,
  onConfirm,
  onCancel,
}) => {
  const [status, setStatus] = useState<
    "idle" | "loading" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");

  const handleConfirmClick = async () => {
    setStatus("loading");
    try {
      await onConfirm();
      setStatus("success");
      setTimeout(() => {
        setStatus("idle");
        onCancel();
      }, 1000);
    } catch (error) {
      setStatus("error");
      setErrorMessage(
        error instanceof Error ? error.message : "Failed to send command"
      );
      setTimeout(() => {
        setStatus("idle");
      }, 2000);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/20 backdrop-blur-[1px] rounded flex items-center justify-center z-50">
      <div className="bg-white rounded-md p-3 shadow-lg w-[20vw]">
        {status === "idle" && (
          <>
            <div className="text-sm font-medium text-foreground mb-2 text-center">
              Confirm {action === "start" ? "Start" : "Stop"} AHU?
            </div>
            <div className="flex gap-2">
              <button
                className="flex-1 h-7 text-xs rounded bg-muted/10 hover:bg-muted/20 text-muted-foreground text-center"
                onClick={onCancel}
              >
                Cancel
              </button>
              <button
                className={cn(
                  "flex-1 h-7 text-xs rounded text-white text-center",
                  action === "start"
                    ? "bg-success hover:bg-success/90"
                    : "bg-destructive hover:bg-destructive/90"
                )}
                onClick={handleConfirmClick}
              >
                Confirm
              </button>
            </div>
          </>
        )}

        {status === "loading" && (
          <div className="flex flex-col items-center justify-center py-1">
            <Loader />
            <div className="text-xs font-medium text-foreground mt-2 text-center">
              Sending command...
            </div>
          </div>
        )}

        {status === "success" && (
          <div className="flex flex-col items-center justify-center py-1">
            <div className="text-success">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M20 6L9 17L4 12" />
              </svg>
            </div>
            <div className="text-xs font-medium text-success mt-1 text-center">
              Command sent successfully
            </div>
          </div>
        )}

        {status === "error" && (
          <div className="flex flex-col items-center justify-center py-1">
            <div className="text-destructive">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M15 9L9 15M9 9L15 15" />
              </svg>
            </div>
            <div className="text-xs font-medium text-destructive mt-1 text-center">
              {errorMessage}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const AutoManualSwitch: React.FC<{
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
}> = ({ checked, onCheckedChange, disabled }) => {
  return (
    <div 
      className="relative inline-flex items-center" 
      onClick={(e) => e.stopPropagation()}
    >
      <Switch
        checked={checked}
        onCheckedChange={onCheckedChange}
        disabled={disabled}
        size="sm"
        className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-[#0E7EE4] data-[state=checked]:to-[#14B8B4]"
      />
      <div className="absolute inset-0 flex justify-between items-center px-[6px] pointer-events-none">
        <span className="text-[8px] font-medium text-white">A</span>
        <span className="text-[8px] font-medium text-white">M</span>
      </div>
    </div>
  );
};

const AHUControlPanel: React.FC<AHUControlPanelProps> = ({
  ahu,
  openSettings,
  onStatusChange,
}) => {
  const { getValue } = useRealtime();
  const { isDeviceInAutopilotMode } = useAutopilot();
  const { isDeviceUnderMaintenance, getDevicesByType } = useDevice();

  // Get AHU control values
  const [status, setStatus] = useState<boolean>(false);
  const [isUpdatingAutopilot, setIsUpdatingAutopilot] = useState<boolean>(false);

  // Setpoint values
  const [pressureSP, setPressureSP] = useState<number>(0);
  const [vsdSpeed, setVsdSpeed] = useState<number>(0);
  const [supplyTemp, setSupplyTemp] = useState<number>(0);

  // Editing state
  const [editing, setEditing] = useState<EditingState | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Status confirmation
  const [confirmation, setConfirmation] = useState<{
    isOpen: boolean;
    action: "start" | "stop";
  } | null>(null);

  // Update state when ahu prop changes
  useEffect(() => {
    // Get current status from latest_data using status_write
    const statusValue = Number(getValue(ahu.deviceId, "status_read"));
    setStatus(statusValue === 1);

    // Get current pressure setpoint with default fallback
    const currentPressureSP = Number(
      getValue(ahu.deviceId, "static_pressure_setpoint_read")
    );
    if (!isNaN(currentPressureSP)) {
      setPressureSP(currentPressureSP);
    }

    // Only try to get VSD speed if we have a floor number
    if (ahu.deviceId) {
      // Determine the correct VSD device ID based on floor
      const vsdDeviceId = `vsd_${ahu.deviceId}`;

      // Get current VSD speed with default fallback - using correct datapoint
      const currentVsdSpeed = Number(getValue(vsdDeviceId, "frequency_read"));
      if (!isNaN(currentVsdSpeed)) {
        setVsdSpeed(currentVsdSpeed);
      }
    }

    // Get current supply temperature with default fallback
    const currentSupplyTemp = Number(
      getValue(ahu.deviceId, "supply_air_temperature_setpoint_read")
    );
    if (!isNaN(currentSupplyTemp)) {
      setSupplyTemp(currentSupplyTemp);
    }
  }, [ahu.deviceId, getValue]);

  // Handle toggling autopilot mode
  const handleAutopilotToggle = async (checked: boolean) => {
    try {
      setIsUpdatingAutopilot(true);

      // Get associated VSD and VAV devices
      const vsdDeviceId = `vsd_${ahu.deviceId}`;
      const devicesByType = getDevicesByType();
      const associatedVavs = devicesByType['vav']?.filter(
        (device: Device) => {
          const deviceData = device as unknown as DeviceData;
          return deviceData.floor === ahu.floor;
        }
      ) || [];

      // Update AHU autopilot status
      await updateAutopilotStatus(ahu.deviceId, checked);

      // Update VSD autopilot status
      await updateAutopilotStatus(vsdDeviceId, checked);

      // Update all associated VAVs autopilot status
      const vavUpdates = associatedVavs.map((vav: Device) => 
        updateAutopilotStatus(vav.deviceId, checked)
      );
      await Promise.all(vavUpdates);

      toast({
        title: "Mode updated",
        description: `Successfully switched to ${checked ? "Autopilot" : "Manual"} mode for AHU and associated devices`,
        variant: "default",
      });
    } catch (error) {
      console.error("Error updating autopilot status:", error);
      toast({
        title: "Failed to update mode",
        description: "The mode update failed. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingAutopilot(false);
    }
  };

  // Handle toggling AHU status
  const toggleStatus = async () => {
    setConfirmation({
      isOpen: true,
      action: !status ? "start" : "stop",
    });
  };

  const handleConfirmStatus = async () => {
    if (!confirmation) return;

    const newStatus = confirmation.action === "start";

    try {
      // Send the status_write command to turn the device on or off
      await sendControl(ahu.deviceId, "status_write", newStatus ? 1 : 0);

      // Update local state
      setStatus(newStatus);

      // Update parent component to refresh monitoring view
      if (onStatusChange) {
        onStatusChange(ahu.deviceId, newStatus ? "normal" : "off");
      }
    } catch (error) {
      throw new Error("Failed to change AHU status");
    }
  };

  // Render editable cell
  const renderEditableCell = (
    field: EditingState["field"],
    value: number | undefined,
    suffix: string,
    min?: number,
    max?: number
  ) => {
    const isEditing = editing?.field === field;

    if (isEditing) {
      // Calculate step based on field type
      const step = field === "temperature" ? 0.5 : 1;

      return (
        <div className="flex items-center gap-1">
          <button
            onClick={() => adjustValue(false)}
            className="p-1 bg-slate-100 rounded hover:bg-slate-200"
            disabled={isSaving}
          >
            <ChevronDown className="h-3 w-3 text-slate-600" />
          </button>

          <input
            type="text"
            value={editing.value}
            onChange={(e) => handleInputChange(e.target.value)}
            className="w-14 py-0.5 px-1 text-center border border-blue-300 rounded text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
            step={step}
            min={min}
            max={max}
            disabled={isSaving}
          />

          <button
            onClick={() => adjustValue(true)}
            className="p-1 bg-slate-100 rounded hover:bg-slate-200"
            disabled={isSaving}
          >
            <ChevronUp className="h-3 w-3 text-slate-600" />
          </button>

          <div className="flex items-center ml-1">
            <button
              onClick={saveValue}
              className="p-1 text-green-600 hover:text-green-700 disabled:opacity-50"
              disabled={isSaving}
            >
              <Check className="h-3 w-3" />
            </button>
            <button
              onClick={cancelEditing}
              className="p-1 text-red-600 hover:text-red-700 disabled:opacity-50"
              disabled={isSaving}
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
      );
    }

    // Display value or placeholder with more intense hover effect
    return (
      <div
        className="cursor-pointer hover:bg-blue-100 py-1 px-2 rounded transition-colors duration-150"
        onClick={() => value !== undefined && startEditing(field, value)}
      >
        {value !== undefined
          ? `${value?.toLocaleString("en-US", {
              minimumFractionDigits: 1,
              maximumFractionDigits: 1,
            })}${suffix}`
          : "--"}
      </div>
    );
  };

  // Start editing a field
  const startEditing = (field: EditingState["field"], currentValue: number) => {
    setEditing({
      field,
      value: currentValue,
    });
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditing(null);
  };

  // Handle value change in the input field
  const handleInputChange = (value: string) => {
    if (editing) {
      setEditing({
        ...editing,
        value: Number(value),
      });
    }
  };

  // Increment or decrement the value being edited
  const adjustValue = (increment: boolean) => {
    if (editing) {
      let step = 1;

      // Use different step sizes based on the field
      if (editing.field === "temperature") {
        step = 0.5;
      } else if (editing.field === "vsd") {
        step = 5;
      } else if (editing.field === "pressure") {
        step = 10;
      }

      setEditing({
        ...editing,
        value: increment ? editing.value + step : editing.value - step,
      });
    }
  };

  // Save edited value to the database
  const saveValue = async () => {
    if (!editing) return;

    setIsSaving(true);

    try {
      let deviceId = ahu.deviceId;
      let datapoint = "";
      let value = editing.value;

      // Determine which datapoint to update
      switch (editing.field) {
        case "temperature":
          datapoint = "supply_air_temperature_setpoint_write";
          // Ensure value is between 13-20°C
          value = Math.max(13, Math.min(20, value));
          break;
        case "vsd":
          datapoint = "frequency_write";
          deviceId = `vsd_${ahu.deviceId}`;
          // Ensure value is between 25-50 Hz
          value = Math.max(25, Math.min(50, value));
          break;
        case "pressure":
          datapoint = "static_pressure_setpoint_write";
          // Ensure value is between 0-10000 Pa
          value = Math.max(0, Math.min(10000, value));
          break;
        default:
          return;
      }

      // Send the control command
      await sendControl(deviceId, datapoint, value);

      // Update local state
      switch (editing.field) {
        case "temperature":
          setSupplyTemp(value);
          break;
        case "vsd":
          setVsdSpeed(value);
          break;
        case "pressure":
          setPressureSP(value);
          break;
      }

      // Show success toast
      toast({
        title: "Value updated",
        description: `Successfully updated ${datapoint.replace(
          /_/g,
          " "
        )} to ${value}`,
        variant: "default",
      });

      // Clear editing state
      setEditing(null);
    } catch (error) {
      console.error(`Error updating value:`, error);

      // Show error toast
      toast({
        title: "Failed to update value",
        description: "The update failed. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-3">
        <h4 className="text-[12px] font-semibold text-[#0E7EE4] flex items-center gap-1">
          {isDeviceUnderMaintenance(ahu.deviceId) && 
            <img src={maintenance_icon} alt="Under Maintenance" className="w-[10px] h-[10px]" />
          }
          AHU Control
        </h4>
        <div className="flex items-center gap-2">
          <div className="text-[9px] text-[#9CA3AF]">Mode</div>
          <div className="flex items-center">
            <AutoManualSwitch
              checked={isDeviceInAutopilotMode(ahu.deviceId)}
              onCheckedChange={handleAutopilotToggle}
              disabled={isUpdatingAutopilot}
            />
            <span className="text-[9px] text-[#212529] ml-1">
              {isDeviceInAutopilotMode(ahu.deviceId) ? "Autopilot" : "Manual"}
            </span>
          </div>
          <div className="text-[9px] text-[#9CA3AF] ml-2">Status</div>
          <div className="flex items-center">
            <Switch
              checked={status}
              onCheckedChange={() => toggleStatus()}
              className="scale-90 origin-left"
              disabled={!!confirmation}
              size="sm"
            />
            <span className="text-[9px] text-[#212529]">
              {status ? "On" : "Off"}
            </span>
          </div>
        </div>
      </div>

      <div className="border border-slate-200 rounded-md overflow-hidden">
        <table className="w-full text-xs">
          <thead>
            <tr className="bg-[#EDEFF9] text-[10px] text-[#788796]">
              <th className="py-1.5 px-2 text-left">Parameter</th>
              <th className="py-1.5 px-2 text-left">Value</th>
              <th className="py-1.5 px-2 text-left">Unit</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-t border-slate-200 hover:bg-slate-50 bg-white">
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                Supply Air Temperature Setpoint
              </td>
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                {renderEditableCell("temperature", supplyTemp, " °C", 18, 26)}
              </td>
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">°C</td>
            </tr>
            <tr className="border-t border-slate-200 hover:bg-slate-50 bg-slate-50/30">
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                VSD Speed Control
              </td>
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                {renderEditableCell("vsd", vsdSpeed, " Hz", 0, 25)}
              </td>
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">Hz</td>
            </tr>
            <tr className="border-t border-slate-200 hover:bg-slate-50 bg-white">
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                Static Pressure Setpoint
              </td>
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                {renderEditableCell("pressure", pressureSP, " Pa", 0, 1000)}
              </td>
              <td className="py-1.5 px-2 text-[10px] text-[#212529]">Pa</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AHUControlPanel;
