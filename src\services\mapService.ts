import { api } from './api';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import { getSiteId } from './authService';
import { ComponentConfig, TableSettings } from '@/pages/Map/components/types';

interface ManualControlRequest {
  deviceId: string;
  command: string;
  value?: number | boolean;
}

export interface MapConfig {
  components: ComponentConfig[];
  properties: {
    toolbarPosition: {
      x: number;
      y: number;
    };
    mapConfig: {
      zoomLevel: number;
      xOffset: number;
      yOffset: number;
      backgroundImage?: string;
    };
    tableConfig: TableSettings;
    headerLogo?: string | null;
  };
}

interface MapMetadata {
  site: string;
  name: string;
  description: string;
  background_image: string | null;
  width: number;
  height: number;
  scale: number;
  properties: {
    gridSize: number;
    snapToGrid: boolean;
    showGrid: boolean;
  };
}

interface MediaUploadResponse {
  id: number;
  filename: string;
  file_type: string;
  url: string;
  size: number;
  content_type: string;
}

// Manual control endpoint
export const manualControl = async (data: ManualControlRequest): Promise<void> => {
  await api.post('/manual_control', data);
};

// Map configuration endpoints
export const createMap = async (metadata: MapMetadata): Promise<any> => {
  const response = await api.post('/maps/', metadata);
  return response.data;
};

export const updateMapComponents = async (mapId: string, components: ComponentConfig[]): Promise<void> => {
  await api.post<void>(`/maps/${mapId}/update_components/`, { components });
};

// Media handling endpoints
export const uploadMedia = async (file: File): Promise<MediaUploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await api.post<MediaUploadResponse>('/media/upload/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

export const getMedia = async (fileName: string): Promise<Blob> => {
  const response = await api.get(`/media/${fileName}`, {
    responseType: 'blob'
  });
  return response.data as Blob;
};

// Map configuration loading and saving
export const loadMapConfig = async (mapId: string): Promise<MapConfig> => {
  const response = await api.get<MapConfig>(`/maps/${mapId}`);
  
  // Sort components so GIF type comes after everything else
  if (response.data.components) {
    const nonGifComponents = response.data.components.filter(c => c.type !== 'gif');
    const gifComponents = response.data.components.filter(c => c.type === 'gif');
    response.data.components = [...gifComponents, ...nonGifComponents];
  }

  console.log('Loaded map config:', response.data);
  return response.data;
};

export const saveMapConfig = async (mapId: string, config: MapConfig): Promise<void> => {
  try {
    const siteId = getSiteId();
    if (!siteId) {
      throw new Error('No site_id found in local storage');
    }
    
    const configWithSiteAndName = {
      ...config,
      site: siteId,
      name: 'Main Chiller Plant Map'
    };
    console.log("saving map config: ", configWithSiteAndName);
    await api.put<void>(`/maps/${mapId}/`, configWithSiteAndName);
  } catch (error) {
    throw new Error('Failed to save map configuration');  
  }
};

// Import/Export functionality
export const exportMapConfig = async (mapId: string): Promise<void> => {
  try {
    // Load current map configuration
    const mapConfig = await loadMapConfig(mapId);
    
    // Create a new zip file
    const zip = new JSZip();
    
    // Add the map configuration as a JSON file
    zip.file('map-config.json', JSON.stringify(mapConfig, null, 2));
    
    // Find all media files referenced in the configuration
    const mediaFilesToExport = new Set<string>();
    
    // Add background image if it exists in map config
    if ((mapConfig.properties.mapConfig as any).backgroundImage) {
      const filename = (mapConfig.properties.mapConfig as any).backgroundImage.split('/').pop();
      if (filename) mediaFilesToExport.add(filename);
    }
    
    // Add media files from components
    if (mapConfig.components) {
      // Extract media filenames from components
      for (const component of mapConfig.components) {
        const props = component.properties as any; // Use type assertion for property access
        
        // Handle different component types and their media properties
        switch (component.type) {
          case 'gif':
            // GIF components may have a gifName property
            if (props?.gifName) {
              const filename = props.gifName.split('/').pop();
              if (filename) mediaFilesToExport.add(filename);
            }
            break;
            
          case 'transitioning-gif':
            // Check for GIF filenames in transitioning GIFs
            if (props?.openGif) {
              const filename = props.openGif.split('/').pop();
              if (filename) mediaFilesToExport.add(filename);
            }
            if (props?.closedGif) {
              const filename = props.closedGif.split('/').pop();
              if (filename) mediaFilesToExport.add(filename);
            }
            if (props?.inTransitionGif) {
              const filename = props.inTransitionGif.split('/').pop();
              if (filename) mediaFilesToExport.add(filename);
            }
            break;
            
          default:
            // Check common image properties for all component types
            if (props?.imageSrc) {
              const filename = props.imageSrc.split('/').pop();
              if (filename) mediaFilesToExport.add(filename);
            }
            if (props?.backgroundImage) {
              const filename = props.backgroundImage.split('/').pop();
              if (filename) mediaFilesToExport.add(filename);
            }
            break;
        }
      }
    }
    
    // Download each media file and add to zip - now adding files directly to root instead of a media folder
    if (mediaFilesToExport.size > 0) {
      console.log('Exporting media files:', Array.from(mediaFilesToExport));
      await Promise.all(
        Array.from(mediaFilesToExport).map(async (filename) => {
          try {
            const blob = await getMedia(filename);
            // Add directly to root of zip instead of in a media folder
            zip.file(filename, blob);
          } catch (error) {
            console.error(`Failed to export media file: ${filename}`, error);
          }
        })
      );
    }
    
    // Generate the zip file
    const content = await zip.generateAsync({ type: 'blob' });
    
    // Save the file to user's computer
    saveAs(content, `map-export-${mapId}.zip`);
  } catch (error) {
    console.error('Failed to export map configuration', error);
    throw new Error('Failed to export map configuration');
  }
};

export const importMapConfig = async (file: File, mapId: string): Promise<void> => {
  try {
    const zip = await JSZip.loadAsync(file);
    
    // Extract the map configuration
    const configFile = zip.file('map-config.json');
    if (!configFile) {
      throw new Error('Invalid map configuration file: map-config.json not found');
    }
    
    const configContent = await configFile.async('text');
    let mapConfig;
    try {
      mapConfig = JSON.parse(configContent) as MapConfig;
    } catch (e) {
      throw new Error('Invalid map configuration: JSON parsing failed');
    }
    
    // First collect all media files (could be in root or in a media folder for backward compatibility)
    const mediaFiles: JSZip.JSZipObject[] = [];
    
    // Check files in root of zip
    const rootFiles = Object.values(zip.files)
      .filter(file => !file.dir && file.name !== 'map-config.json');
    mediaFiles.push(...rootFiles);
    
    // Also check in media folder for backward compatibility
    const mediaFolder = zip.folder('media');
    if (mediaFolder) {
      const nestedFiles = Object.values(mediaFolder.files)
        .filter(file => !file.dir && !file.name.endsWith('/'));
      mediaFiles.push(...nestedFiles);
    }
    
    console.log(`Found ${mediaFiles.length} media files to import`);
    
    // Upload each media file
    for (const file of mediaFiles) {
      try {
        const fileName = file.name.split('/').pop();
        if (fileName) {
          const content = await file.async('blob');
          const fileObj = new File([content], fileName, { type: content.type });
          
          // Upload the media file
          await uploadMedia(fileObj);
        }
      } catch (error) {
        console.error(`Error uploading media file ${file.name}:`, error);
        // Continue with other files even if one fails
      }
    }
    
    // Save the imported configuration
    await saveMapConfig(mapId, mapConfig);
    
    console.log('Map configuration imported successfully');
  } catch (error) {
    console.error('Failed to import map configuration', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to import map configuration');
  }
}; 