import React, { useMemo } from 'react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { ReportingDataRow } from '../types';
import { CheckCircle, CircleSlash } from 'lucide-react';

const ReportingTable: React.FC = () => {
  const {
    reportingData,
    reportingRange,
    regressionResult,
    variableSelection,
    weekdayFilterEnabled,
    excludedReportingRowIds,
    setExcludedReportingRowIds
  } = useSavingDashboard();

  // Get the unit label based on the dependent variable
  const getUnitLabel = () => {
    if (variableSelection.dependent === 'plant_energy' || variableSelection.dependent === 'total_energy') {
      return 'kWh';
    } else if (variableSelection.dependent === 'plant_efficiency' || variableSelection.dependent === 'total_efficiency') {
      return 'kW/Ton';
    } else if (variableSelection.dependent === 'temperature') {
      return '°C';
    } else if (variableSelection.dependent === 'humidity') {
      return '%';
    } else if (variableSelection.dependent === 'cooling_load' || variableSelection.dependent === 'airside_energy') {
      return 'kWh';
    } else if (variableSelection.dependent === 'cdd') {
      return 'degree days';
    } else {
      return 'units';
    }
  };

  const unitLabel = useMemo(() => getUnitLabel(), [variableSelection.dependent]);

  // Filter data based on reporting range and weekday filter FIRST
  const dateFilteredData = useMemo(() => {
    return reportingData.filter(row => {
      if (!reportingRange.start || !reportingRange.end) return true;
      
      const rowDate = new Date(row.date);
      const startDate = new Date(reportingRange.start);
      const endDate = new Date(reportingRange.end);
      
      // First check date range
      const inDateRange = rowDate >= startDate && rowDate <= endDate;
      
      // Then apply weekday filter if enabled
      if (weekdayFilterEnabled) {
        const dayOfWeek = rowDate.getDay();
        // 0 is Sunday, 6 is Saturday - so 1-5 are weekdays
        return inDateRange && dayOfWeek >= 1 && dayOfWeek <= 5;
      }
      
      return inDateRange;
    });
  }, [reportingData, reportingRange, weekdayFilterEnabled]);

  // THEN, filter based on exclusion state
  const filteredData = useMemo(() => {
    // Ensure row.id is defined before checking exclusion
    return dateFilteredData.filter(row => row.id !== undefined && !excludedReportingRowIds.has(row.id));
  }, [dateFilteredData, excludedReportingRowIds]);

  // Handler to toggle row exclusion
  const handleToggleRowExclusion = (id: number | undefined) => {
    if (id === undefined) return;
    setExcludedReportingRowIds(prevExcluded => {
      const newExcluded = new Set(prevExcluded);
      if (newExcluded.has(id)) {
        newExcluded.delete(id);
      } else {
        newExcluded.add(id);
      }
      return newExcluded;
    });
  };

  if (dateFilteredData.length === 0) {
    return (
      <div className="p-3 text-center text-gray-500 text-sm">
        No reporting data available for the selected date range{weekdayFilterEnabled ? ' and weekday filter' : ''}.
      </div>
    );
  }

  return (
    <div className="h-full">
      <div className="overflow-auto h-full">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50 sticky top-0 z-2">
            <tr>
              <th scope="col" className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th scope="col" className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" title="Calculated using Model Equation from section 3">
                Baseline ({unitLabel})
              </th>
              <th scope="col" className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actual ({unitLabel})
              </th>
              <th scope="col" className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Savings ({unitLabel})
              </th>
              <th scope="col" className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Savings (%)
              </th>
              <th scope="col" className="px-2 py-1.5 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Include
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {dateFilteredData.map((row: ReportingDataRow) => {
              // Ensure row.id is defined before checking exclusion
              const isExcluded = row.id !== undefined && excludedReportingRowIds.has(row.id);
              return (
                <tr key={row.id ?? row.date} className={`hover:bg-gray-50 ${isExcluded ? 'opacity-40' : ''}`}>
                  <td className="px-2 py-1.5 whitespace-nowrap text-xs text-gray-500">{row.date}</td>
                  <td className="px-2 py-1.5 whitespace-nowrap text-xs text-gray-500" title="From Model Equation">{row.baseline_kwh.toFixed(2)}</td>
                  <td className="px-2 py-1.5 whitespace-nowrap text-xs text-gray-500">{row.actual_kwh.toFixed(2)}</td>
                  <td className="px-2 py-1.5 whitespace-nowrap text-xs font-medium" style={{ color: row.savings_kwh >= 0 ? 'green' : 'red' }}>
                    {row.savings_kwh.toFixed(2)}
                  </td>
                  <td className="px-2 py-1.5 whitespace-nowrap text-xs font-medium" style={{ color: row.savings_percent >= 0 ? 'green' : 'red' }}>
                    {row.savings_percent.toFixed(2)}%
                  </td>
                  <td className="px-2 py-1.5 whitespace-nowrap text-xs text-center">
                    <button
                      disabled={row.id === undefined}
                      onClick={() => handleToggleRowExclusion(row.id)}
                      className={`p-1 rounded ${isExcluded ? 'text-gray-400 hover:text-gray-600' : 'text-green-600 hover:text-green-800'} ${row.id === undefined ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title={row.id === undefined ? 'Cannot toggle row without ID' : (isExcluded ? 'Include row' : 'Exclude row')}
                    >
                      {isExcluded ? <CircleSlash size={14} /> : <CheckCircle size={14} />}
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      {regressionResult && (
        <div className="mt-2 text-xs text-blue-600">
          Baseline values calculated using Model Equation: {regressionResult.equation}
        </div>
      )}
    </div>
  );
};

export default ReportingTable;