import React, { useMemo } from 'react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { DataRow } from '../types';
import { CheckCircle, CircleSlash } from 'lucide-react';

const BaselineTable: React.FC = () => {
  const { 
    baselineData, 
    baselineRange, 
    variableSelection,
    weekdayFilterEnabled,
    excludedBaselineRowIds,
    setExcludedBaselineRowIds
  } = useSavingDashboard();

  // Filter data based on baseline range and weekday filter FIRST
  const dateFilteredData = useMemo(() => baselineData.filter(row => {
    if (!baselineRange.start || !baselineRange.end) return true;
    
    const rowDate = new Date(row.date);
    const startDate = new Date(baselineRange.start);
    const endDate = new Date(baselineRange.end);
    
    const inDateRange = rowDate >= startDate && rowDate <= endDate;
    
    if (weekdayFilterEnabled) {
      const dayOfWeek = rowDate.getDay();
      return inDateRange && dayOfWeek >= 1 && dayOfWeek <= 5;
    }
    
    return inDateRange;
  }), [baselineData, baselineRange, weekdayFilterEnabled]);

  // THEN, filter based on exclusion state
  const filteredData = useMemo(() => {
    // Ensure row.id is defined before checking exclusion
    return dateFilteredData.filter(row => row.id !== undefined && !excludedBaselineRowIds.has(row.id));
  }, [dateFilteredData, excludedBaselineRowIds]);

  // Get all columns to display
  const columnsToDisplay = ['date'];
  
  // Add dependent variable if selected
  if (variableSelection.dependent) {
    columnsToDisplay.push(variableSelection.dependent);
  }
  
  // Add independent variables
  columnsToDisplay.push(...variableSelection.independent.filter(
    col => col !== variableSelection.dependent
  ));

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!variableSelection.dependent || filteredData.length === 0) {
      return null;
    }

    // Format date range
    const sortedData = [...filteredData].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );
    
    const startDate = sortedData[0]?.date || '';
    const endDate = sortedData[sortedData.length - 1]?.date || '';
    
    // Calculate number of days
    const numDays = filteredData.length;
    
    // Calculate total of dependent variable
    const dependentVar = variableSelection.dependent;
    const total = filteredData.reduce(
      (sum, row) => sum + (typeof row[dependentVar] === 'number' ? row[dependentVar] : 0), 
      0
    );
    
    // Calculate average
    const average = numDays > 0 ? total / numDays : 0;
    
    return {
      dateRange: `${startDate} - ${endDate}`,
      numDays,
      total,
      average
    };
  }, [filteredData, variableSelection.dependent]);

  // New handler to toggle row exclusion
  const handleToggleRowExclusion = (id: number | undefined) => {
    if (id === undefined) return;
    setExcludedBaselineRowIds(prevExcluded => {
      const newExcluded = new Set(prevExcluded);
      if (newExcluded.has(id)) {
        newExcluded.delete(id);
      } else {
        newExcluded.add(id);
      }
      return newExcluded;
    });
  };

  if (dateFilteredData.length === 0) {
    return (
      <div className="p-3 text-center text-gray-500 text-sm">
        No data available{weekdayFilterEnabled ? ' for weekdays' : ''}. Please import data first.
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {summaryStats && variableSelection.dependent && (
        <div className="bg-gray-50 p-2 mb-2 rounded-md text-xs">
          <div className="grid grid-cols-[4fr_1fr_4fr_3fr] gap-2">
            <div>
              <span className="font-medium">Date Range:</span> {summaryStats.dateRange}{weekdayFilterEnabled ? ' (Weekdays Only)' : ''}
            </div>
            <div>
              <span className="font-medium">Days:</span> {summaryStats.numDays}
            </div>
            <div>
              <span className="font-medium">Total {variableSelection.dependent}:</span> {summaryStats.total.toLocaleString(undefined, {minimumFractionDigits: 1, maximumFractionDigits: 1})}
            </div>
            <div>
              <span className="font-medium">Avg {variableSelection.dependent}:</span> {summaryStats.average.toLocaleString(undefined, {minimumFractionDigits: 3, maximumFractionDigits: 3})}
            </div>
          </div>
        </div>
      )}
      <div className="overflow-x-auto flex-1">
        <div className="overflow-y-auto max-h-[calc(100vh-400px)]">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0 z-0">
              <tr>
                {columnsToDisplay.map(column => (
                  <th key={column} scope="col" className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {column}
                  </th>
                ))}
                <th scope="col" className="px-2 py-1.5 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Include
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {dateFilteredData.map((row: DataRow) => {
                // Ensure row.id is defined before checking exclusion
                const isExcluded = row.id !== undefined && excludedBaselineRowIds.has(row.id);
                return (
                  <tr key={row.id ?? row.date} className={`hover:bg-gray-50 ${isExcluded ? 'opacity-40' : ''}`}>
                    {columnsToDisplay.map(column => (
                      <td key={column} className="px-2 py-1.5 whitespace-nowrap text-xs text-gray-500">
                        {column === 'date' ? row[column] : typeof row[column] === 'number' ? row[column].toFixed(2) : row[column]}
                      </td>
                    ))}
                    <td className="px-2 py-1.5 whitespace-nowrap text-xs text-center">
                      <button
                        // Disable button if id is undefined
                        disabled={row.id === undefined}
                        onClick={() => handleToggleRowExclusion(row.id)}
                        className={`p-1 rounded ${isExcluded ? 'text-gray-400 hover:text-gray-600' : 'text-green-600 hover:text-green-800'} ${row.id === undefined ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title={row.id === undefined ? 'Cannot toggle row without ID' : (isExcluded ? 'Include row' : 'Exclude row')}
                      >
                        {isExcluded ? <CircleSlash size={14} /> : <CheckCircle size={14} />}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default BaselineTable;