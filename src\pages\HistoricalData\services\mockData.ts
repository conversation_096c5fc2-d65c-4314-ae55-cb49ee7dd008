// Mock data for Historical Data Visualization

// Mock Devices
export const mockDevices = [
  { id: 'chiller-1', name: 'Chiller 1' },
  { id: 'chiller-2', name: 'Chiller 2' },
  { id: 'chiller-3', name: 'Chiller 3' },
  { id: 'chiller-4', name: 'Chiller 4' },
  { id: 'chiller-5', name: 'Chiller 5' },
  { id: 'chiller-6', name: 'Chiller 6' },
  { id: 'chiller-7', name: 'Chiller 7' },
  { id: 'chiller-8', name: 'Chiller 8' },
  { id: 'pchp-1', name: 'PCHP 1' },
  { id: 'pchp-2', name: 'PCHP 2' },
  { id: 'pchp-3', name: 'PCHP 3' },
  { id: 'pchp-4', name: 'PCHP 4' },
  { id: 'pchp-5', name: 'PCHP 5' },
  { id: 'cdp-1', name: 'CDP 1' },
  { id: 'cdp-2', name: 'CDP 2' },
  { id: 'cdp-3', name: 'CDP 3' },
  { id: 'mvct-1', name: 'MVCT 1' },
  { id: 'mvct-2', name: 'MVCT 2' },
  { id: 'ct-1', name: 'Cooling Tower 1' },
  { id: 'ct-2', name: 'Cooling Tower 2' },
];

// Mock Datapoints by Device Type
export const mockDatapoints = {
  chiller: [
    { id: 'power', name: 'Power (kW)' },
    { id: 'chw-flow', name: 'CHW Flow (GPM)' },
    { id: 'chw-supply-temp', name: 'CHW Supply Temp (°F)' },
    { id: 'chw-return-temp', name: 'CHW Return Temp (°F)' },
    { id: 'cw-flow', name: 'CW Flow (GPM)' },
    { id: 'cw-supply-temp', name: 'CW Supply Temp (°F)' },
    { id: 'cw-return-temp', name: 'CW Return Temp (°F)' },
    { id: 'evap-approach', name: 'Evaporator Approach (°F)' },
    { id: 'cond-approach', name: 'Condenser Approach (°F)' },
    { id: 'lift', name: 'Lift (°F)' },
    { id: 'kw-per-ton', name: 'kW/Ton' },
    { id: 'tons', name: 'Cooling Load (Tons)' },
  ],
  pump: [
    { id: 'power', name: 'Power (kW)' },
    { id: 'flow', name: 'Flow (GPM)' },
    { id: 'head', name: 'Head (ft)' },
    { id: 'speed', name: 'Speed (%)' },
    { id: 'suction-pressure', name: 'Suction Pressure (PSI)' },
    { id: 'discharge-pressure', name: 'Discharge Pressure (PSI)' },
    { id: 'efficiency', name: 'Efficiency (%)' },
  ],
  coolingTower: [
    { id: 'power', name: 'Fan Power (kW)' },
    { id: 'speed', name: 'Fan Speed (%)' },
    { id: 'approach', name: 'Approach (°F)' },
    { id: 'range', name: 'Range (°F)' },
    { id: 'effectiveness', name: 'Effectiveness (%)' },
    { id: 'cw-supply-temp', name: 'CW Supply Temp (°F)' },
    { id: 'cw-return-temp', name: 'CW Return Temp (°F)' },
    { id: 'ambient-wetbulb', name: 'Ambient WB Temp (°F)' },
  ],
  mvct: [
    { id: 'valve-position', name: 'Valve Position (%)' },
    { id: 'flow', name: 'Flow (GPM)' },
    { id: 'supply-temp', name: 'Supply Temp (°F)' },
    { id: 'return-temp', name: 'Return Temp (°F)' },
    { id: 'pressure-drop', name: 'Pressure Drop (PSI)' },
  ]
};

// Function to get datapoints based on device ID
export const getDatapointsForDevice = (deviceId: string) => {
  if (deviceId.startsWith('chiller')) {
    return mockDatapoints.chiller;
  } else if (deviceId.startsWith('pchp') || deviceId.startsWith('cdp')) {
    return mockDatapoints.pump;
  } else if (deviceId.startsWith('ct')) {
    return mockDatapoints.coolingTower;
  } else if (deviceId.startsWith('mvct')) {
    return mockDatapoints.mvct;
  }
  return [];
};