// import jsPDF from 'jspdf';
// import 'jspdf-autotable';
// import { format } from 'date-fns';
// import { 
//   RegressionResult, 
//   SummaryStats, 
//   DataRow,
//   ReportingDataRow
// } from '../types';

// // Extend jsPDF to include autoTable
// declare module 'jspdf' {
//   interface jsPDF {
//     autoTable: (options: any) => jsPDF;
//     lastAutoTable: {
//       finalY: number;
//     };
//   }
// }

// /**
//  * Format numbers with commas and fixed decimal places
//  */
// export const formatNumber = (num: number | undefined, decimals: number = 2): string => {
//   if (num === undefined || num === 0) return "";
//   return num.toLocaleString('en-US', { minimumFractionDigits: decimals, maximumFractionDigits: decimals });
// };

// /**
//  * Generate a comprehensive PDF report combining baseline and savings information
//  */
// export const generateFullReport = (
//   // Baseline data
//   baselineData: DataRow[],
//   regressionResult: RegressionResult | null,
//   baselineRange: { start: string; end: string },
//   variableSelection: { dependent: string; independent: string[] },
//   excludedBaselineRowIds: Set<number>,
  
//   // Reporting data
//   reportingData: ReportingDataRow[],
//   reportingRange: { start: string; end: string },
//   summaryStats: SummaryStats | null,
  
//   // Other parameters
//   selectedYear: number,
//   selectedMonth: number
// ) => {
//   try {
//     console.log('Starting PDF generation with jsPDF...');
    
//     // Create PDF in landscape orientation
//     const doc = new jsPDF({
//       orientation: 'landscape',
//       unit: 'mm'
//     });
    
//     console.log('jsPDF created successfully');
//     console.log('autoTable available:', !!doc.autoTable);
    
//     const pageWidth = doc.internal.pageSize.getWidth();
//     const pageHeight = doc.internal.pageSize.getHeight();
//     const margin = 10;
    
//     // Cover page
//     addCoverPage(doc, pageWidth, pageHeight, margin);
    
//     // Add baseline model details (Page 1)
//     doc.addPage();
//     addBaselineModelDetails(
//       doc, 
//       baselineData, 
//       regressionResult, 
//       baselineRange, 
//       variableSelection, 
//       excludedBaselineRowIds,
//       pageWidth,
//       pageHeight,
//       margin
//     );
    
//     // Add savings report (Page 2)
//     doc.addPage();
//     addSavingsReport(
//       doc, 
//       reportingData, 
//       reportingRange, 
//       summaryStats, 
//       selectedYear,
//       selectedMonth,
//       pageWidth,
//       pageHeight,
//       margin
//     );
    
//     // Add signature page
//     doc.addPage();
//     addSignaturePage(doc, pageWidth, pageHeight, margin);
    
//     // Save the PDF
//     const fileName = selectedMonth === 0 
//       ? `Energy_Savings_Report_${selectedYear}.pdf` 
//       : `Energy_Savings_Report_${format(new Date(selectedYear, selectedMonth - 1, 1), 'yyyy_MM')}.pdf`;
    
//     console.log('Saving PDF file with name:', fileName);
//     doc.save(fileName);
//     console.log('PDF saved successfully');
    
//     return fileName;
//   } catch (error) {
//     console.error('Error in generateFullReport:', error);
//     console.log('Error type:', typeof error);
//     console.log('Error message:', error instanceof Error ? error.message : String(error));
//     console.log('Stack trace:', error instanceof Error ? error.stack : 'No stack trace available');
//     throw error;
//   }
// };

// /**
//  * Add a cover page to the PDF
//  */
// const addCoverPage = (
//   doc: jsPDF, 
//   pageWidth: number, 
//   pageHeight: number, 
//   margin: number
// ) => {
//   try {
//     let yPos = margin * 3;
    
//     // Report title
//     doc.setFontSize(24);
//     doc.setTextColor(66, 83, 122); // Navy blue color
//     doc.text('Energy Performance Report', pageWidth / 2, yPos, { align: 'center' });
//     yPos += 10;
    
//     // Current date
//     doc.setFontSize(12);
//     doc.setTextColor(100, 100, 100); // Dark gray
//     const today = new Date();
//     const dateStr = format(today, 'MMMM d, yyyy');
//     doc.text(`Generated on: ${dateStr}`, pageWidth / 2, yPos, { align: 'center' });
//     yPos += 40;
    
//     // Description of report
//     doc.setFontSize(14);
//     doc.setTextColor(0, 0, 0); // Black
//     doc.text('This report contains:', pageWidth / 2, yPos, { align: 'center' });
//     yPos += 10;
    
//     // List of report contents
//     doc.setFontSize(12);
//     const contentList = [
//       'IPMVP Baseline Model Details',
//       'Model Performance Criteria',
//       'Savings Calculation Summary',
//       'Monthly and Yearly Energy Consumption Analysis'
//     ];
    
//     contentList.forEach(item => {
//       doc.text(`• ${item}`, pageWidth / 2, yPos, { align: 'center' });
//       yPos += 7;
//     });
    
//     // Footer
//     yPos = pageHeight - margin * 3;
//     doc.setFontSize(10);
//     doc.setTextColor(100, 100, 100); // Dark gray
//     doc.text('CONFIDENTIAL - For internal use only', pageWidth / 2, yPos, { align: 'center' });
//   } catch (error) {
//     console.error('Error in addCoverPage:', error);
//     throw error;
//   }
// };

// /**
//  * Add baseline model details to the PDF
//  */
// const addBaselineModelDetails = (
//   doc: jsPDF, 
//   baselineData: DataRow[],
//   regressionResult: RegressionResult | null,
//   baselineRange: { start: string; end: string },
//   variableSelection: { dependent: string; independent: string[] },
//   excludedBaselineRowIds: Set<number>,
//   pageWidth: number,
//   pageHeight: number,
//   margin: number
// ) => {
//   let yPos = margin;
  
//   // Page title
//   doc.setFontSize(16);
//   doc.setTextColor(66, 83, 122); // Navy blue color
//   doc.text('IPMVP Baseline Model Details', pageWidth / 2, yPos, { align: 'center' });
//   yPos += 10;
  
//   // Baseline period
//   doc.setFontSize(12);
//   doc.setTextColor(0, 0, 0); // Black
//   doc.text('Baseline Period:', margin, yPos);
  
//   if (baselineRange.start && baselineRange.end) {
//     const startDate = format(new Date(baselineRange.start), 'MMMM d, yyyy');
//     const endDate = format(new Date(baselineRange.end), 'MMMM d, yyyy');
//     doc.text(`${startDate} to ${endDate}`, margin + 40, yPos);
//   } else {
//     doc.text('Not specified', margin + 40, yPos);
//   }
//   yPos += 7;
  
//   // Model variables
//   doc.text('Dependent Variable:', margin, yPos);
//   doc.text(variableSelection.dependent || 'Not selected', margin + 40, yPos);
//   yPos += 7;
  
//   doc.text('Independent Variables:', margin, yPos);
//   const independentVars = variableSelection.independent.length > 0 
//     ? variableSelection.independent.join(', ') 
//     : 'None selected';
//   doc.text(independentVars, margin + 40, yPos);
//   yPos += 10;
  
//   // Model equation section
//   if (regressionResult) {
//     doc.setFontSize(14);
//     doc.text('Regression Model', margin, yPos);
//     yPos += 7;
    
//     doc.setFontSize(12);
//     doc.text('Model Equation:', margin, yPos);
//     doc.text(regressionResult.equation, margin + 40, yPos);
//     yPos += 7;
    
//     // Model statistics 
//     doc.text('Model Statistics:', margin, yPos);
//     yPos += 7;
    
//     const statistics = [
//       { name: 'R²', value: regressionResult.statistics.r2 },
//       { name: 'Adjusted R²', value: regressionResult.statistics.adjustedR2 },
//       { name: 'CV(RMSE)', value: regressionResult.statistics.cvrmse },
//       { name: 'NMBE', value: regressionResult.statistics.nmbe },
//       { name: 'F-Statistic', value: regressionResult.statistics.fStatistic },
//       { name: 'P-Value', value: regressionResult.statistics.pValue },
//       { name: 'Observations', value: regressionResult.statistics.observations },
//     ];
    
//     // Create table for statistics
//     const statisticsRows = statistics.map(stat => [
//       stat.name, 
//       typeof stat.value === 'number' ? formatNumber(stat.value) : String(stat.value || '')
//     ]);
    
//     doc.autoTable({
//       startY: yPos,
//       head: [['Statistic', 'Value']],
//       body: statisticsRows,
//       margin: { left: margin },
//       theme: 'grid',
//       headStyles: { fillColor: [220, 230, 240] }
//     });
    
//     // @ts-ignore - Get the final y position after the table
//     yPos = doc.lastAutoTable.finalY + 10;
    
//     // Model coefficients
//     doc.text('Model Coefficients:', margin, yPos);
//     yPos += 7;
    
//     const coeffRows = Object.entries(regressionResult.coefficients).map(([variable, value]) => [
//       variable === 'intercept' ? 'Intercept' : variable,
//       formatNumber(value)
//     ]);
    
//     doc.autoTable({
//       startY: yPos,
//       head: [['Variable', 'Coefficient']],
//       body: coeffRows,
//       margin: { left: margin },
//       theme: 'grid',
//       headStyles: { fillColor: [220, 230, 240] }
//     });
    
//     // @ts-ignore - Get the final y position after the table
//     yPos = doc.lastAutoTable.finalY + 10;
    
//     // IPMVP acceptance criteria
//     doc.setFontSize(14);
//     doc.text('IPMVP Acceptance Criteria', margin, yPos);
//     yPos += 7;
    
//     doc.setFontSize(12);
//     const passR2 = (regressionResult.statistics.r2 || 0) >= 0.75;
//     const passCVRMSE = (regressionResult.statistics.cvrmse || 0) <= 0.25;
    
//     const criteriaRows = [
//       ['R² ≥ 0.75', passR2 ? 'PASS' : 'FAIL', formatNumber(regressionResult.statistics.r2 || 0)],
//       ['CV(RMSE) ≤ 25%', passCVRMSE ? 'PASS' : 'FAIL', formatNumber(regressionResult.statistics.cvrmse || 0, 2) + '%']
//     ];
    
//     doc.autoTable({
//       startY: yPos,
//       head: [['Criterion', 'Status', 'Value']],
//       body: criteriaRows,
//       margin: { left: margin },
//       theme: 'grid',
//       headStyles: { fillColor: [220, 230, 240] },
//       bodyStyles: { textColor: [0, 0, 0] },
//       styles: { cellPadding: 5 },
//       columnStyles: {
//         1: { 
//           textColor: function(row: any) {
//             const value = criteriaRows[row.row.index][1];
//             return value === 'PASS' ? [0, 128, 0] : [255, 0, 0];
//           }
//         }
//       }
//     });
//   } else {
//     doc.setFontSize(12);
//     doc.text('No regression model available.', margin, yPos);
//   }
// };

// /**
//  * Add savings report to the PDF
//  */
// const addSavingsReport = (
//   doc: jsPDF, 
//   reportingData: ReportingDataRow[],
//   reportingRange: { start: string; end: string },
//   summaryStats: SummaryStats | null,
//   selectedYear: number,
//   selectedMonth: number,
//   pageWidth: number,
//   pageHeight: number,
//   margin: number
// ) => {
//   let yPos = margin;
  
//   // Page title
//   doc.setFontSize(16);
//   doc.setTextColor(66, 83, 122); // Navy blue color
//   doc.text('Energy Savings Report', pageWidth / 2, yPos, { align: 'center' });
//   yPos += 7;
  
//   // Report period
//   doc.setFontSize(12);
//   doc.setTextColor(0, 0, 0); // Black
//   const periodText = selectedMonth === 0 
//     ? `Year: ${selectedYear}`
//     : `Period: ${format(new Date(selectedYear, selectedMonth - 1, 1), 'MMMM yyyy')}`;
//   doc.text(periodText, pageWidth / 2, yPos, { align: 'center' });
//   yPos += 10;
  
//   // Reporting period details
//   doc.text('Reporting Period:', margin, yPos);
  
//   if (reportingRange.start && reportingRange.end) {
//     const startDate = format(new Date(reportingRange.start), 'MMMM d, yyyy');
//     const endDate = format(new Date(reportingRange.end), 'MMMM d, yyyy');
//     doc.text(`${startDate} to ${endDate}`, margin + 40, yPos);
//   } else {
//     doc.text('Not specified', margin + 40, yPos);
//   }
//   yPos += 10;
  
//   // Summary stats
//   if (summaryStats) {
//     doc.setFontSize(14);
//     doc.text('Energy Savings Summary', margin, yPos);
//     yPos += 7;
    
//     doc.setFontSize(12);
//     const summaryRows = [
//       ['Total Baseline Consumption (kWh)', formatNumber(summaryStats.totalBaselineConsumption)],
//       ['Total Actual Consumption (kWh)', formatNumber(summaryStats.totalActualConsumption)],
//       ['Total Energy Savings (kWh)', formatNumber(summaryStats.totalSaving)],
//       ['Savings Percentage (%)', formatNumber(summaryStats.savingPercentage) + '%']
//     ];
    
//     doc.autoTable({
//       startY: yPos,
//       body: summaryRows,
//       margin: { left: margin },
//       theme: 'grid',
//       styles: { cellPadding: 5 },
//       columnStyles: {
//         0: { fontStyle: 'bold' }
//       }
//     });
    
//     // @ts-ignore - Get the final y position after the table
//     yPos = doc.lastAutoTable.finalY + 10;
//   }
  
//   // Monthly breakdown
//   doc.setFontSize(14);
//   doc.text('Monthly Energy Consumption', margin, yPos);
//   yPos += 7;
  
//   if (reportingData && reportingData.length > 0) {
//     // Create monthly data
//     const monthMap = new Map<string, { baseline: number; actual: number; count: number }>();
    
//     reportingData.forEach(row => {
//       if (!row.date || !row.baseline_kwh || !row.actual_kwh) return;
      
//       const date = new Date(row.date);
//       if (!isNaN(date.getTime())) {
//         const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
//         const existingData = monthMap.get(monthKey) || { baseline: 0, actual: 0, count: 0 };
//         monthMap.set(monthKey, {
//           baseline: existingData.baseline + (row.baseline_kwh || 0),
//           actual: existingData.actual + (row.actual_kwh || 0),
//           count: existingData.count + 1
//         });
//       }
//     });
    
//     // Generate all months for selected year
//     const monthlyRows = [];
//     for (let month = 1; month <= 12; month++) {
//       const monthKey = `${selectedYear}-${String(month).padStart(2, '0')}`;
//       const data = monthMap.get(monthKey) || { baseline: 0, actual: 0, count: 0 };
      
//       const date = new Date(selectedYear, month - 1, 1);
//       const monthName = format(date, 'MMM yyyy');
      
//       if (data.baseline === 0 && data.actual === 0) continue; // Skip empty months
      
//       const baseline = data.baseline;
//       const actual = data.actual;
//       const savings = baseline - actual;
//       const savingsPercentage = baseline !== 0 ? (savings / baseline) * 100 : 0;
      
//       monthlyRows.push([
//         monthName,
//         formatNumber(baseline),
//         formatNumber(actual),
//         formatNumber(savings),
//         formatNumber(savingsPercentage) + '%'
//       ]);
//     }
    
//     if (monthlyRows.length > 0) {
//       doc.autoTable({
//         startY: yPos,
//         head: [['Month', 'Baseline (kWh)', 'Actual (kWh)', 'Savings (kWh)', 'Savings (%)']],
//         body: monthlyRows,
//         margin: { left: margin },
//         theme: 'grid',
//         headStyles: { fillColor: [220, 230, 240] },
//         styles: { cellPadding: 5 }
//       });
      
//       // @ts-ignore - Get the final y position after the table
//       yPos = doc.lastAutoTable.finalY + 10;
//     } else {
//       doc.text('No monthly data available.', margin, yPos);
//       yPos += 7;
//     }
//   } else {
//     doc.text('No reporting data available.', margin, yPos);
//     yPos += 7;
//   }
// };

// /**
//  * Add signature page to the PDF
//  */
// const addSignaturePage = (
//   doc: jsPDF, 
//   pageWidth: number, 
//   pageHeight: number, 
//   margin: number
// ) => {
//   let yPos = margin * 2;
  
//   // Page title
//   doc.setFontSize(18);
//   doc.setTextColor(66, 83, 122);
//   doc.text('Customer Acceptance', pageWidth / 2, yPos, { align: 'center' });
//   yPos += 20;
  
//   // Confirmation text
//   doc.setFontSize(12);
//   doc.setTextColor(0, 0, 0);
//   doc.text('I acknowledge and confirm that the information contained in this report is accurate', pageWidth / 2, yPos, { align: 'center' });
//   doc.text('and that the energy savings reported herein have been calculated in accordance with', pageWidth / 2, yPos + 6, { align: 'center' });
//   doc.text('the agreed-upon measurement and verification plan.', pageWidth / 2, yPos + 12, { align: 'center' });
//   yPos += 40;
  
//   // Customer signature line
//   doc.line(pageWidth / 2 - 40, yPos, pageWidth / 2 + 40, yPos);
//   yPos += 5;
//   doc.text('Customer Signature', pageWidth / 2, yPos, { align: 'center' });
//   yPos += 20;
  
//   // Date line
//   doc.line(pageWidth / 2 - 40, yPos, pageWidth / 2 + 40, yPos);
//   yPos += 5;
//   doc.text('Date', pageWidth / 2, yPos, { align: 'center' });
//   yPos += 20;
  
//   // Name line
//   doc.line(pageWidth / 2 - 40, yPos, pageWidth / 2 + 40, yPos);
//   yPos += 5;
//   doc.text('Name (Print)', pageWidth / 2, yPos, { align: 'center' });
//   yPos += 20;
  
//   // Title line
//   doc.line(pageWidth / 2 - 40, yPos, pageWidth / 2 + 40, yPos);
//   yPos += 5;
//   doc.text('Title', pageWidth / 2, yPos, { align: 'center' });
// }; 