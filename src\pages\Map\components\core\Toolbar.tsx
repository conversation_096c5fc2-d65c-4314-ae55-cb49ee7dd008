import React, { useState, useRef } from 'react';
import { Plus, Save, Edit2, Settings, ZoomIn, ZoomOut, RotateCcw, Check, X, Table, Download, Upload, Loader, Image } from 'lucide-react';
import { useDraggable } from '@dnd-kit/core';
import { FloatingComponentMenu } from './FloatingComponentMenu';
import { cn } from '@/lib/utils';
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { exportMapConfig, importMapConfig, uploadMedia, saveMapConfig } from '@/services/mapService';
import JSZip from 'jszip';

interface DraggableToolbarProps {
  position: { x: number; y: number };
  isEditMode: boolean;
  onEditModeToggle: () => void;
  onSave: () => Promise<boolean>;
  onComponentAdd: (type: string) => void;
  activeId: string | null;
  dragTransform: { x: number; y: number } | null;
  onBackgroundUpload?: () => void;
  onHeaderLogoUpload?: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onReset: () => void;
  isPanningEnabled: boolean;
  onPanningToggle: () => void;
  onTableConfig?: () => void;
  mapId?: string;
  isAdmin?: boolean;
}

const SettingsMenu: React.FC<{
  onClose: () => void;
  onBackgroundUpload?: () => void;
  onHeaderLogoUpload?: () => void;
  onReset: () => void;
  isPanningEnabled: boolean;
  onPanningToggle: () => void;
  mapId?: string;
}> = ({ onClose, onBackgroundUpload, onHeaderLogoUpload, onReset, isPanningEnabled, onPanningToggle, mapId }) => {
  const multipleFilesInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');

  const handleExport = async () => {
    if (!mapId) {
      alert('Cannot export: No map ID available. Please save the map first.');
      return;
    }
    
    try {
      await exportMapConfig(mapId);
      toast({
        title: "Export Successful",
        description: "Map configuration has been exported successfully",
        variant: "default",
      });
      onClose();
    } catch (error) {
      console.error('Failed to export map configuration', error);
      alert('Failed to export map configuration. See console for details.');
    }
  };

  const handleImportMultipleFiles = () => {
    if (!mapId) {
      alert('Cannot import: No map ID available. Please save the map first.');
      return;
    }
    
    if (multipleFilesInputRef.current) {
      multipleFilesInputRef.current.click();
    }
  };

  const handleMultipleFilesChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!mapId) {
      console.error('No map ID provided for import');
      return;
    }
    
    const files = event.target.files;
    if (!files || files.length === 0) {
      toast({
        title: "Import Failed",
        description: "No files selected",
        variant: "destructive",
      });
      return;
    }

    // Show loading state immediately
    setIsLoading(true);
    setLoadingMessage(`Processing ${files.length} files...`);

    try {
      // Convert FileList to array for easier handling
      const fileArray = Array.from(files);
      
      // Check for config file first
      const configFile = fileArray.find(file => file.name === 'map-config.json');
      
      if (!configFile) {
        throw new Error("Missing map-config.json file. Please include this file in your selection.");
      }
      
      // Create a ZIP file with all selected files
      const zip = new JSZip();
      
      // Add config file
      setLoadingMessage('Adding configuration file...');
      const configContent = await configFile.arrayBuffer();
      zip.file('map-config.json', configContent);
      
      // Add all other files directly to the zip root
      const mediaFiles = fileArray.filter(file => file.name !== 'map-config.json');
      
      // Process each file with a visible status update
      for (let i = 0; i < mediaFiles.length; i++) {
        setLoadingMessage(`Adding media file ${i+1}/${mediaFiles.length}: ${mediaFiles[i].name}`);
        
        try {
          const content = await mediaFiles[i].arrayBuffer();
          zip.file(mediaFiles[i].name, content);
        } catch (error) {
          console.error(`Error processing file ${mediaFiles[i].name}:`, error);
        }
        
        // Yield to UI thread
        await new Promise(resolve => setTimeout(resolve, 0));
      }
      
      // Generate the ZIP file
      setLoadingMessage('Creating import package...');
      const zipBlob = await zip.generateAsync({ 
        type: 'blob',
        // Regular progress updates
        compression: 'DEFLATE',
        compressionOptions: { level: 6 }
      });
      
      // Check progress periodically
      let lastProgress = 0;
      while (lastProgress < 100) {
        await new Promise(resolve => setTimeout(resolve, 100));
        lastProgress += 10;
        setLoadingMessage(`Preparing import package: ${lastProgress}%`);
      }
      
      // Create a File from the ZIP blob
      const zipFile = new File([zipBlob], 'map-import.zip', { type: 'application/zip' });
      
      // Import using the ZIP file
      setLoadingMessage('Importing to server...');
      await importMapConfig(zipFile, mapId);
      
      // Reset the file input
      if (multipleFilesInputRef.current) {
        multipleFilesInputRef.current.value = '';
      }
      
      // Show success toast
      toast({
        title: "Import Successful",
        description: "Map configuration has been imported successfully",
        variant: "default",
      });
      
      // Reset loading state
      setIsLoading(false);
      
      // Close the menu
      onClose();
      
      // Reload the page to reflect changes
      window.location.reload();
      
    } catch (error) {
      console.error('Failed to import files:', error);
      
      toast({
        title: "Import Failed",
        description: error instanceof Error ? error.message : "Failed to import files",
        variant: "destructive",
      });
      
      // Reset loading state
      setIsLoading(false);
      
      // Reset the file input
      if (multipleFilesInputRef.current) {
        multipleFilesInputRef.current.value = '';
      }
    }
  };

  return (
    <div className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 bg-background backdrop-blur-sm rounded-lg border border-border shadow-card py-1 min-w-[270px]">
      <div className="text-sm font-medium text-foreground mb-1 px-3 py-1">Settings</div>
      {isLoading ? (
        <div className="border-t border-border p-4">
          <div className="flex flex-col items-center justify-center gap-2">
            <Loader className="w-5 h-5 animate-spin text-muted-foreground" />
            <p className="text-sm text-muted-foreground">{loadingMessage}</p>
          </div>
        </div>
      ) : (
        <div className="border-t border-border">
          {onBackgroundUpload && (
            <button
              className="w-full flex items-center px-3 py-2 text-sm text-muted-foreground transition-colors hover:bg-accent hover:text-background"
              onClick={() => {
                onBackgroundUpload();
                onClose();
              }}
            >
              <Upload className="mr-2 h-4 w-4" />
              Upload Background Image
            </button>
          )}
          
          {onHeaderLogoUpload && (
            <button
              className="w-full flex items-center px-3 py-2 text-sm text-muted-foreground transition-colors hover:bg-accent hover:text-background"
              onClick={() => {
                onHeaderLogoUpload();
                onClose();
              }}
            >
              <Image className="mr-2 h-4 w-4" />
              Upload Header Logo
            </button>
          )}
          <button
            onClick={handleExport}
            className="w-full flex items-center gap-2 px-3 py-2 hover:bg-accent transition-colors text-sm text-muted-foreground hover:text-background"
          >
            <Download className="w-4 h-4 opacity-80" />
            Export Map Configuration
          </button>
          <button
            onClick={handleImportMultipleFiles}
            className="w-full flex items-center gap-2 px-3 py-2 hover:bg-accent transition-colors text-sm text-muted-foreground hover:text-background"
          >
            <Upload className="w-4 h-4 opacity-80" />
            Import Files
          </button>
          <div className="px-3 py-2">
            <p className="text-xs text-muted-foreground">
              Select map-config.json and all media files
            </p>
          </div>
          {/* Hidden file input for import */}
          <input 
            type="file" 
            ref={multipleFilesInputRef}
            multiple
            style={{ display: 'none' }}
            onChange={handleMultipleFilesChange}
          />
          <button
            onClick={() => {
              onReset();
              onClose();
            }}
            className="w-full flex items-center gap-2 px-3 py-2 hover:bg-accent transition-colors text-sm text-muted-foreground hover:text-background"
          >
            <RotateCcw className="w-4 h-4 opacity-80" />
            Reset View
          </button>
          <div className="flex items-center justify-between px-3 py-2 text-sm text-muted-foreground">
            <span>Enable Map Panning</span>
            <Switch
              checked={isPanningEnabled}
              onCheckedChange={(_checked: boolean) => {
                onPanningToggle();
              }}
              onClick={(e: React.MouseEvent) => {
                e.stopPropagation();
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export const DraggableToolbar: React.FC<DraggableToolbarProps> = ({
  position,
  isEditMode,
  onEditModeToggle,
  onSave,
  onComponentAdd,
  activeId,
  dragTransform,
  onBackgroundUpload,
  onHeaderLogoUpload,
  onZoomIn,
  onZoomOut,
  onReset,
  isPanningEnabled,
  onPanningToggle,
  onTableConfig,
  mapId,
  isAdmin = false,
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const saveStatusTimeout = useRef<NodeJS.Timeout>();
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: 'toolbar',
    disabled: !isEditMode,
  });

  const isBeingDragged = activeId === 'toolbar';
  const transform = isBeingDragged && dragTransform 
    ? `translate(${dragTransform.x}px, ${dragTransform.y}px)`
    : 'translate(0, 0)';

  const handleSave = async () => {
    try {
      const success = await onSave();
      setSaveStatus(success ? 'success' : 'error');
      
      if (saveStatusTimeout.current) {
        clearTimeout(saveStatusTimeout.current);
      }
      
      saveStatusTimeout.current = setTimeout(() => {
        setSaveStatus('idle');
      }, 2000);
    } catch (error) {
      setSaveStatus('error');
      
      if (saveStatusTimeout.current) {
        clearTimeout(saveStatusTimeout.current);
      }
      
      saveStatusTimeout.current = setTimeout(() => {
        setSaveStatus('idle');
      }, 2000);
    }
  };

  // Only render the toolbar if user has admin/superuser permissions
  if (!isAdmin) return null;

  return (
    <div 
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      className="absolute z-50 select-none"
      style={{
        left: `${position.x}%`,
        top: `${position.y}%`,
        transform,
        cursor: isEditMode ? 'move' : 'default'
      }}
    >
      <div className="relative">
        {isEditMode && showMenu && (
          <FloatingComponentMenu
            onSelect={type => {
              onComponentAdd(type);
              setShowMenu(false);
            }}
            onClose={() => setShowMenu(false)}
          />
        )}
        
        {showSettings && (
          <SettingsMenu
            onClose={() => setShowSettings(false)}
            onBackgroundUpload={onBackgroundUpload}
            onHeaderLogoUpload={onHeaderLogoUpload}
            onReset={onReset}
            isPanningEnabled={isPanningEnabled}
            onPanningToggle={onPanningToggle}
            mapId={mapId}
          />
        )}
        
        <div className={cn(
          "flex items-center gap-1.5 backdrop-blur-sm rounded-full shadow-card border border-border transition-all duration-300 px-2",
          "bg-background"
        )}>
          {isEditMode && (
            <>
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="p-1.5 rounded-full hover:bg-accent transition-colors text-muted-foreground hover:text-background"
                title="Add Component"
              >
                <Plus className="h-4 w-4" />
              </button>
              {onTableConfig && (
                <button
                  onClick={onTableConfig}
                  className="p-1.5 rounded-full hover:bg-accent transition-colors text-muted-foreground hover:text-background"
                  title="Configure Tables"
                >
                  <Table className="h-4 w-4" />
                </button>
              )}
              <button
                onClick={handleSave}
                className={cn(
                  "p-1.5 rounded-full transition-colors",
                  saveStatus === 'success' && "text-success hover:text-success hover:bg-success/10",
                  saveStatus === 'error' && "text-destructive hover:text-destructive hover:bg-destructive/10",
                  saveStatus === 'idle' && "text-muted-foreground hover:text-background hover:bg-accent"
                )}
                title="Save Changes"
              >
                {saveStatus === 'success' ? <Check className="h-4 w-4" /> : 
                 saveStatus === 'error' ? <X className="h-4 w-4" /> : 
                 <Save className="h-4 w-4" />}
              </button>
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-1.5 rounded-full hover:bg-accent transition-colors text-muted-foreground hover:text-background"
                title="Settings"
              >
                <Settings className="h-4 w-4" />
              </button>
              <button
                onClick={onZoomIn}
                className="p-1.5 rounded-full hover:bg-accent transition-colors text-muted-foreground hover:text-background"
                title="Zoom In"
              >
                <ZoomIn className="h-4 w-4" />
              </button>
              <button
                onClick={onZoomOut}
                className="p-1.5 rounded-full hover:bg-accent transition-colors text-muted-foreground hover:text-background"
                title="Zoom Out"
              >
                <ZoomOut className="h-4 w-4" />
              </button>
            </>
          )}
          
          <button
            onClick={onEditModeToggle}
            className="p-1.5 rounded-full hover:bg-accent transition-colors text-muted-foreground hover:text-background"
            title={isEditMode ? "Exit Edit Mode" : "Enter Edit Mode"}
          >
            <Edit2 className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}; 