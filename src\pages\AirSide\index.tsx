import React, { useState, useEffect, useMemo } from "react";
import { useRealtime } from "@/contexts/RealtimeContext";
import { ActionProvider } from "@/contexts/ActionContext";
import { getDevicesWithZones, DeviceData } from "@/services/deviceService";
import EfficiencyCard from "./components/EfficiencyCard";
import PowerCard from "./components/PowerCard";
import CoolingLoadCard from "./components/CoolingLoadCard";
import { EnergyUsageCompareCard } from "./components/EnergyUsageCompareCard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { STATUS_MAPPING } from "./constants/statusMapping";
import { AutopilotProvider } from "@/contexts/AutopilotContext";

// Import components
import {
  AirSideMonitoring,
  VAVControlPanel,
  AHUDetailsPanel,
  AHUControlPanel,
} from "./components";

// Data display modes
type DataMode = "temp" | "damper";

const AirSidePage: React.FC = () => {
  const { getValue } = useRealtime();
  const [isLoading, setIsLoading] = useState(true);
  const [devices, setDevices] = useState<DeviceData[]>([]);
  const [activeTab, setActiveTab] = useState<"all" | "ahu" | "vav">("all");
  const [selectedAhu, setSelectedAhu] = useState<DeviceData | null>(null);
  const [controlledVavs, setControlledVavs] = useState<DeviceData[]>([]);
  const [vavStates, setVavStates] = useState<{ [key: string]: boolean }>({});
  const [showAhuDropdown, setShowAhuDropdown] = useState(false);
  const [monitoringMode, setMonitoringMode] = useState<DataMode>("temp");

  // Calculate device status counts
  const deviceStatusCounts = useMemo(() => {
    // Count all AHUs
    const allAhus = devices.filter(
      (device) =>
        device.model.toLowerCase() === "ahu" ||
        device.model.toLowerCase() === "vsd_ahu"
    );

    // Count all VAVs
    const allVavs = devices.filter(
      (device) => device.model.toLowerCase() === "vav"
    );

    // Count online AHUs (using status_write from latest_data)
    const onlineAhus = allAhus.filter(
      (ahu) => Number(getValue(ahu.deviceId, "status_read")) === 1
    );

    // Count online VAVs (using status_write from latest_data)
    const onlineVavs = allVavs.filter(
      (vav) => Number(getValue(vav.deviceId, "status_read")) === 1
    );

    return {
      ahuTotal: allAhus.length,
      ahuOnline: onlineAhus.length,
      vavTotal: allVavs.length,
      vavOnline: onlineVavs.length,
    };
  }, [devices, getValue]);

  // Fetch air-side devices
  useEffect(() => {
    const fetchDevices = async () => {
      setIsLoading(true);
      try {
        const result = await getDevicesWithZones();

        // Flatten all devices from all floors
        const allDevices: DeviceData[] = [];
        Object.values(result.groupedData).forEach((groupDevices) => {
          allDevices.push(...groupDevices);
        });

        // Filter to only keep AHU and VAV devices
        const airSideDevices = allDevices.filter(
          (device) =>
            device.model.toLowerCase() === "ahu" ||
            device.model.toLowerCase() === "vsd_ahu" ||
            device.model.toLowerCase() === "vav"
        );

        // Set default selected AHU to the first AHU found
        const firstAhu = airSideDevices.find(
          (device) =>
            device.model.toLowerCase() === "ahu" ||
            device.model.toLowerCase() === "vsd_ahu"
        );

        if (firstAhu) {
          setSelectedAhu(firstAhu);

          // Get VAVs controlled by this AHU
          const associatedVavs = airSideDevices.filter(
            (device) =>
              device.model.toLowerCase() === "vav" &&
              device.floor === firstAhu.floor
          );

          setControlledVavs(associatedVavs);

          // Initialize VAV states as on (true) by default
          const initialStates: { [key: string]: boolean } = {};
          associatedVavs.forEach((vav) => {
            initialStates[vav.deviceId] = vav.status === "normal";
          });
          setVavStates(initialStates);
        }

        setDevices(airSideDevices);
      } catch (error) {
        console.error("Error fetching air-side devices:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDevices();
  }, []);

  // Handle AHU selection
  const handleAhuSelection = (ahu: DeviceData) => {
    setSelectedAhu(ahu);
    setShowAhuDropdown(false);

    // Get VAVs controlled by this AHU
    const associatedVavs = devices.filter(
      (device) =>
        device.model.toLowerCase() === "vav" && device.floor === ahu.floor
    );

    setControlledVavs(associatedVavs);

    // Initialize VAV states
    const initialStates: { [key: string]: boolean } = {};
    associatedVavs.forEach((vav) => {
      // If we already have a state for this VAV, use it, otherwise set based on status
      initialStates[vav.deviceId] =
        vavStates[vav.deviceId] !== undefined
          ? vavStates[vav.deviceId]
          : vav.status === "normal";
    });
    setVavStates(initialStates);
  };

  // Get available AHUs for dropdown
  const availableAhus = devices.filter(
    (device) =>
      device.model.toLowerCase() === "ahu" ||
      device.model.toLowerCase() === "vsd_ahu"
  );

  // Handle toggling a VAV state - This now only updates the UI state
  // The actual API call is handled by the VAVControlPanel component
  const toggleVavState = (vavId: string) => {
    setVavStates((prev) => ({
      ...prev,
      [vavId]: !prev[vavId],
    }));

    // Also update the device status in the devices array
    setDevices((prev) =>
      prev.map((device) => {
        if (device.deviceId === vavId) {
          const newStatus = vavStates[vavId] ? "off" : "normal";
          return { ...device, status: newStatus };
        }
        return device;
      })
    );
  };

  // Handle device status change (for monitoring view updates)
  const handleDeviceStatusChange = (deviceId: string, newStatus: string) => {
    setDevices((prev) =>
      prev.map((device) => {
        if (device.deviceId === deviceId) {
          // Ensure newStatus is one of the allowed types for DeviceData.status
          const validStatus =
            newStatus === "normal" ||
            newStatus === "off" ||
            newStatus === "warning" ||
            newStatus === "alarm"
              ? newStatus
              : device.status;
          return { ...device, status: validStatus };
        }
        return device;
      })
    );
  };

  // Filter devices based on the active tab
  const filteredDevices = devices.filter((device) => {
    if (activeTab === "all") return true;
    return device.model.toLowerCase() === activeTab;
  });

  return (
    <ActionProvider>
      <div className="w-full h-full bg-slate-50">
        <div className="px-4 py-2 grid grid-cols-12 gap-[10px] h-full">
          <div className="col-span-8 h-full">
            {/* Overview metrics */}
            <div className="w-full h-[12vh]">
              {/* All metrics in a single row with custom column widths */}
              <div className="flex items-center gap-2 w-full h-full">
                {/* Air-side Efficiency Card */}
                <EfficiencyCard
                  thresholds={[0.0, 0.2, 0.25, 0.3, 0.4]}
                  deviceId="air_distribution_system"
                  title="Air-Side Efficiency"
                />
                {/* Distribution Power Card */}
                <PowerCard
                  deviceId="air_distribution_system"
                  title="Distribution Power"
                />
                {/* Cooling Load Card */}
                <CoolingLoadCard />

                {/* Energy Usage Card - 3/12 width */}
                <EnergyUsageCompareCard />
              </div>
            </div>

            {/* System monitoring */}
            <div className="mt-4 bg-[#EDEFF9] rounded-md border border-[#DBE4FF] pl-[18px] pt-[18px]">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-[#065BA9] font-semibold">
                  System Monitoring
                </h3>

                <div className="flex space-x-3">
                  {Object.entries(STATUS_MAPPING).map(
                    ([key, { color, label, temp, angle }]) => (
                      <div
                        key={key}
                        className="flex items-center text-slate-600 gap-[4px]"
                      >
                        <span
                          className="inline-block w-2 h-2 rounded-full"
                          style={{ backgroundColor: color }}
                        ></span>
                        <span className="text-[11px] text-[#292929]">
                          {label}
                        </span>
                        {monitoringMode === "temp" ? (
                          <span className="text-[11px] text-[#292929]">
                            {temp}
                          </span>
                        ) : (
                          <span className="text-[11px] text-[#292929]">
                            {angle}
                          </span>
                        )}
                      </div>
                    )
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <label className="text-[12px] text-[#111827]">Mode:</label>
                  <Select
                    value={monitoringMode}
                    onValueChange={(value) =>
                      setMonitoringMode(value as DataMode)
                    }
                  >
                    <SelectTrigger className="h-7 text-[10px] text-[#212529] bg-white border border-[#DBE4FF] rounded-[8px]">
                      <SelectValue placeholder="Select mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        value="temp"
                        className="text-[10px] text-[#212529]"
                      >
                        Temperature
                      </SelectItem>
                      <SelectItem
                        value="damper"
                        className="text-[10px] text-[#212529]"
                      >
                        Damper
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-[8px] mr-2">
                  <div className="p-2 bg-[#F9FAFF] rounded-[8px] border border-[#DBE4FF] text-[#111827] flex items-center gap-[4px]">
                    <p className="text-[12px]">Operational AHU</p>
                    <div className="px-[6px] py-0 rounded-[4px] bg-[#EDEFF9] text-[10px] text-[#212529]">
                      {deviceStatusCounts.ahuOnline}/
                      {deviceStatusCounts.ahuTotal}
                    </div>
                  </div>

                  <div className="p-2 bg-[#F9FAFF] rounded-[8px] border border-[#DBE4FF] text-[#111827] flex items-center gap-[4px]">
                    <p className="text-[12px]">Operational VAV</p>
                    <div className="px-[6px] py-0 rounded-[4px] bg-[#EDEFF9] text-[10px] text-[#212529]">
                      {deviceStatusCounts.vavOnline}/
                      {deviceStatusCounts.vavTotal}
                    </div>
                  </div>
                </div>
              </div>

              <AirSideMonitoring
                onSelectAhu={handleAhuSelection}
                selectedAhuId={selectedAhu?.deviceId}
                devices={devices}
                dataMode={monitoringMode}
              />
            </div>
          </div>

          {/* Right column - 40% width */}
          <div className="col-span-4 h-full">
            {/* AHU details and controls */}
            <div className="alto-card p-4 h-full">
              <div className="mb-4 flex justify-between items-center">
                <h3 className="text-[#065BA9] font-semibold">
                  Explore Details
                </h3>

                {availableAhus.length > 0 && (
                  <Select
                    value={selectedAhu?.deviceId || ""}
                    onValueChange={(value) => {
                      const ahu = availableAhus.find(
                        (a) => a.deviceId === value
                      );
                      if (ahu) handleAhuSelection(ahu);
                    }}
                  >
                    <SelectTrigger className="h-7 px-[12px] py-[6px] rounded-[6px] bg-[#0E7EE4] text-[12px] font-medium text-white w-[88px]">
                      <SelectValue placeholder="Select AHU">
                        {selectedAhu &&
                          selectedAhu.deviceId.toUpperCase().replace(/_/g, "-")}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {availableAhus.map((ahu) => (
                        <SelectItem
                          key={ahu.deviceId}
                          value={ahu.deviceId}
                          className="text-sm"
                        >
                          {ahu.deviceId.toUpperCase().replace(/_/g, "-")}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              {selectedAhu ? (
                <div className="overflow-y-auto h-full">
                  {/* AHU Details Panel */}
                  <AHUDetailsPanel
                    ahu={selectedAhu}
                    controlledVavs={controlledVavs}
                  />

                  {/* AHU Control Panel */}
                  <AHUControlPanel
                    ahu={selectedAhu}
                    onStatusChange={handleDeviceStatusChange}
                  />

                  {/* VAV Control Panel */}
                  <VAVControlPanel
                    controlledVavs={controlledVavs}
                    toggleVavState={toggleVavState}
                    onStatusChange={handleDeviceStatusChange}
                  />
                </div>
              ) : (
                <div className="flex justify-center items-center h-64">
                  <p className="text-slate-500">
                    Select an AHU to view details
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Equipment list - hidden for now to match Figma design */}
        <div className="hidden mt-4 bg-white rounded-md border border-slate-200">
          <div className="flex border-b border-slate-200">
            <button
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === "all"
                  ? "text-blue-700 border-b-2 border-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
              onClick={() => setActiveTab("all")}
            >
              All Equipment
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === "ahu"
                  ? "text-blue-700 border-b-2 border-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
              onClick={() => setActiveTab("ahu")}
            >
              AHUs
            </button>
            <button
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === "vav"
                  ? "text-blue-700 border-b-2 border-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
              onClick={() => setActiveTab("vav")}
            >
              VAVs
            </button>
          </div>
        </div>
      </div>
    </ActionProvider>
  );
};

export default function AirSidePageWithAutopilot() {
  return (
    <AutopilotProvider>
      <AirSidePage />
    </AutopilotProvider>
  );
}
