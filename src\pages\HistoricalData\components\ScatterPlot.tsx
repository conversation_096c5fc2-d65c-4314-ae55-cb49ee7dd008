import React from 'react';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { Card, CardContent } from "@/components/ui/card";

interface DataPoint {
  x: number;
  y: number;
  name?: string;
}

interface ScatterPlotProps {
  data: DataPoint[];
  deviceId?: string;
  xAxisDatapoint?: string;
  yAxisDatapoint?: string;
  dateRange?: [Date, Date];
}

const ScatterPlot: React.FC<ScatterPlotProps> = ({ 
  data,
  xAxisDatapoint = 'X Axis',
  yAxisDatapoint = 'Y Axis'
}) => {
  const getOption = (): EChartsOption => {
    // Calculate linear regression if we have enough data points
    if (data.length < 2) {
      return {
        title: {
          text: 'Insufficient Data',
          left: 'center',
          textStyle: {
            fontFamily: 'Inter, sans-serif',
            color: 'var(--foreground)'
          }
        }
      };
    }
    
    let sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
    const n = data.length;
    
    data.forEach(point => {
      sumX += point.x;
      sumY += point.y;
      sumXY += point.x * point.y;
      sumX2 += point.x * point.x;
    });
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    // Find min and max X values for the regression line
    const minX = Math.min(...data.map(point => point.x));
    const maxX = Math.max(...data.map(point => point.x));
    
    return {
      title: {
        text: 'Correlation Analysis',
        left: 'center',
        textStyle: {
          fontFamily: 'Inter, sans-serif',
          color: 'var(--foreground)'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          const point = data[params.dataIndex];
          return `
            <div>${point.name || 'Point'}</div>
            <div>${xAxisDatapoint}: ${point.x.toFixed(2)}</div>
            <div>${yAxisDatapoint}: ${point.y.toFixed(2)}</div>
          `;
        },
        backgroundColor: 'var(--background)',
        borderColor: 'var(--border)',
        textStyle: {
          color: 'var(--foreground)'
        }
      },
      legend: {
        data: ['Data Points', 'Regression Line'],
        bottom: 0,
        textStyle: {
          color: 'var(--foreground)'
        }
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '60px',
        top: '80px',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        name: xAxisDatapoint,
        nameLocation: 'middle',
        nameGap: 30,
        axisLine: {
          lineStyle: {
            color: 'var(--muted-foreground)'
          }
        },
        axisLabel: {
          color: 'var(--muted-foreground)'
        },
        splitLine: {
          lineStyle: {
            color: 'var(--border)'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: yAxisDatapoint,
        nameLocation: 'middle',
        nameGap: 50,
        axisLine: {
          lineStyle: {
            color: 'var(--muted-foreground)'
          }
        },
        axisLabel: {
          color: 'var(--muted-foreground)'
        },
        splitLine: {
          lineStyle: {
            color: 'var(--border)'
          }
        }
      },
      series: [
        {
          name: 'Data Points',
          type: 'scatter',
          data: data.map(point => [point.x, point.y]),
          symbolSize: 10,
          itemStyle: {
            color: 'var(--primary)'
          }
        },
        {
          name: 'Regression Line',
          type: 'line',
          data: [
            [minX, slope * minX + intercept],
            [maxX, slope * maxX + intercept]
          ],
          smooth: true,
          showSymbol: false,
          lineStyle: {
            type: 'dashed',
            color: 'var(--success)'
          }
        }
      ]
    };
  };
  
  return (
    <div className="h-full">
      <ReactECharts 
        option={getOption()} 
        style={{ height: '100%', width: '100%' }} 
        notMerge={true}
      />
    </div>
  );
};

export default ScatterPlot;