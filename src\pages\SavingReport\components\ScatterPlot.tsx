import React, { useState, useRef, useEffect } from 'react';
import * as echarts from 'echarts';

interface DataPoint {
  x: number;
  y: number;
  date?: string;
}

interface MultiVarDataPoint {
  [key: string]: number | string | undefined;
  date?: string;
}

interface ScatterPlotProps {
  data: DataPoint[];
  xLabel: string;
  yLabel: string;
  multiVarData?: MultiVarDataPoint[];
  xVariables?: string[];
  r2Values?: {[key: string]: number};
  dates?: string[];
  weekdayFilterEnabled?: boolean;
}

const ScatterPlot: React.FC<ScatterPlotProps> = ({ 
  data, 
  xLabel, 
  yLabel, 
  multiVarData = [], 
  xVariables = [],
  r2Values = {},
  dates = [],
  weekdayFilterEnabled = false
}) => {
  const [selectedVar, setSelectedVar] = useState<string | null>(null);
  
  // Chart refs
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  
  // Multiple X variables handling
  const hasMultipleVars = xVariables.length > 1 && multiVarData.length > 0;
  
  // Generate colors for different variables
  const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#0088fe'];

  // Helper function to calculate least squares fit
  const calculateLeastSquaresFit = (xValues: number[], yValues: number[]) => {
    if (xValues.length !== yValues.length || xValues.length === 0) {
      return null;
    }

    // Calculate means
    const n = xValues.length;
    const sumX = xValues.reduce((a, b) => a + b, 0);
    const sumY = yValues.reduce((a, b) => a + b, 0);
    const meanX = sumX / n;
    const meanY = sumY / n;

    // Calculate slope and intercept
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < n; i++) {
      numerator += (xValues[i] - meanX) * (yValues[i] - meanY);
      denominator += Math.pow(xValues[i] - meanX, 2);
    }
    
    if (denominator === 0) {
      return null;
    }
    
    const slope = numerator / denominator;
    const intercept = meanY - slope * meanX;
    
    return { slope, intercept };
  };

  // Handle missing values in data
  const handleMissingValues = (data: MultiVarDataPoint[], yLabel: string) => {
    // Calculate the mean of non-null values
    const validValues = data
      .map(point => typeof point[yLabel] === 'number' ? point[yLabel] as number : null)
      .filter((val): val is number => val !== null);
    
    const mean = validValues.length > 0 
      ? validValues.reduce((sum, val) => sum + val, 0) / validValues.length 
      : 0;

    // Replace missing values with the mean
    return data.map(point => ({
      ...point,
      [yLabel]: typeof point[yLabel] === 'number' ? point[yLabel] : mean
    }));
  };

  // Initialize and update chart
  useEffect(() => {
    // Initialize chart
    if (chartRef.current) {
      if (!chartInstance.current) {
        chartInstance.current = echarts.init(chartRef.current);
      }
      
      let option: echarts.EChartsOption;
      
      if (hasMultipleVars && selectedVar) {
        // Single variable view when a variable is selected
        option = createSingleVarChartOption();
      } else if (hasMultipleVars) {
        // Multi-panel view when no variable is selected
        option = createMultiVarChartOption();
      } else {
        // Original single variable view
        option = create2DChartOption();
      }
      
      chartInstance.current.setOption(option);
    }
    
    // Cleanup
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [data, multiVarData, selectedVar, hasMultipleVars]);
  
  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    // Observe parent container for size changes (especially for fullscreen mode)
    if (chartRef.current && window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(() => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      });
      
      const parentElement = chartRef.current.parentElement;
      if (parentElement) {
        resizeObserver.observe(parentElement);
      }
      
      // Also observe the chart container itself
      if (chartRef.current) {
        resizeObserver.observe(chartRef.current);
      }
      
      return () => {
        window.removeEventListener('resize', handleResize);
        if (parentElement) {
          resizeObserver.unobserve(parentElement);
        }
        if (chartRef.current) {
          resizeObserver.unobserve(chartRef.current);
        }
      };
    }
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Create echarts option for single variable view
  const createSingleVarChartOption = () => {
    const variable = selectedVar || xVariables[0];
    
    // Filter data by weekday if enabled
    const dataForProcessing = multiVarData;
    
    // Handle missing values
    const processedData = handleMissingValues(dataForProcessing, yLabel);
    
    const xValues = processedData.map(point => typeof point[variable] === 'number' ? point[variable] as number : 0);
    const yValues = processedData.map(point => typeof point[yLabel] === 'number' ? point[yLabel] as number : 0);
    const dates = processedData.map(point => point.date || '');
    
    // Create scatter data
    const scatterData = xValues.map((x, i) => [x, yValues[i], dates[i]]);
    
    // Calculate least squares fit
    const fit = calculateLeastSquaresFit(xValues, yValues);
    let lineData: number[][] = [];
    
    if (fit) {
      const minX = Math.min(...xValues);
      const maxX = Math.max(...xValues);
      const lineX = [minX, maxX];
      const lineY = lineX.map(x => fit.slope * x + fit.intercept);
      
      lineData = lineX.map((x, i) => [x, lineY[i]]);
    }
    
    // Set min value based on variable
    let minValue: number | undefined = undefined;
    if (variable.toLowerCase().includes('temperature')) minValue = 20;
    if (variable.toLowerCase().includes('humidity')) minValue = 30;
    
    const option: echarts.EChartsOption = {
      title: {
        text: `${r2Values[variable] ? `R² = ${r2Values[variable].toFixed(4)}` : ''}${weekdayFilterEnabled ? ' (Weekdays Only)' : ''}`,
        left: 'center',
        top: 0,
        textStyle: {
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          if (params.seriesIndex === 0) { // Scatter series
            return `${variable}: ${params.value[0]}<br>${yLabel}: ${params.value[1]}<br>Date: ${params.value[2]}`;
          }
          return `${variable}: ${params.value[0]}<br>${yLabel}: ${params.value[1]}`;
        }
      },
      xAxis: {
        type: 'value' as const,
        name: variable,
        nameLocation: 'middle' as const,
        nameGap: 25,
        min: minValue
      },
      yAxis: {
        type: 'value' as const,
        name: yLabel,
        nameLocation: 'middle' as const,
        nameGap: 60
      },
      series: [
        {
          type: 'scatter',
          data: scatterData,
          name: `${variable} vs ${yLabel}`,
          itemStyle: {
            color: colors[xVariables.indexOf(variable) % colors.length],
            opacity: 0.8
          },
          symbolSize: 8
        },
        {
          type: 'line',
          data: lineData,
          name: 'Least Squares Fit',
          lineStyle: {
            color: 'rgba(0, 0, 255, 0.7)',
            width: 2,
            type: 'dashed'
          },
          symbol: 'none'
        }
      ]
    };
    
    return option;
  };
  
  // Create echarts option for multi-panel view
  const createMultiVarChartOption = () => {
    const numVars = Math.min(xVariables.length, 4);
    
    // Filter data by weekday if enabled
    const dataForProcessing = multiVarData;
    
    // Handle missing values
    const processedData = handleMissingValues(dataForProcessing, yLabel);
    
    const option: echarts.EChartsOption = {
      grid: Array.from({ length: numVars }, (_, i) => ({
        left: '10%',
        right: '5%',
        top: `${10 + (i * 85 / numVars)}%`,
        height: `${80 / numVars}%`
      })),
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          const variable = xVariables[params.seriesIndex % 2 === 0 ? params.seriesIndex / 2 : Math.floor(params.seriesIndex / 2)];
          if (params.seriesIndex % 2 === 0) { // Scatter series
            return `${variable}: ${params.value[0]}<br>${yLabel}: ${params.value[1]}<br>Date: ${params.value[2] || ''}`;
          }
          return `${variable}: ${params.value[0]}<br>${yLabel}: ${params.value[1]}`;
        }
      },
      xAxis: Array.from({ length: numVars }, (_, i) => {
        // Set min value based on variable
        const variable = xVariables[i];
        let minValue: number | undefined = undefined;
        if (variable.toLowerCase().includes('temperature')) minValue = 20;
        if (variable.toLowerCase().includes('humidity')) minValue = 30;
        
        return {
          gridIndex: i,
          type: 'value' as const,
          name: variable,
          nameLocation: 'middle' as const,
          nameGap: 25,
          axisLabel: {
            showMaxLabel: true,
            showMinLabel: true
          },
          min: minValue
        };
      }),
      yAxis: Array.from({ length: numVars }, (_, i) => ({
        gridIndex: i,
        type: 'value' as const,
        name: i === 0 ? yLabel : '',
        nameLocation: 'middle' as const,
        nameGap: 60
      })),
      series: []
    };
    
    // Create series for each variable
    let allSeries: echarts.SeriesOption[] = [];
    
    for (let i = 0; i < numVars; i++) {
      const variable = xVariables[i];
      const xValues = processedData.map(point => typeof point[variable] === 'number' ? point[variable] as number : 0);
      const yValues = processedData.map(point => typeof point[yLabel] === 'number' ? point[yLabel] as number : 0);
      const dates = processedData.map(point => point.date || '');
      
      // Scatter data
      const scatterData = xValues.map((x, idx) => [x, yValues[idx], dates[idx]]);
      
      // Add scatter series
      allSeries.push({
        type: 'scatter',
        datasetIndex: i,
        xAxisIndex: i,
        yAxisIndex: i,
        data: scatterData,
        name: variable,
        itemStyle: {
          color: colors[i % colors.length],
          opacity: 0.8
        },
        symbolSize: 6
      });
      
      // Calculate and add least squares fit line
      const fit = calculateLeastSquaresFit(xValues, yValues);
      if (fit) {
        const minX = Math.min(...xValues);
        const maxX = Math.max(...xValues);
        const lineX = [minX, maxX];
        const lineY = lineX.map(x => fit.slope * x + fit.intercept);
        
        const lineData = lineX.map((x, idx) => [x, lineY[idx]]);
        
        allSeries.push({
          type: 'line',
          xAxisIndex: i,
          yAxisIndex: i,
          data: lineData,
          name: 'Least Squares Fit',
          lineStyle: {
            color: 'rgba(0, 0, 255, 0.7)',
            width: 2,
            type: 'dashed'
          },
          symbol: 'none'
        });
      }
    }
    
    option.series = allSeries;
    
    return option;
  };
  
  // Create data for basic 2D plot
  const create2DChartOption = () => {
    // Filter data by weekday if enabled
    const dataForProcessing = data.map((point, i) => ({ 
        x: point.x, 
        y: point.y, 
        date: dates[i] || undefined // Use dates from props
    }));

    // Handle missing values
    const processedData = handleMissingValues(dataForProcessing, yLabel);
    
    // Filter out any undefined values and ensure we have numbers
    const xValues = processedData.map(point => point.x).filter((x): x is number => x !== undefined);
    const yValues = processedData.map(point => point.y as number).filter((y): y is number => y !== undefined);
    const dateValues = processedData.map(point => point.date || '');
    
    // Create scatter data
    const scatterData = xValues.map((x, i) => [x, yValues[i], dateValues[i]]);
    
    // Calculate least squares fit
    const fit = calculateLeastSquaresFit(xValues, yValues);
    let lineData: number[][] = [];
    
    if (fit && xValues.length > 0) {
      const minX = Math.min(...xValues);
      const maxX = Math.max(...xValues);
      const lineX = [minX, maxX];
      const lineY = lineX.map(x => fit.slope * x + fit.intercept);
      
      lineData = lineX.map((x, i) => [x, lineY[i]]);
    }
    
    // Set min value based on variable
    let minValue: number | undefined = undefined;
    if (xLabel.toLowerCase().includes('temperature')) minValue = 20;
    if (xLabel.toLowerCase().includes('humidity')) minValue = 30;
    
    const option: echarts.EChartsOption = {
      title: {
        text: `${r2Values && r2Values[xLabel] ? `R² = ${r2Values[xLabel].toFixed(4)}` : ''}${weekdayFilterEnabled ? ' (Weekdays Only)' : ''}`,
        left: 'center',
        top: 0,
        textStyle: {
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          if (params.seriesIndex === 0) { // Scatter series
            return `${xLabel}: ${params.value[0]}<br>${yLabel}: ${params.value[1]}<br>Date: ${params.value[2] || ''}`;
          }
          return `${xLabel}: ${params.value[0]}<br>${yLabel}: ${params.value[1]}`;
        }
      },
      xAxis: {
        type: 'value' as const,
        name: xLabel,
        nameLocation: 'middle' as const,
        nameGap: 25,
        min: minValue
      },
      yAxis: {
        type: 'value' as const,
        name: yLabel,
        nameLocation: 'middle' as const,
        nameGap: 60
      },
      series: [
        {
          type: 'scatter',
          data: scatterData,
          name: 'Data Points',
          itemStyle: {
            color: '#8884d8',
            opacity: 0.8
          },
          symbolSize: 8
        },
        {
          type: 'line',
          data: lineData,
          name: 'Least Squares Fit',
          lineStyle: {
            color: 'rgba(0, 0, 255, 0.7)',
            width: 2,
            type: 'dashed'
          },
          symbol: 'none'
        }
      ]
    };
    
    return option;
  };

  return (
    <div className="bg-white h-full w-full flex flex-col">      
      {hasMultipleVars && (
        <div className="px-4 pt-4 flex justify-between items-center">
          <div className="flex flex-wrap gap-2">
            {xVariables.map((variable, index) => (
              <button
                key={variable}
                onClick={() => setSelectedVar(variable === selectedVar ? null : variable)}
                className={`px-2 py-1 text-xs border ${
                  variable === selectedVar ? 
                    'bg-blue-100 border-blue-300 text-[#065BA9]' : 
                    'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                {variable}
                {r2Values[variable] && ` (R²: ${r2Values[variable].toFixed(4)})`}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div 
        ref={chartRef} 
        className="flex-grow w-full h-[calc(100%-40px)] p-4 min-h-[250px]"
        style={{ minHeight: hasMultipleVars ? '250px' : '300px' }}
      ></div>
    </div>
  );
};

export default ScatterPlot; 