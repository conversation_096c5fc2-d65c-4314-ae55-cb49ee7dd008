import { createBrowser<PERSON>outer, Navigate, Outlet } from "react-router-dom";
import MainLayout from "./layouts/MainLayout";
import Dashboard from "./pages/Dashboard";
import Map from "./pages/Map";
import Analytics from "./pages/Analytics";
import Settings from "./pages/Settings";
import Login from "./pages/Login";
import HistoricalData from "./pages/HistoricalData";
import Maintenance from "./pages/Maintenance";
import AFDD from "./pages/AFDD";
import SavingReport from "./pages/SavingReport";
import TechnicalReport from "./pages/TechnicalReport";
import Autopilot from "./pages/Autopilot";
import AirSide from "./pages/AirSide";
import UserManual from "./pages/UserManual";
import RoomAnalytics from "./pages/RoomAnalytics";
import ProtectedRoute from "./components/ProtectedRoute";
import { useAuth } from "./contexts/AuthContext";
import { AuthProvider } from "./contexts/AuthContext";
import { DeviceProvider } from "./contexts/DeviceContext";
import { RealtimeProvider } from "./contexts/RealtimeContext";
import { AutopilotProvider } from "./contexts/AutopilotContext";
import Summary from "./pages/Summary";

// Root redirect component
const RootRedirect = () => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? <Navigate to="/app/map" replace /> : <Navigate to="/login" replace />;
};

// Auth layout component
const AuthLayout = () => {
  return (
    <div className="w-full min-w-[100vw] min-h-screen">
      <Outlet />
    </div>
  );
};

export const router = createBrowserRouter([
  {
    path: "/",
    element: <AuthProvider><RootRedirect /></AuthProvider>,
  },
  {
    path: "/",
    element: <AuthProvider><AuthLayout /></AuthProvider>,
    children: [
      {
        path: "login",
        element: <Login />,
      },
    ],
  },
  {
    path: "/app",
    element: (
      <AuthProvider>
        <ProtectedRoute>
          <DeviceProvider>
            <RealtimeProvider>
              <AutopilotProvider>
                <MainLayout />
              </AutopilotProvider>
            </RealtimeProvider>
          </DeviceProvider>
        </ProtectedRoute>
      </AuthProvider>
    ),
    children: [
      {
        path: "map",
        element: <Map />,
      },
      {
        path: "overview-dashboard",
        element: <Summary />,
      },
      {
        path: "historical-data",
        element: <HistoricalData />,
      },
      {
        path: "maintenance",
        element: <Maintenance />,
      },
      {
        path: "analytics",
        element: <Analytics />,
      },
      {
        path: "settings",
        element: <Settings />,
      },
      {
        path: "afdd",
        element: <AFDD />,
      },
      {
        path: "saving-report",
        element: <SavingReport />,
      },
      {
        path: "technical-report",
        element: <TechnicalReport />,
      },
      {
        path: "autopilot",
        element: <Autopilot />,
      },
      {
        path: "air-side",
        element: <AirSide />,
      },
      {
        path: "manual",
        element: <UserManual />,
      },
      {
        path: "room-analytics",
        element: <RoomAnalytics />,
      }
    ],
  },
]);