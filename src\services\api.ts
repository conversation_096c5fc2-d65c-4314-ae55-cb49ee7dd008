import axios from 'axios';

// Dynamically get the hostname from the browser URL
const getBaseApiUrl = () => {
  if (import.meta.env.VITE_DJANGO_API_URL) {
    return import.meta.env.VITE_DJANGO_API_URL;
  }

  // Get the current hostname (localhost, 0.0.0.0, etc.)
  const hostname = window.location.hostname;
  const protocol = window.location.protocol; // 'http:' or 'https:'
  return `${protocol}//${hostname}/api`;
};

const API_URL = getBaseApiUrl();

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle unauthorized errors (expired token, invalid token)
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
    //   Only redirect if we're not already on the login page
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }
    
    // Handle other common errors
    if (error.response?.status === 403) {
      console.error('Forbidden access:', error);
    } else if (error.response?.status === 404) {
      console.error('Resource not found:', error);
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error);
    }

    return Promise.reject(error);
  }
);
