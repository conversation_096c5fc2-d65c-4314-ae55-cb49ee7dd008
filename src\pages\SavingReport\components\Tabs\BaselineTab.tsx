import React, { useState, useMemo } from 'react';
import { useSavingDashboard } from '../../contexts/SavingDashboardContext';
import BaselineTable from '../BaselineTable';
import ModelCriteria from '../ModelCriteria';
import BaselinePlot from '../BaselinePlot';
import DataControl from '../DataControl';
import ScatterPlot from '../ScatterPlot';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import FullscreenSection from '@/components/ui/fullscreen-section';
import { Button } from '@/components/ui/button';
import { RotateCcw } from 'lucide-react';
import ExportButton from '../ExportButton';

const BaselineTab: React.FC = () => {
  const {
    baselineData,
    filteredData,
    variableSelection,
    regressionResult,
    loadSavedState,
    weekdayFilterEnabled,
    excludedBaselineRowIds
  } = useSavingDashboard();
  const [baselinePlotTab, setBaselinePlotTab] = useState<string>("time");

  // Handle reset function - loads the last saved state
  const handleReset = () => {
    loadSavedState();
  };

  // Prepare data for ScatterPlot component, adding exclusion filter
  const scatterData = useMemo(() => {
    // Start with the date/weekday filtered data from context
    const dataToFilter = filteredData;
    
    // Apply the exclusion filter
    const fullyFilteredData = dataToFilter.filter(
      row => row.id !== undefined && !excludedBaselineRowIds.has(row.id)
    );

    if (!fullyFilteredData.length || !variableSelection.dependent || !variableSelection.independent.length) {
      return { data: [], multiVarData: [], xVariables: [], yLabel: '', r2Values: {} };
    }

    const data = fullyFilteredData.map(row => ({
      x: row[variableSelection.independent[0]],
      y: row[variableSelection.dependent],
      date: row.date
    }));

    // For multi-variable support
    const multiVarData = fullyFilteredData.map(row => {
      const point: any = { date: row.date };
      variableSelection.independent.forEach(variable => {
        point[variable] = row[variable];
      });
      point[variableSelection.dependent] = row[variableSelection.dependent];
      return point;
    });

    // Calculate r-squared values for each variable (using fullyFilteredData)
    const r2Values: {[key: string]: number} = {};
    if (regressionResult && regressionResult.statistics) {
      r2Values['model'] = regressionResult.statistics.r2 || 0;
      
      variableSelection.independent.forEach(variable => {
        // Filter out rows with null/NaN, now using fullyFilteredData
        const validData = fullyFilteredData.filter(row => 
          row[variable] != null && 
          !isNaN(row[variable]) && 
          row[variableSelection.dependent] != null && 
          !isNaN(row[variableSelection.dependent])
        );

        const xValues = validData.map(row => Number(row[variable]));
        const yValues = validData.map(row => Number(row[variableSelection.dependent]));
        
        if (xValues.length === 0 || yValues.length === 0) {
          r2Values[variable] = 0;
          return;
        }

        // Calculate means
        const xMean = xValues.reduce((a, b) => a + b, 0) / xValues.length;
        const yMean = yValues.reduce((a, b) => a + b, 0) / yValues.length;
        
        // Calculate correlation coefficient
        let numerator = 0;
        let denominatorX = 0;
        let denominatorY = 0;
        
        for (let i = 0; i < xValues.length; i++) {
          const xDiff = xValues[i] - xMean;
          const yDiff = yValues[i] - yMean;
          numerator += xDiff * yDiff;
          denominatorX += xDiff * xDiff;
          denominatorY += yDiff * yDiff;
        }
        
        // Avoid division by zero
        if (denominatorX === 0 || denominatorY === 0) {
          r2Values[variable] = 0;
          return;
        }

        const correlation = numerator / Math.sqrt(denominatorX * denominatorY);
        // Round to 4 decimal places to ensure consistency
        r2Values[variable] = Math.round(correlation * correlation * 10000) / 10000;
      });
    }

    return { 
      data,
      multiVarData,
      xVariables: variableSelection.independent,
      yLabel: variableSelection.dependent,
      r2Values
    };
  }, [filteredData, variableSelection, regressionResult, excludedBaselineRowIds]);

  return (
    <div className="flex flex-col gap-3">
      <DataControl />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 h-[calc(100vh-220px)]">
        <FullscreenSection 
          title={<span className="text-[#065BA9]">1. Baseline Table</span>}
          headerContent={
            <div className="flex space-x-1">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleReset}
                className="h-6 px-2 py-0 text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Reset
              </Button>
              <ExportButton 
                size="sm"
                className="h-6 px-2 py-0 text-xs"
              />
            </div>
          }
        >
          <BaselineTable />
        </FullscreenSection>

        <FullscreenSection title={<span className="text-[#065BA9]">2. Scatter Plot</span>}>
          {scatterData.data.length > 0 ? (
            <ScatterPlot 
              data={scatterData.data}
              xLabel={variableSelection.independent[0] || ''}
              yLabel={variableSelection.dependent || ''}
              multiVarData={scatterData.multiVarData}
              xVariables={scatterData.xVariables}
              dates={scatterData.data.map(d => d.date || '')}
              r2Values={scatterData.r2Values}
              weekdayFilterEnabled={false}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">Please select variables to display the scatter plot</p>
            </div>
          )}
        </FullscreenSection>

        <FullscreenSection title={<span className="text-[#065BA9]">3. Model Criteria</span>}>
          <ModelCriteria />
        </FullscreenSection>

        <FullscreenSection 
          title={<span className="text-[#065BA9]">4. Baseline Plot</span>}
          headerContent={
            <div className="flex space-x-1">
              <button 
                className={`text-xs px-2 py-0.5 h-6 rounded-sm ${baselinePlotTab === 'time' ? 'bg-background shadow-sm' : 'bg-card hover:bg-card/80'}`}
                onClick={() => setBaselinePlotTab('time')}
              >
                Time Series
              </button>
              <button 
                className={`text-xs px-2 py-0.5 h-6 rounded-sm ${baselinePlotTab === 'scatter' ? 'bg-background shadow-sm' : 'bg-card hover:bg-card/80'}`}
                onClick={() => setBaselinePlotTab('scatter')}
              >
                Scatter
              </button>
              <button 
                className={`text-xs px-2 py-0.5 h-6 rounded-sm ${baselinePlotTab === 'bar' ? 'bg-background shadow-sm' : 'bg-card hover:bg-card/80'}`}
                onClick={() => setBaselinePlotTab('bar')}
              >
                Bar
              </button>
            </div>
          }
        >
          {(isFullscreen) => (
            <Tabs value={baselinePlotTab} onValueChange={setBaselinePlotTab} className="w-full">
              <TabsList className="hidden">
                <TabsTrigger value="time">Time Series</TabsTrigger>
                <TabsTrigger value="scatter">Scatter</TabsTrigger>
                <TabsTrigger value="bar">Bar</TabsTrigger>
              </TabsList>
              <TabsContent value="time">
                <BaselinePlot type="time" isFullscreenComponent={isFullscreen} />
              </TabsContent>
              <TabsContent value="scatter">
                <BaselinePlot type="scatter" isFullscreenComponent={isFullscreen} />
              </TabsContent>
              <TabsContent value="bar">
                <BaselinePlot type="bar" isFullscreenComponent={isFullscreen} />
              </TabsContent>
            </Tabs>
          )}
        </FullscreenSection>
      </div>
    </div>
  );
};

export default BaselineTab;