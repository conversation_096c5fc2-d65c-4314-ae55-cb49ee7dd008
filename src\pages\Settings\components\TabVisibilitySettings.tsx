import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Loader } from "@/components/ui/loader";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Check, Download, Upload, FileText } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  getTabVisibilitySettings, 
  saveTabVisibilitySettings, 
  TabVisibility, 
  defaultTabConfig,
  downloadSampleTabVisibilityFile
} from '@/services/tabVisibilityService';
import { useToast } from "@/components/ui/use-toast";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TabVisibilitySettingsProps {
  isAdmin: boolean;
}

const TabVisibilitySettings: React.FC<TabVisibilitySettingsProps> = ({ isAdmin }) => {
  const { toast } = useToast();
  const [tabConfigs, setTabConfigs] = useState<TabVisibility[]>(defaultTabConfig);
  const [activeTab, setActiveTab] = useState<string>("admin");
  const [saved, setSaved] = useState(false);
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch tab visibility settings on component mount
  useEffect(() => {
    fetchTabVisibilitySettings();
  }, []);

  // Fetch tab visibility settings from the server
  const fetchTabVisibilitySettings = async () => {
    try {
      setLoading(true);
      setError(null);
      const settings = await getTabVisibilitySettings();
      setTabConfigs(settings);
    } catch (error) {
      console.error('Failed to fetch tab visibility settings', error);
      setError('Failed to load tab visibility settings. Using default settings.');
      // Use default settings as fallback
      setTabConfigs(defaultTabConfig);
    } finally {
      setLoading(false);
    }
  };

  // Handler for tab visibility toggle
  const handleTabVisibilityChange = (tabId: string, role: string, checked: boolean) => {
    setTabConfigs(configs => 
      configs.map(config => 
        config.id === tabId 
          ? { 
              ...config, 
              visibleTo: { 
                ...config.visibleTo, 
                [role]: checked 
              } 
            } 
          : config
      )
    );
    setSaved(false);
  };

  // Save tab visibility settings to the server
  const handleSave = async () => {
    setSaving(true);
    try {
      const success = await saveTabVisibilitySettings(tabConfigs);
      if (success) {
        setSaved(true);
        toast({
          title: "Success",
          description: "Tab visibility settings saved successfully. Page will refresh in 2 seconds.",
        });
        
        // Schedule a page refresh after 2 seconds
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to save tab visibility settings.",
        });
      }
    } catch (error) {
      console.error('Failed to save tab visibility settings', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save tab visibility settings.",
      });
    } finally {
      setSaving(false);
    }
  };

  // Export tab visibility settings as a JSON file
  const handleExport = () => {
    try {
      // Create a JSON string from the current settings
      const settingsJson = JSON.stringify(tabConfigs, null, 2);
      
      // Create a blob from the JSON string
      const blob = new Blob([settingsJson], { type: 'application/json' });
      
      // Create a URL for the blob
      const url = URL.createObjectURL(blob);
      
      // Create a link element to trigger the download
      const link = document.createElement('a');
      link.href = url;
      link.download = 'tab-visibility-settings.json';
      
      // Append the link to the document, click it, and remove it
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Revoke the URL to free up memory
      URL.revokeObjectURL(url);
      
      toast({
        title: "Success",
        description: "Tab visibility settings exported successfully.",
      });
    } catch (error) {
      console.error('Failed to export tab visibility settings', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to export tab visibility settings.",
      });
    }
  };

  // Download a sample tab visibility file with default settings
  const handleDownloadSample = () => {
    downloadSampleTabVisibilityFile();
    toast({
      title: "Success",
      description: "Sample tab visibility file downloaded successfully.",
    });
  };

  // Trigger file input click
  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Import tab visibility settings from a JSON file
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    
    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string;
        const importedSettings = JSON.parse(content) as TabVisibility[];
        
        // Validate the imported settings
        if (!Array.isArray(importedSettings)) {
          throw new Error('Invalid file format. Settings must be an array.');
        }
        
        // Check if each item has the required properties
        const isValid = importedSettings.every(setting => 
          typeof setting.id === 'string' && 
          typeof setting.label === 'string' && 
          setting.visibleTo && 
          typeof setting.visibleTo.admin === 'boolean' && 
          typeof setting.visibleTo.operator === 'boolean' && 
          typeof setting.visibleTo.viewer === 'boolean'
        );
        
        if (!isValid) {
          throw new Error('Invalid settings format in file.');
        }
        
        // Set the imported settings
        setTabConfigs(importedSettings);
        
        // Save the imported settings
        await saveTabVisibilitySettings(importedSettings);
        
        toast({
          title: "Success",
          description: "Tab visibility settings imported successfully. Page will refresh.",
        });

        // Refresh the page to show the changes.
        window.location.reload();

      } catch (error) {
        console.error('Failed to import tab visibility settings', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to import tab visibility settings.",
        });
      }
    };
    
    reader.onerror = () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to read the file.",
      });
    };
    
    reader.readAsText(file);
    
    // Reset the file input so the same file can be selected again
    if (event.target) {
      event.target.value = '';
    }
  };

  // Reset to default settings
  const handleResetToDefaults = async () => {
    if (confirm('Are you sure you want to reset to default settings? This will reset all tab visibility settings to their defaults.')) {
      try {
        setSaving(true);
        
        // Clear local storage
        localStorage.removeItem('tab_visibility_settings');
        
        // Set tab configs to defaults
        setTabConfigs(defaultTabConfig);
        
        // Save the default settings
        await saveTabVisibilitySettings(defaultTabConfig);
        
        toast({
          title: "Success",
          description: "Tab visibility settings reset to defaults. Page will refresh in 2 seconds.",
        });
        
        // Schedule a page refresh after 2 seconds
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } catch (error) {
        console.error('Failed to reset tab visibility settings', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to reset tab visibility settings.",
        });
      } finally {
        setSaving(false);
      }
    }
  };

  // If user is not an admin, don't render anything
  // The parent Settings component will hide this tab entirely
  if (!isAdmin) {
    return null;
  }

  // Show loading state
  if (loading) {
    return (
      <div className="container py-6 flex flex-col items-center justify-center h-80">
        <Loader />
        <p className="text-muted-foreground">Loading tab visibility settings...</p>
      </div>
    );
  }

  return (
    <div className="container py-6">
      <div className="alto-card">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Tab Visibility Settings</CardTitle>
              <CardDescription>
                Configure which tabs are visible to different user roles
              </CardDescription>
            </div>
            {/* <div className="flex space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <FileText className="h-4 w-4 mr-2" />
                    File
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleExport}>
                    <Download className="h-4 w-4 mr-2" />
                    Export Current Settings
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleImportClick}>
                    <Upload className="h-4 w-4 mr-2" />
                    Import Settings
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDownloadSample}>
                    <FileText className="h-4 w-4 mr-2" />
                    Download Sample File
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleResetToDefaults}>
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Reset to Defaults
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <input 
                type="file" 
                ref={fileInputRef} 
                className="hidden" 
                accept=".json" 
                onChange={handleImport} 
              />
            </div> */}
          </div>
          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="admin">Admin</TabsTrigger>
              <TabsTrigger value="operator">Operator</TabsTrigger>
              <TabsTrigger value="viewer">Viewer</TabsTrigger>
            </TabsList>
            
            {["admin", "operator", "viewer"].map((role) => (
              <TabsContent key={role} value={role}>
                <div className="space-y-4 h-[50vh] overflow-y-scroll">
                  <div className="text-sm text-muted-foreground mb-2">
                    Configure which tabs are visible to {role}s
                  </div>
                  
                  <div className="grid gap-1 h-full">
                    {tabConfigs.map((tab) => (
                      <div key={tab.id} className="flex items-center justify-between">
                        <div className="flex flex-col gap-1">
                          <Label htmlFor={`${tab.id}-${role}`} className="font-medium">
                            {tab.label}
                          </Label>
                        </div>
                        <Switch
                          id={`${tab.id}-${role}`}
                          checked={tab.visibleTo[role as keyof typeof tab.visibleTo]}
                          onCheckedChange={(checked) => 
                            handleTabVisibilityChange(tab.id, role, checked)
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
          
          <div className="mt-6 flex justify-end">
            {saved && (
              <div className="mr-4 flex items-center text-sm text-green-500">
                <Check className="mr-1 h-4 w-4" />
                <span>Settings saved successfully</span>
              </div>
            )}
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <>
                  <Loader size="sm" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </div>
        </CardContent>
      </div>
    </div>
  );
};

export default TabVisibilitySettings; 