import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getDashAppUrl } from '@/utils/dash';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Loader } from "@/components/ui/loader";
import UserManagement from './components/UserManagement';
import TabVisibilitySettingsComponent from './components/TabVisibilitySettings';
import { getTabVisibilitySettings, TabVisibility } from '@/services/tabVisibilityService';
import AutomationTabContent from './components/AutomationTabContent';

const Settings: React.FC = () => {
  const { site, hasRole, userRole, isSuperuser } = useAuth();
  const [activeTab, setActiveTab] = useState<string>("ahu-automation");
  const [tabSettings, setTabSettings] = useState<TabVisibility[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Define all available tabs
  const tabs = [
    { id: "ahu-automation", label: "AHU Automation", dashApp: "air-distribution-control-setting" },
    { id: "chiller-automation", label: "Chiller Automation", dashApp: "chiller-control-setting" },
    { id: "smart-ct", label: "Smart CT", dashApp: "smart-ct-setting" },
    { id: "schedule", label: "Schedule", component: AutomationTabContent },
    { id: "pid-control", label: "PID Control", dashApp: "pid-control-setting" },
    { id: "tabs-visibility", label: "Tabs Visibility", component: TabVisibilitySettingsComponent },
    { id: "user-management", label: "User Management", component: UserManagement },
  ];
  
  // Check if user has permission to view the tabs visibility page
  const isAdmin = hasRole('admin') || isSuperuser;

  // Load tab visibility settings on component mount
  useEffect(() => {
    loadTabVisibilitySettings();
  }, []);

  // Load tab visibility settings from service
  const loadTabVisibilitySettings = async () => {
    try {
      setIsLoading(true);
      const settings = await getTabVisibilitySettings();
      setTabSettings(settings);
      setError(null);
    } catch (error) {
      console.error('Failed to load tab visibility settings:', error);
      setError('Failed to load tab visibility settings. Using default visibility.');
    } finally {
      setIsLoading(false);
    }
  };

  // Check if a tab should be visible based on user role
  const isTabVisible = (tabId: string) => {
    // Admin tabs are only visible to admins and superusers
    if ((tabId === 'tabs-visibility' || tabId === 'user-management') && !isAdmin) {
      return false;
    }
    
    // For other tabs, use the tab visibility settings if loaded
    if (!isLoading && (tabId === 'ahu-automation' || tabId === 'chiller-automation' || tabId === 'smart-ct' || tabId === 'pid-control')) {
      return true;
    }
    
    // If no settings loaded, show non-admin tabs
    return true;
  };

  // Filter tabs based on visibility
  const visibleTabs = tabs.filter(tab => isTabVisible(tab.id));

  // Set initial active tab to the first visible tab
  useEffect(() => {
    if (!isLoading && visibleTabs.length > 0 && !visibleTabs.some(tab => tab.id === activeTab)) {
      setActiveTab(visibleTabs[0].id);
    }
  }, [visibleTabs, isLoading, activeTab]);

  if (isLoading) {
    return (
      <div className="w-full h-[calc(100vh-71px)] flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader />
        </div>
      </div>
    );
  }

  if (visibleTabs.length === 0) {
    return (
      <div className="w-full h-[calc(100vh-71px)] flex items-center justify-center bg-background p-6">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Access</AlertTitle>
          <AlertDescription>
            You don't have access to any settings tabs.
            Please contact your administrator for assistance.
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className="w-full h-[calc(100vh-71px)] flex flex-col overflow-hidden bg-background">
      {error && (
        <Alert variant="destructive" className="m-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <div className="px-6 pt-4">
        <Tabs 
          value={activeTab} 
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="mb-4">
            {visibleTabs.map(tab => (
              <TabsTrigger 
                key={tab.id} 
                value={tab.id}
                className="px-6"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          
          <div className="flex-grow overflow-y-auto">
            {visibleTabs.map(tab => (
              <TabsContent 
                key={tab.id} 
                value={tab.id}
                className="w-full mt-0"
              >
                {/* Explicitly handle components needing isAdmin */}
                {tab.id === 'user-management' && tab.component && <UserManagement isAdmin={isAdmin} />}
                {tab.id === 'tabs-visibility' && tab.component && <TabVisibilitySettingsComponent isAdmin={isAdmin} />}
                
                {/* Handle Automation component (doesn't need isAdmin) */}
                {tab.id === 'chiller-automation' && tab.component && <AutomationTabContent />}
                {tab.id === 'schedule' && tab.component && <AutomationTabContent />}

                {/* Handle iframe tabs */}
                {!tab.component && tab.dashApp && (
                  <iframe 
                    src={getDashAppUrl(tab.dashApp, site?.id !== undefined ? String(site.id) : '')}
                    className="w-full h-[calc(100vh-140px)] border-none"
                    title={`Alto Dashboard - ${tab.label}`}
                    sandbox="allow-same-origin allow-scripts allow-forms"
                  />
                )}
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default Settings;