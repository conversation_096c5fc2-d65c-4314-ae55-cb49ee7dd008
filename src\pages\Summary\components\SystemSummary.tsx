import React from "react";
import MetricsSummary from "./MetricsSummary";

export interface Metric {
  label: string;
  value: number;
  decimalPlaces?: number;
  unit: string;
}

export interface SystemSummaryProps {
  title: string;
  airSideValue: number;
  plantValue: number;
  totalValue?: number;
  unit?: string;
  decimalPlaces?: number;
}

const SystemSummary: React.FC<SystemSummaryProps> = ({
  title,
  airSideValue,
  plantValue,
  totalValue = airSideValue + plantValue, // Default total is the sum of air and plant
  unit = "kW/RT",
  decimalPlaces = 2,
}) => {
  const metrics = [
    {
      label: "Total",
      value: totalValue,
      decimalPlaces,
      unit
    },
    {
      label: "Plant",
      value: plantValue,
      decimalPlaces,
      unit
    },
    {
      label: "Air Side",
      value: airSideValue,
      decimalPlaces,
      unit
    }
  ];

  return (
    <MetricsSummary 
      title={title}
      metrics={metrics}
    />
  );
};

export default SystemSummary; 