import React, { useState, useEffect, useMemo, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { fetchDailyData, DailyEnergyDataPoint } from "@/services/timescaleService";
import { getSiteId } from "@/services/authService";
import { DateTime } from "luxon";
import { useAuth } from "@/contexts/AuthContext";
import { Loader } from "@/components/ui/loader";

interface EfficiencyHeatmapProps {
  data?: (string | null)[][];
  year?: number;
}

const EfficiencyHeatmap: React.FC<EfficiencyHeatmapProps> = ({
  data,
  year = DateTime.now().year,
}) => {
  // Get timezone from auth context
  const { site } = useAuth();
  const timezone = site?.timezone || 'Asia/Bangkok';
  
  const [selectedYear, setSelectedYear] = useState(year);
  const [heatmapData, setHeatmapData] = useState<(string | null)[][]>([]);
  const [loading, setLoading] = useState(true);
  
  // Add refs for scrollable elements
  const heatmapScrollAreaRef = useRef<HTMLDivElement>(null);
  const monthsScrollAreaRef = useRef<HTMLDivElement>(null);

  // Status codes and their corresponding colors
  const statusColors = {
    normal: "bg-success", // hsl(178, 80%, 40%)
    off: "bg-muted/20", // light gray
    warning: "bg-warning", // hsl(42, 99%, 66%)
    alarm: "bg-destructive", // hsl(4, 85%, 57%)
  };

  // Days of the week labels
  const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

  // Months labels
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Create a 2D array representing the year (53 weeks x 7 days)
  const createEmptyYearData = () => {
    return Array(53).fill(0).map(() => Array(7).fill(null));
  };

  // Calculate efficiency from daily energy and daily cooling energy
  const calculateEfficiency = (energyValue: number, coolingValue: number) => {
    if (energyValue <= 0) return null;
    const efficiency = energyValue / coolingValue;
    return efficiency.toFixed(2);
  };

  // Pre-calculate the date mapping once per selected year
  const dateToGridPositionMap = useMemo(() => {
    // Create a map of ISO date strings to [week, day] positions
    const map = new Map<string, [number, number]>();
    
    // Find the first day of the calendar grid (Monday of the first week)
    const firstDay = DateTime.local(selectedYear, 1, 1).setZone(timezone);
    const firstDayOfWeek = firstDay.weekday; // 1 = Monday, 7 = Sunday in Luxon
    
    // In our grid, we want to start with Monday as day 0
    // Adjust to the Monday of the first week
    let adjustment = 0; // Days to subtract to get to the previous Monday
    if (firstDayOfWeek === 7) { // Sunday
      adjustment = 6;
    } else {
      adjustment = firstDayOfWeek - 1;
    }
    
    // Get the first Monday in our grid
    const firstMonday = firstDay.minus({ days: adjustment });
    
    // Fill the map for the entire year (53 weeks x 7 days)
    for (let weekIdx = 0; weekIdx < 53; weekIdx++) {
      for (let dayIdx = 0; dayIdx < 7; dayIdx++) {
        // Calculate the date for this cell
        const cellDate = firstMonday.plus({ days: weekIdx * 7 + dayIdx });
        
        // Format date as ISO string and store the grid position
        const isoDate = cellDate.toISODate(); // YYYY-MM-DD
        if (isoDate) { // Ensure isoDate is not null
          map.set(isoDate, [weekIdx, dayIdx]);
        }
      }
    }
    
    return map;
  }, [selectedYear]);

  // Fetch real data
  useEffect(() => {
    const fetchHeatmapData = async () => {
      try {
        setLoading(true);
        const siteId = getSiteId() || '';
        
        // Calculate date range for the selected year using Luxon with timezone
        const startDate = DateTime.local(selectedYear, 1, 1).setZone(timezone).startOf('day');
        const endDate = DateTime.local(selectedYear, 12, 31).setZone(timezone).endOf('day');
        
        // Fetch daily energy data
        const [energyResponse, coolingResponse] = await Promise.all([
          fetchDailyData({
            site_id: siteId,
            device_id: 'plant',
            datapoints: ['daily_energy'],
            start_timestamp: startDate.toString(),
            end_timestamp: endDate.toString()
          }),
          fetchDailyData({
            site_id: siteId,
            device_id: 'plant',
            datapoints: ['daily_cooling_energy'],
            start_timestamp: startDate.toString(),
            end_timestamp: endDate.toString()
          })
        ]);
        
        // Initialize empty year data
        const yearData = createEmptyYearData();
        
        if (energyResponse.success && coolingResponse.success) {
          // Create a map of daily energy data by timestamp
          const energyByTimestamp = new Map<string, number>();
          energyResponse.data.forEach((item: DailyEnergyDataPoint) => {
            energyByTimestamp.set(item.timestamp, item.value);
          });
          
          // Process daily cooling energy and calculate efficiency
          coolingResponse.data.forEach((item: DailyEnergyDataPoint) => {
            const timestamp = item.timestamp;
            // Parse timestamp properly with Luxon and timezone
            const date = DateTime.fromISO(timestamp).setZone(timezone);
            
            // Skip if not in the selected year
            if (date.year !== selectedYear) return;
            
            // Find matching energy data
            const energyValue = energyByTimestamp.get(timestamp);
            if (energyValue === undefined) return;
            
            // Calculate efficiency
            const efficiency = calculateEfficiency(energyValue, item.value);
            if (efficiency === null) return;

            // OPTIMIZED DATE MAPPING
            // Get the ISO date string for this date (YYYY-MM-DD)
            const isoDate = date.toISODate();
            
            // Skip if isoDate is null
            if (!isoDate) return;
            
            // Look up the grid position directly from our pre-calculated map
            const position = dateToGridPositionMap.get(isoDate);
            
            // If we found a match, update the heatmap data
            if (position) {
              const [weekIdx, dayIdx] = position;
              yearData[weekIdx][dayIdx] = efficiency;
            } else {
              console.warn(`No grid position found for date: ${isoDate}`);
            }
          });
        }
        
        setHeatmapData(yearData);
      } catch (error) {
        console.error("Error fetching efficiency heatmap data:", error);
        setHeatmapData(createEmptyYearData());
      } finally {
        setLoading(false);
      }
    };
    
    fetchHeatmapData();
  }, [selectedYear, dateToGridPositionMap]);

  // Helper function to get the date based on week and day index
  const getDateFromIndices = (weekIdx: number, dayIdx: number) => {
    // Find the first day of the calendar grid (Monday of the first week)
    const firstDay = DateTime.local(selectedYear, 1, 1).setZone(timezone);
    const firstDayOfWeek = firstDay.weekday; // 1 = Monday, 7 = Sunday in Luxon
    
    // In our grid, we want to start with Monday as day 0
    // Adjust to the Monday of the first week
    let adjustment = 0; // Days to subtract to get to the previous Monday
    if (firstDayOfWeek === 7) { // Sunday
      adjustment = 6;
    } else {
      adjustment = firstDayOfWeek - 1;
    }
    
    // Get the first Monday in our grid
    const firstMonday = firstDay.minus({ days: adjustment });
    
    // Calculate the date for the given week and day indices
    const targetDate = firstMonday.plus({ days: weekIdx * 7 + dayIdx });
    
    return targetDate;
  };

  // Helper function to check if a date is in the selected year
  const isDateInSelectedYear = (date: DateTime) => {
    return date.year === selectedYear;
  };

  // Format date to display in tooltip
  const formatDate = (date: DateTime) => {
    return date.toLocaleString(DateTime.DATE_FULL);
  };

  // Sync scroll between heatmap and months
  useEffect(() => {
    const heatmapViewport = heatmapScrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
    const monthsViewport = monthsScrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
    
    if (!heatmapViewport || !monthsViewport) return;
    
    const syncHeatmapToMonths = () => {
      monthsViewport.scrollLeft = heatmapViewport.scrollLeft;
    };
    
    const syncMonthsToHeatmap = () => {
      heatmapViewport.scrollLeft = monthsViewport.scrollLeft;
    };
    
    heatmapViewport.addEventListener('scroll', syncHeatmapToMonths);
    monthsViewport.addEventListener('scroll', syncMonthsToHeatmap);
    
    return () => {
      heatmapViewport.removeEventListener('scroll', syncHeatmapToMonths);
      monthsViewport.removeEventListener('scroll', syncMonthsToHeatmap);
    };
  }, []);

  return (
    <div className="alto-card">
      <CardHeader className="pb-1 pt-3 flex-row justify-between items-center">
        <CardTitle className="text-sm font-medium">
          Daily Total System Efficiency
        </CardTitle>
        <div className="flex items-center gap-2">
        <Select
          value={selectedYear.toString()}
            onValueChange={(value) => setSelectedYear(Number(value))}
        >
            <SelectTrigger className="h-7 flex items-center gap-2 min-w-[90px] text-xs">
              <span className="text-sm text-gray-500">{selectedYear}</span>
          </SelectTrigger>
            <SelectContent className="bg-white text-black">
              <SelectItem
                value={(selectedYear - 1).toString()}
                className="hover:bg-blue-50 focus:bg-blue-50 text-black"
              >
                {selectedYear - 1}
                </SelectItem>
              <SelectItem
                value={selectedYear.toString()}
                className="hover:bg-blue-50 focus:bg-blue-50 text-black"
              >
                {selectedYear}
              </SelectItem>
              <SelectItem
                value={(selectedYear + 1).toString()}
                className="hover:bg-blue-50 focus:bg-blue-50 text-black"
              >
                {selectedYear + 1}
              </SelectItem>
          </SelectContent>
        </Select>
        </div>
      </CardHeader>
      <CardContent className="pt-1 pb-2 px-3 relative">
        {/* Loading overlay */}
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-20 rounded-md">
            <Loader size="md" />
          </div>
        )}
        
        {/* Wrap the entire visualization in a container */}
        <div className="flex flex-col">
          {/* Heatmap container */}
          <ScrollArea ref={heatmapScrollAreaRef} className="h-full w-full bg-white rounded-md">
            <div className="flex items-start justify-start min-w-max">
              <div className="flex flex-col justify-between h-[114px] mr-1 sticky left-0 bg-white z-10 py-[1px]">
                {daysOfWeek.map((day, index) => (
                  <div
                    key={index}
                    className="flex-1 flex items-center justify-end pr-1 text-[10px] text-gray-500 leading-none"
                  >
                    {day}
                  </div>
                ))}
              </div>

              <div className="flex">
                {heatmapData.length > 0 ? (
                  heatmapData.map((week, weekIdx) => (
                    <div key={weekIdx} className="flex-shrink-0 flex flex-col">
                      {week.map((day, dayIdx) => {
                        const date = getDateFromIndices(weekIdx, dayIdx);
                        const formattedDate = formatDate(date);
                        const isInSelectedYear = isDateInSelectedYear(date);

                        // Skip rendering cells for dates outside the selected year
                        if (!isInSelectedYear) {
                          return <div key={dayIdx} className="h-[13px] w-[13px] m-[1px]"></div>;
                        }

                        return (
                          <TooltipProvider key={dayIdx}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div
                                  className={`h-[13px] w-[13px] m-[1px] rounded-sm ${
                                    day === null
                                      ? statusColors.off
                                      : parseFloat(day) < 0.7
                                      ? statusColors.normal
                                      : parseFloat(day) < 0.8
                                      ? statusColors.warning
                                      : statusColors.alarm
                                  } cursor-pointer transition-opacity hover:opacity-80`}
                                ></div>
                              </TooltipTrigger>
                              <TooltipContent
                                side="top"
                                className="bg-white p-2 shadow-md rounded-md border"
                              >
                                <div className="text-xs">
                                  <div className="font-medium">{formattedDate}</div>
                                  <div>{day ? `Efficiency: ${parseFloat(day).toFixed(3)}` : "No data"}</div>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        );
                      })}
                    </div>
                  ))
                ) : (
                  <div className="flex-1 flex items-center justify-center h-[114px]">
                    <p className="text-sm text-gray-500">
                      {!loading && "No efficiency data available"}
                    </p>
                  </div>
                )}
              </div>
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>

          {/* Month labels below the heatmap - will now scroll in sync */}
          <div className="pl-18 pt-[1px]">
            <ScrollArea ref={monthsScrollAreaRef}>
              <div className="flex">
                {Array.from({ length: 12 }).map((_, monthIndex) => {
                  // Calculate weeks per month (approximate)
                  const weeksInMonth = new Date(
                    selectedYear, 
                    monthIndex + 1, 
                    0
                  ).getDate() / 7;
                  
                  // Use weeksInMonth to determine width - adjusting for smaller cells
                  const widthStyle = { 
                    width: `${Math.ceil(weeksInMonth * 15)}px`, // 16px matches the width of a week in the heatmap (7 days * 14px + margins)
                    minWidth: `${Math.ceil(weeksInMonth * 15)}px`
                  };
                  
                  return (
                    <div
                      key={monthIndex}
                      className="text-[10px] text-gray-500 text-center"
                      style={widthStyle}
                    >
                      {months[monthIndex]}
                    </div>
                  );
                })}
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>

          {/* Legend */}
          <div className="flex justify-center mt-3">
            <div className="flex items-center gap-6 text-[10px]">
              <div className="flex items-center gap-1">
                <span className="inline-block w-3 h-3 rounded-sm bg-success"></span>
                <span>&lt;0.7</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="inline-block w-3 h-3 rounded-sm bg-warning"></span>
                <span>0.7-0.8</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="inline-block w-3 h-3 rounded-sm bg-destructive"></span>
                <span>&gt;0.8</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="inline-block w-3 h-3 rounded-sm bg-muted/20"></span>
                <span>No data</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </div>
  );
};

export default EfficiencyHeatmap;
