import React, { useState, useEffect } from "react";
import { DeviceData } from "@/services/deviceService";
import { Switch } from "@/components/ui/switch";
import { useRealtime } from "@/contexts/RealtimeContext";
import { sendControl } from "@/services/controlService";
import { toast } from "@/components/ui/use-toast";
import { Check, X, ChevronUp, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Loader } from "@/components/ui/loader";

interface VAVControlPanelProps {
  controlledVavs: DeviceData[];
  toggleVavState: (vavId: string) => void;
  onStatusChange?: (deviceId: string, newStatus: string) => void;
}

interface ConfirmationModalProps {
  isOpen: boolean;
  action: "start" | "stop";
  onConfirm: () => Promise<void>;
  onCancel: () => void;
}

interface EditingState {
  deviceId: string;
  field:
    | "temperature"
    | "damper"
    | "airflow"
    | "minAirflow"
    | "maxAirflow"
    | null;
  value: number;
}

interface DeviceStatusMap {
  [key: string]: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  action,
  onConfirm,
  onCancel,
}) => {
  const [status, setStatus] = useState<
    "idle" | "loading" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");

  const handleConfirmClick = async () => {
    setStatus("loading");
    try {
      await onConfirm();
      setStatus("success");
      setTimeout(() => {
        setStatus("idle");
        onCancel();
      }, 1000);
    } catch (error) {
      setStatus("error");
      setErrorMessage(
        error instanceof Error ? error.message : "Failed to send command"
      );
      setTimeout(() => {
        setStatus("idle");
      }, 2000);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed top-0 left-0 inset-0 bg-black/20 backdrop-blur-[1px] flex items-center justify-center z-50">
      <div className="bg-white rounded-md p-3 shadow-lg w-[20vw]">
        {status === "idle" && (
          <>
            <div className="text-sm font-medium text-foreground mb-2 text-center">
              Confirm {action === "start" ? "Start" : "Stop"} VAV?
            </div>
            <div className="flex gap-2 relative">
              <button
                className="flex-1 h-7 text-xs rounded bg-muted/10 hover:bg-muted/20 text-muted-foreground text-center"
                onClick={onCancel}
              >
                Cancel
              </button>
              <button
                className={cn(
                  "flex-1 h-7 text-xs rounded text-white text-center",
                  action === "start"
                    ? "bg-success hover:bg-success/90"
                    : "bg-destructive hover:bg-destructive/90"
                )}
                onClick={handleConfirmClick}
              >
                Confirm
              </button>
            </div>
          </>
        )}

        {status === "loading" && (
          <div className="flex flex-col items-center justify-center py-1">
            <Loader />
            <div className="text-xs font-medium text-foreground mt-2 text-center">
              Sending command...
            </div>
          </div>
        )}

        {status === "success" && (
          <div className="flex flex-col items-center justify-center py-1">
            <div className="text-success">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M20 6L9 17L4 12" />
              </svg>
            </div>
            <div className="text-xs font-medium text-success mt-1 text-center">
              Command sent successfully
            </div>
          </div>
        )}

        {status === "error" && (
          <div className="flex flex-col items-center justify-center py-1">
            <div className="text-destructive">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M15 9L9 15M9 9L15 15" />
              </svg>
            </div>
            <div className="text-xs font-medium text-destructive mt-1 text-center">
              {errorMessage}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const VAVControlPanel: React.FC<VAVControlPanelProps> = ({
  controlledVavs,
  toggleVavState,
  onStatusChange,
}) => {
  const { getValue } = useRealtime();
  const [deviceStatuses, setDeviceStatuses] = useState<DeviceStatusMap>({});
  const [processingDevices, setProcessingDevices] = useState<{
    [key: string]: boolean;
  }>({});
  const [editing, setEditing] = useState<EditingState | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [confirmation, setConfirmation] = useState<{
    isOpen: boolean;
    action: "start" | "stop";
    deviceId: string;
  } | null>(null);

  // Initialize device statuses based on status_write datapoint
  useEffect(() => {
    const statuses: DeviceStatusMap = {};

    controlledVavs.forEach((vav) => {
      // Get status_write value from latest_data (1 = on, 0 = off)
      const statusValue = Number(getValue(vav.deviceId, "status_read"));
      statuses[vav.deviceId] = statusValue === 1;
    });

    setDeviceStatuses(statuses);
  }, [controlledVavs, getValue]);

  // Enhanced toggle function that shows confirmation modal first
  const handleToggleDevice = (vavId: string) => {
    const currentStatus = deviceStatuses[vavId] || false;
    setConfirmation({
      isOpen: true,
      action: currentStatus ? "stop" : "start",
      deviceId: vavId,
    });
  };

  // Function to handle the actual toggle after confirmation
  const handleConfirmToggle = async () => {
    if (!confirmation) return;

    const vavId = confirmation.deviceId;
    const currentStatus = deviceStatuses[vavId] || false;
    const newStatus = !currentStatus;

    // Set this device as processing
    setProcessingDevices((prev) => ({ ...prev, [vavId]: true }));

    try {
      // Send the status_write command to turn the device on or off
      await sendControl(vavId, "status_write", newStatus ? 1 : 0);

      // Update local state
      setDeviceStatuses((prev) => ({
        ...prev,
        [vavId]: newStatus,
      }));

      // Update parent component to refresh monitoring view
      if (onStatusChange) {
        onStatusChange(vavId, newStatus ? "normal" : "off");
      }

      // Also update the parent component's state via callback
      toggleVavState(vavId);

      // Show success toast
      toast({
        title: `Device ${currentStatus ? "turned off" : "turned on"}`,
        description: `Successfully ${
          currentStatus ? "deactivated" : "activated"
        } device ${vavId}`,
        variant: "default",
      });
    } catch (error) {
      console.error(`Error toggling device ${vavId}:`, error);
      // Show error toast
      toast({
        title: "Operation failed",
        description: `Could not change device status. Please try again.`,
        variant: "destructive",
      });
      throw error; // Re-throw to be caught by the confirmation modal
    } finally {
      // Clear processing state
      setProcessingDevices((prev) => ({ ...prev, [vavId]: false }));
    }
  };

  // Render editable cell
  const renderEditableCell = (
    deviceId: string,
    field: EditingState["field"],
    value: number | undefined,
    suffix: string,
    min?: number,
    max?: number
  ) => {
    const isEditing =
      editing?.deviceId === deviceId && editing?.field === field;

    if (isEditing) {
      // Calculate step based on field type
      const step = field === "temperature" ? 0.5 : 1;

      return (
        <div className="flex items-center gap-1">
          <button
            onClick={() => adjustValue(false)}
            className="p-1 bg-slate-100 rounded hover:bg-slate-200"
            disabled={isSaving}
          >
            <ChevronDown className="h-3 w-3 text-slate-600" />
          </button>

          <input
            type="text"
            value={editing.value}
            onChange={(e) => handleInputChange(e.target.value)}
            className="w-14 py-0.5 px-1 text-center border border-blue-300 rounded text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
            step={step}
            min={min}
            max={max}
            disabled={isSaving}
          />

          <button
            onClick={() => adjustValue(true)}
            className="p-1 bg-slate-100 rounded hover:bg-slate-200"
            disabled={isSaving}
          >
            <ChevronUp className="h-3 w-3 text-slate-600" />
          </button>

          <div className="flex items-center ml-1">
            <button
              onClick={saveValue}
              className="p-1 text-green-600 hover:text-green-700 disabled:opacity-50"
              disabled={isSaving}
            >
              <Check className="h-3 w-3" />
            </button>
            <button
              onClick={cancelEditing}
              className="p-1 text-red-600 hover:text-red-700 disabled:opacity-50"
              disabled={isSaving}
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
      );
    }

    // Display value or placeholder with more intense hover effect
    return (
      <div
        className="cursor-pointer hover:bg-blue-100 py-1 px-2 rounded transition-colors duration-150"
        onClick={() =>
          value !== undefined && startEditing(deviceId, field, value)
        }
      >
        {value !== undefined ? `${value}${suffix}` : "--"}
      </div>
    );
  };

  // Start editing a field
  const startEditing = (
    deviceId: string,
    field: EditingState["field"],
    currentValue: number
  ) => {
    setEditing({
      deviceId,
      field,
      value: currentValue,
    });
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditing(null);
  };

  // Handle value change in the input field
  const handleInputChange = (value: string) => {
    if (editing) {
      setEditing({
        ...editing,
        value: Number(value),
      });
    }
  };

  // Increment or decrement the value being edited
  const adjustValue = (increment: boolean) => {
    if (editing) {
      let step = 1;

      // Use different step sizes based on the field
      if (editing.field === "temperature") {
        step = 0.5;
      } else if (editing.field === "damper") {
        step = 5;
      } else if (editing.field === "airflow") {
        step = 10;
      }

      setEditing({
        ...editing,
        value: increment ? editing.value + step : editing.value - step,
      });
    }
  };

  // Save edited value to the database
  const saveValue = async () => {
    if (!editing) return;

    setIsSaving(true);

    try {
      let datapoint = "";
      let value = editing.value;

      // Determine which datapoint to update
      switch (editing.field) {
        case "temperature":
          datapoint = "room_temperature_setpoint_write";
          // Ensure value is between 18-26°C
          value = Math.max(18, Math.min(26, value));
          break;
        case "damper":
          datapoint = "damper_position_setpoint_write";
          // Ensure value is between 0-100%
          value = Math.max(0, Math.min(100, value));
          break;
        case "airflow":
          datapoint = "air_flow_rate_setpoint_write";
          // No range check here as it depends on min/max values
          break;
        case "minAirflow":
          datapoint = "minimum_air_flow_rate_setpoint_write";
          value = Math.max(0, value);
          break;
        case "maxAirflow":
          datapoint = "maximum_air_flow_rate_setpoint_write";
          value = Math.max(value, editing.value); // Ensure max is >= current value
          break;
        default:
          return;
      }

      // Send the control command
      await sendControl(editing.deviceId, datapoint, value);

      // Show success toast
      toast({
        title: "Value updated",
        description: `Successfully updated ${datapoint.replace(
          /_/g,
          " "
        )} to ${value}`,
        variant: "default",
      });

      // Clear editing state
      setEditing(null);
    } catch (error) {
      console.error(`Error updating value:`, error);

      // Show error toast
      toast({
        title: "Failed to update value",
        description: "The update failed. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="mt-4">
      <div className="flex justify-between items-center mb-3">
        <h4 className="text-[12px] font-semibold text-[#0E7EE4]">
          VAV Control
        </h4>
      </div>

      {controlledVavs.length > 0 ? (
        <div className="border border-slate-200 rounded-md overflow-hidden">
          <table className="w-full text-xs">
            <thead>
              <tr className="bg-[#EDEFF9] text-[10px] text-[#788796]">
                <th className="py-1.5 px-2 text-center">Name</th>
                <th className="py-1.5 px-2 text-center">Status</th>
                <th className="py-1.5 px-2 text-center">Damper%</th>
                <th className="py-1.5 px-2 text-center">Temp.</th>
                <th className="py-1.5 px-2 text-center">
                  <p>Temp.</p>
                  <p>Setpoint</p>
                </th>
                <th className="py-1.5 px-2 text-center">Airflow</th>
                <th className="py-1.5 px-2 text-center">
                  <p>Airflow</p>
                  <p>Min.</p>
                </th>
                <th className="py-1.5 px-2 text-center">
                  <p>Airflow</p>
                  <p>Max.</p>
                </th>
              </tr>
            </thead>
            <tbody>
              {controlledVavs
                .sort((a, b) => a.id - b.id)
                .map((vav, index) => {
                  const isOn = deviceStatuses[vav.deviceId] || false;
                  const hasError = vav.status === "alarm";
                  const isProcessing = processingDevices[vav.deviceId] || false;

                  // Get datapoints from latest_data
                  let damperPercentage = getValue(
                    vav.deviceId,
                    "damper_position_setpoint_read"
                  );
                  let roomTemperature = getValue(
                    vav.deviceId,
                    "room_temperature"
                  );
                  let roomTemperatureSetpoint = getValue(
                    vav.deviceId,
                    "room_temperature_setpoint_read"
                  );
                  let airflowRate = getValue(
                    vav.deviceId,
                    "air_flow_rate_setpoint_read"
                  );
                  let minAirflowRate = getValue(
                    vav.deviceId,
                    "minimum_air_flow_rate_setpoint_read"
                  );
                  let maxAirflowRate = getValue(
                    vav.deviceId,
                    "maximum_air_flow_rate_setpoint_read"
                  );

                  // Round values to 1 decimal place
                  damperPercentage =
                    damperPercentage !== null
                      ? Math.round(damperPercentage * 10) / 10
                      : null;
                  roomTemperature =
                    roomTemperature !== null
                      ? Math.round(roomTemperature * 10) / 10
                      : null;
                  roomTemperatureSetpoint =
                    roomTemperatureSetpoint !== null
                      ? Math.round(roomTemperatureSetpoint * 10) / 10
                      : null;
                  airflowRate =
                    airflowRate !== null
                      ? Math.round(airflowRate * 10) / 10
                      : null;
                  minAirflowRate =
                    minAirflowRate !== null
                      ? Math.round(minAirflowRate * 10) / 10
                      : null;
                  maxAirflowRate =
                    maxAirflowRate !== null
                      ? Math.round(maxAirflowRate * 10) / 10
                      : null;

                  return (
                    <tr
                      key={vav.deviceId}
                      className={`border-t border-slate-200 hover:bg-slate-50 ${
                        index % 2 === 0 ? "bg-white" : "bg-slate-50/30"
                      }`}
                    >
                      <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                        {vav.deviceName.replace(/_/g, "-").toUpperCase()}
                      </td>
                      <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                        <div className="flex items-center">
                          <Switch
                            checked={isOn}
                            onCheckedChange={() =>
                              !isProcessing && handleToggleDevice(vav.deviceId)
                            }
                            className={`${hasError ? "!bg-rose-500" : ""} ${
                              isProcessing ? "opacity-50" : ""
                            } scale-75 origin-left`}
                            onClick={(e) => e.stopPropagation()}
                            disabled={isProcessing}
                            size="sm"
                          />
                          <span className="text-[10px]">
                            {isProcessing
                              ? "..."
                              : hasError
                              ? "Error"
                              : isOn
                              ? "On"
                              : "Off"}
                          </span>
                        </div>
                      </td>
                      <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                        {renderEditableCell(
                          vav.deviceId,
                          "damper",
                          damperPercentage,
                          "%",
                          0,
                          100
                        )}
                      </td>
                      <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                        {roomTemperature !== undefined
                          ? `${roomTemperature}°C`
                          : "--"}
                      </td>
                      <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                        {renderEditableCell(
                          vav.deviceId,
                          "temperature",
                          roomTemperatureSetpoint,
                          "°C",
                          18,
                          26
                        )}
                      </td>
                      <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                        {renderEditableCell(
                          vav.deviceId,
                          "airflow",
                          airflowRate,
                          " L/s",
                          minAirflowRate,
                          maxAirflowRate
                        )}
                      </td>
                      <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                        {renderEditableCell(
                          vav.deviceId,
                          "minAirflow",
                          minAirflowRate,
                          " L/s",
                          0,
                          maxAirflowRate
                        )}
                      </td>
                      <td className="py-1.5 px-2 text-[10px] text-[#212529]">
                        {renderEditableCell(
                          vav.deviceId,
                          "maxAirflow",
                          maxAirflowRate,
                          " L/s",
                          minAirflowRate
                        )}
                      </td>
                    </tr>
                  );
                })}
            </tbody>
          </table>
        </div>
      ) : (
        <p className="text-sm text-slate-500">
          No VAVs associated with this AHU
        </p>
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={!!confirmation}
        action={confirmation?.action || "start"}
        onConfirm={handleConfirmToggle}
        onCancel={() => setConfirmation(null)}
      />
    </div>
  );
};

export default VAVControlPanel;
