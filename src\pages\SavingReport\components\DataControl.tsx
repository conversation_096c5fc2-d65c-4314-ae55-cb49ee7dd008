import React, { useEffect, useState } from "react";
import { useSavingDashboard } from "../contexts/SavingDashboardContext";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const DataControl: React.FC = () => {
  // Baseline date range - keep the initial date setup
  const [baselineStartDate, setBaselineStartDate] = useState<Date | null>(new Date());
  const [baselineEndDate, setBaselineEndDate] = useState<Date | null>(null);
  
  // Reporting date range - keep the initial date setup
  const [reportingStartDate, setReportingStartDate] = useState<Date | null>(new Date());
  const [reportingEndDate, setReportingEndDate] = useState<Date | null>(null);
  
  // Error state for regression issues
  const [regressionError, setRegressionError] = useState<string | null>(null);
  
  const {
    baselineData,
    variableSelection,
    setVariableSelection,
    baselineRange,
    setBaselineRange,
    reportingRange,
    setReportingRange,
    regressionResult,
    runRegression,
    activeTab,
    reportingData,
    generateReport,
    weekdayFilterEnabled,
    setWeekdayFilterEnabled
  } = useSavingDashboard();

  // Convert date objects to DateRange format for DatePickerWithRange
  const baselineDateRange: DateRange = {
    from: baselineStartDate || undefined,
    to: baselineEndDate || undefined,
  };

  const reportingDateRange: DateRange = {
    from: reportingStartDate || undefined,
    to: reportingEndDate || undefined,
  };
  
  const onBaselineChange = (newRange: DateRange | undefined) => {
    const start = newRange?.from || null;
    const end = newRange?.to || null;
    
    // Update local state
    setBaselineStartDate(start);
    setBaselineEndDate(end);
    
    // Update context state
    if (start) {
      setBaselineRange({
        start: start.toISOString().split("T")[0],
        end: end ? end.toISOString().split("T")[0] : start.toISOString().split("T")[0],
      });
    }
  };
  
  const onReportingChange = (newRange: DateRange | undefined) => {
    const start = newRange?.from || null;
    const end = newRange?.to || null;
    
    // Update local state
    setReportingStartDate(start);
    setReportingEndDate(end);
    
    // Update context state
    if (start) {
      const newRangeObj = {
        start: start.toISOString().split("T")[0],
        end: end ? end.toISOString().split("T")[0] : start.toISOString().split("T")[0],
      };
      
      setReportingRange(newRangeObj);
      
      // If we're on the reporting tab, trigger a data refresh
      if (activeTab === "reporting" && reportingData.length > 0) {
        generateReport(false);
      }
    }
  };

  // Get all available columns from the first data row
  const availableColumns =
    baselineData.length > 0
      ? Object.keys(baselineData[0]).filter(
          (col) => col !== "id" && col !== "date"
        )
      : [];

  const handleDependentChange = (value: string) => {
    setVariableSelection({
      ...variableSelection,
      dependent: value,
    });
  };

  const handleIndependentChange = (value: string, checked: boolean) => {
    if (checked) {
      setVariableSelection({
        ...variableSelection,
        independent: [...variableSelection.independent, value],
      });
    } else {
      setVariableSelection({
        ...variableSelection,
        independent: variableSelection.independent.filter((v) => v !== value),
      });
    }
  };

  // Toggle weekday filter
  const toggleWeekdayFilter = () => {
    setWeekdayFilterEnabled(!weekdayFilterEnabled);
    
    // Re-run regression and regenerate report when filter is toggled
    setTimeout(() => {
      runRegression();
      if (activeTab === 'reporting' && reportingData.length > 0) {
        generateReport(false);
      }
    }, 100);
  };

  // Validate selected variables exist in the data
  useEffect(() => {
    if (baselineData.length > 0 && variableSelection.independent.length > 0) {
      // Clear any previous error
      setRegressionError(null);
      
      // Get actual columns available in the data
      const actualColumns = Object.keys(baselineData[0]).filter(
        (col) => col !== "id" && col !== "date"
      );
      
      // Check if dependent variable exists in the data
      const validDependent = variableSelection.dependent && 
        actualColumns.includes(variableSelection.dependent) ? 
        variableSelection.dependent : 
        "";
      
      // Filter independent variables to only those that exist in the data
      const validIndependent = variableSelection.independent.filter(
        (variable) => actualColumns.includes(variable)
      );
      
      // Update variable selection if needed
      if (
        validDependent !== variableSelection.dependent ||
        validIndependent.length !== variableSelection.independent.length
      ) {
        setVariableSelection({
          dependent: validDependent,
          independent: validIndependent,
        });
        
        if (variableSelection.dependent && !validDependent) {
          setRegressionError(`The selected dependent variable "${variableSelection.dependent}" does not exist in the imported data.`);
        }
        
        const invalidIndependent = variableSelection.independent.filter(
          v => !actualColumns.includes(v)
        );
        if (invalidIndependent.length > 0) {
          setRegressionError(`The following independent variables do not exist in the imported data: ${invalidIndependent.join(', ')}`);
        }
        
        return; // Will trigger another useEffect cycle with valid variables
      }
      
      // Only run regression with valid variables
      try {
        runRegression();
      } catch (error) {
        if (error instanceof Error) {
          setRegressionError(error.message);
        } else {
          setRegressionError("An unknown error occurred during regression");
        }
      }
    }
  }, [variableSelection, baselineData, runRegression, setVariableSelection]);

  // Regenerate reporting data when regression model changes
  useEffect(() => {
    if (regressionResult && reportingData.length > 0 && activeTab === 'reporting') {
      // Only regenerate the report if we're on the reporting tab
      // This prevents issues when trying to switch back to baseline tab
      const timer = setTimeout(() => {
        generateReport(false);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [regressionResult, reportingData.length, activeTab, generateReport]);

  // Update DatePicker state when context state changes
  useEffect(() => {
    if (baselineRange.start) {
      setBaselineStartDate(new Date(baselineRange.start));
    }
    if (baselineRange.end) {
      setBaselineEndDate(new Date(baselineRange.end));
    }
  }, [baselineRange]);

  useEffect(() => {
    if (reportingRange.start) {
      setReportingStartDate(new Date(reportingRange.start));
    }
    if (reportingRange.end) {
      setReportingEndDate(new Date(reportingRange.end));
    }
  }, [reportingRange]);

  return (
    <>
      <div className="border rounded-md p-1 bg-gray-100 text-gray-800 flex items-center gap-1 shadow-sm text-xs">
        <div className="flex items-center flex-1 z-[10] gap-[0px] flex-col">
          <div className="text-[10px] font-medium whitespace-nowrap self-start">Baseline Period:</div>
          <DatePickerWithRange 
            date={baselineDateRange}
            onDateChange={onBaselineChange}
            className="w-full"
          />
        </div>

        <div className="flex items-center flex-1 gap-[0px] flex-col">
          <div className="text-[10px] font-medium whitespace-nowrap self-start">Reporting Period:</div>
          <DatePickerWithRange 
            date={reportingDateRange}
            onDateChange={onReportingChange}
            className="w-full"
          />
        </div>

        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant={weekdayFilterEnabled ? "default" : "outline"} 
                  size="sm" 
                  className="h-7 px-2 text-xs flex items-center gap-1"
                  onClick={toggleWeekdayFilter}
                >
                  <Calendar className="h-3.5 w-3.5" />
                  {weekdayFilterEnabled ? "Weekdays Only" : "All Days"}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">
                  {weekdayFilterEnabled 
                    ? "Currently filtering to show only weekdays (Mon-Fri). Click to show all days." 
                    : "Click to filter data to weekdays only (Mon-Fri)"}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        
        <div className="flex items-center flex-1 gap-[0px] flex-col">
          <div className="text-[10px] font-medium whitespace-nowrap self-start">Dependent Variable (Y):</div>
          <Select
            value={variableSelection.dependent}
            onValueChange={handleDependentChange}
          >
            <SelectTrigger className="h-7 text-xs bg-white border-gray-200">
              <SelectValue placeholder="Select dependent variable (y)" />
            </SelectTrigger>
            <SelectContent>
              {availableColumns.map((column) => (
                <SelectItem key={column} value={column} className="text-xs">
                  {column}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center w-[700px] gap-[0px] flex-col">
          <div className="text-[10px] font-medium whitespace-nowrap self-start">Independent Variables (X):</div>
          <div className="grid grid-cols-5 gap-1 flex-1 overflow-y-auto px-2 py-0.5 bg-white rounded border border-gray-200 w-full">
            {availableColumns.map((column) => (
              <div key={column} className="flex items-center">
                <input
                  type="checkbox"
                  id={`var-${column}`}
                  checked={variableSelection.independent.includes(column)}
                  onChange={(e) =>
                    handleIndependentChange(column, e.target.checked)
                  }
                  disabled={column === variableSelection.dependent}
                  className="h-2.5 w-2.5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label
                  htmlFor={`var-${column}`}
                  className="ml-1 block text-[10px] text-gray-700 truncate"
                >
                  {column}
                </label>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {regressionError && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-xs">
            {regressionError}
          </AlertDescription>
        </Alert>
      )}
    </>
  );
};

export default DataControl;
