// mock-hotel-config.ts

export type FCUStatus = {
  on_off: 0 | 1;
  operation_mode: 1 | 2 | 3 | 4;
  airflow_rate: 1 | 2 | 3 | 4 | 5 | 6 | 7;
  measured_room_temperature: number;
  set_room_temperature: number;
  communication_status: 0 | 1;
};

export type IAQStatus = {
  pm25: number;
  noise: number;
  illuminance: number;
  temperature: number;
  humidity: number;
  co2: number;
  online_status: boolean;
};

export type PresenceSensor = {
  presence_state: boolean;
  sensitivity: number; // scale from 1–10
  status: 'active' | 'inactive' | 'error';
};

export type OccupancyStatus = 0 | 1 | 2 | 3;

export interface RoomDeviceStatus {
  fcu: FCUStatus;
  iaq: IAQStatus;
  presence_sensor: PresenceSensor;
  occupancy_status: OccupancyStatus;
}

export interface Room {
  roomId: string;
  roomType: 'Standard' | 'Deluxe' | 'Suite' | 'Family' | 'Executive';
  devices: RoomDeviceStatus;
}

export const BUILDING_CONFIG = {
  name: 'Mock Smart Hotel',
  location: 'Bangkok, Thailand',
  floors: [
    {
      level: 1,
      rooms: [
        {
          roomId: '101',
          roomType: 'Standard',
          devices: {
            fcu: {
              on_off: 1,
              operation_mode: 1,
              airflow_rate: 3,
              measured_room_temperature: 24.5,
              set_room_temperature: 23.0,
              communication_status: 1,
            },
            iaq: {
              pm25: 12,
              noise: 35,
              illuminance: 450,
              temperature: 24.5,
              humidity: 55,
              co2: 420,
              online_status: true,
            },
            presence_sensor: {
              presence_state: true,
              sensitivity: 7,
              status: 'active',
            },
            occupancy_status: 2,
          },
        },
        {
          roomId: '102',
          roomType: 'Deluxe',
          devices: {
            fcu: {
              on_off: 0,
              operation_mode: 3,
              airflow_rate: 1,
              measured_room_temperature: 27.0,
              set_room_temperature: 24.0,
              communication_status: 1,
            },
            iaq: {
              pm25: 20,
              noise: 40,
              illuminance: 380,
              temperature: 27.0,
              humidity: 60,
              co2: 500,
              online_status: true,
            },
            presence_sensor: {
              presence_state: false,
              sensitivity: 5,
              status: 'inactive',
            },
            occupancy_status: 0,
          },
        },
        {
          roomId: '103',
          roomType: 'Suite',
          devices: {
            fcu: {
              on_off: 1,
              operation_mode: 2,
              airflow_rate: 4,
              measured_room_temperature: 22.0,
              set_room_temperature: 22.0,
              communication_status: 1,
            },
            iaq: {
              pm25: 15,
              noise: 30,
              illuminance: 500,
              temperature: 22.0,
              humidity: 53,
              co2: 410,
              online_status: true,
            },
            presence_sensor: {
              presence_state: true,
              sensitivity: 9,
              status: 'active',
            },
            occupancy_status: 3,
          },
        },
        {
          roomId: '104',
          roomType: 'Family',
          devices: {
            fcu: {
              on_off: 1,
              operation_mode: 1,
              airflow_rate: 5,
              measured_room_temperature: 23.5,
              set_room_temperature: 23.0,
              communication_status: 1,
            },
            iaq: {
              pm25: 18,
              noise: 38,
              illuminance: 460,
              temperature: 23.5,
              humidity: 57,
              co2: 450,
              online_status: true,
            },
            presence_sensor: {
              presence_state: true,
              sensitivity: 8,
              status: 'active',
            },
            occupancy_status: 2,
          },
        },
      ],
    },
    {
      level: 2,
      rooms: [
        {
          roomId: '201',
          roomType: 'Executive',
          devices: {
            fcu: {
              on_off: 1,
              operation_mode: 4,
              airflow_rate: 5,
              measured_room_temperature: 21.0,
              set_room_temperature: 22.0,
              communication_status: 1,
            },
            iaq: {
              pm25: 10,
              noise: 33,
              illuminance: 480,
              temperature: 21.0,
              humidity: 50,
              co2: 390,
              online_status: true,
            },
            presence_sensor: {
              presence_state: true,
              sensitivity: 8,
              status: 'active',
            },
            occupancy_status: 1,
          },
        },
        {
          roomId: '202',
          roomType: 'Standard',
          devices: {
            fcu: {
              on_off: 0,
              operation_mode: 3,
              airflow_rate: 2,
              measured_room_temperature: 26.0,
              set_room_temperature: 25.0,
              communication_status: 1,
            },
            iaq: {
              pm25: 22,
              noise: 42,
              illuminance: 400,
              temperature: 26.0,
              humidity: 61,
              co2: 520,
              online_status: true,
            },
            presence_sensor: {
              presence_state: false,
              sensitivity: 6,
              status: 'inactive',
            },
            occupancy_status: 0,
          },
        },
        {
          roomId: '203',
          roomType: 'Deluxe',
          devices: {
            fcu: {
              on_off: 1,
              operation_mode: 1,
              airflow_rate: 3,
              measured_room_temperature: 23.0,
              set_room_temperature: 22.5,
              communication_status: 1,
            },
            iaq: {
              pm25: 16,
              noise: 34,
              illuminance: 470,
              temperature: 23.0,
              humidity: 54,
              co2: 410,
              online_status: true,
            },
            presence_sensor: {
              presence_state: true,
              sensitivity: 7,
              status: 'active',
            },
            occupancy_status: 2,
          },
        },
        {
          roomId: '204',
          roomType: 'Suite',
          devices: {
            fcu: {
              on_off: 1,
              operation_mode: 2,
              airflow_rate: 4,
              measured_room_temperature: 22.5,
              set_room_temperature: 22.0,
              communication_status: 1,
            },
            iaq: {
              pm25: 14,
              noise: 31,
              illuminance: 490,
              temperature: 22.5,
              humidity: 52,
              co2: 400,
              online_status: true,
            },
            presence_sensor: {
              presence_state: true,
              sensitivity: 9,
              status: 'active',
            },
            occupancy_status: 3,
          },
        },
      ],
    },
  ],
};
