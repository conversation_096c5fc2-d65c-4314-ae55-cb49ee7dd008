import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Info } from 'lucide-react';
import { RegressionResult } from '../types';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';

interface UncertaintyCalculatorProps {
  baselineUncertainty: number; // in decimal (e.g., 0.0795 for 7.95%)
  onUncertaintyChange: (meterUncertainty: number, confidenceLevel: number) => void;
  regressionResult?: RegressionResult | null;
  totalBaselineConsumption: number;
  reportingDays: number;
}

const confidenceLevels = [
  { label: '68%', value: 1.0, tValue: 1.0 },
  { label: '90%', value: 1.645, tValue: 1.645 },
  { label: '95%', value: 1.96, tValue: 1.96 },
  { label: '99%', value: 2.576, tValue: 2.576 }
];

const UncertaintyCalculator: React.FC<UncertaintyCalculatorProps> = ({
  baselineUncertainty,
  onUncertaintyChange,
  regressionResult,
  totalBaselineConsumption,
  reportingDays
}) => {
  const [meterUncertainty, setMeterUncertainty] = useState<number>(1.25);
  const [selectedConfidenceLevel, setSelectedConfidenceLevel] = useState<number>(1.96); // Default to 95%
  const [showMethodDetails, setShowMethodDetails] = useState<boolean>(false);
  const { filteredData, variableSelection } = useSavingDashboard();
  
  // Get baseline model Standard Error from regression result
  const modelSE = regressionResult?.statistics?.rse || 0;
  
  // Calculate the mean daily consumption (dependent variable) from baseline data
  const meanDailyConsumption = filteredData.length > 0 
    ? filteredData.reduce((sum, row) => sum + row[variableSelection.dependent], 0) / filteredData.length 
    : 0;
  
  // Calculate baseline uncertainty as Standard Error / Mean of dependent variable
  // This is the coefficient of variation (CV)
  const calculatedBaselineUncertainty = meanDailyConsumption ? (modelSE / meanDailyConsumption) : baselineUncertainty;
  
  // Convert percentages to decimals for calculations
  const meterUncertaintyDecimal = meterUncertainty / 100;
  
  // Calculate combined uncertainty (square root of sum of squares)
  const combinedUncertainty = Math.sqrt(Math.pow(calculatedBaselineUncertainty, 2) + Math.pow(meterUncertaintyDecimal, 2));
  const combinedUncertaintyPercent = combinedUncertainty * 100;
  
  // Calculate daily values
  const avgDailyBaseline = totalBaselineConsumption / reportingDays;
  const errorKwhPerDay = avgDailyBaseline * combinedUncertainty;
  
  // Apply confidence level
  const dailyUncertainty = errorKwhPerDay * selectedConfidenceLevel;
  
  // Total uncertainty for the reporting period
  const totalUncertaintyKwh = dailyUncertainty * Math.sqrt(reportingDays);
  
  useEffect(() => {
    onUncertaintyChange(meterUncertaintyDecimal, selectedConfidenceLevel);
  }, [meterUncertainty, selectedConfidenceLevel, onUncertaintyChange]);
  
  const handleMeterUncertaintyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value > 0) {
      setMeterUncertainty(value);
    }
  };
  
  const handleConfidenceLevelChange = (tValue: number) => {
    setSelectedConfidenceLevel(tValue);
  };
  
  return (
    <div className="space-y-3">
      <div className="grid grid-cols-2 gap-3">
        <div className="col-span-2 md:col-span-1">
          <label className="text-sm font-medium mb-1 block">Meter Uncertainty (%)</label>
          <input
            type="number"
            value={meterUncertainty}
            onChange={handleMeterUncertaintyChange}
            className="p-1.5 text-sm border rounded w-full"
            step="0.01"
            min="0.01"
            placeholder="Enter meter uncertainty"
          />
          <p className="text-xs text-gray-500 mt-0.5">
            Enter your metering equipment uncertainty
          </p>
        </div>
        
        <div className="col-span-2 md:col-span-1">
          <label className="text-sm font-medium mb-1 block">Confidence Level</label>
          <div className="flex space-x-1">
            {confidenceLevels.map((level) => (
              <Button
                key={level.label}
                onClick={() => handleConfidenceLevelChange(level.tValue)}
                variant={selectedConfidenceLevel === level.tValue ? "default" : "outline"}
                size="sm"
                className={`${selectedConfidenceLevel === level.tValue ? "bg-blue-500 text-white" : ""} text-xs px-2 py-1`}
              >
                {level.label}
              </Button>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-0.5">
            Select desired confidence level
          </p>
        </div>
      </div>
      
      <div className="grid grid-cols-3 gap-2 mt-2">
        <div className="p-2 border rounded-md bg-gray-50">
          <h4 className="text-xs font-medium text-gray-500">Baseline Model Uncertainty</h4>
          <p className="text-base font-semibold">{(calculatedBaselineUncertainty * 100).toFixed(2)}%</p>
          <p className="text-xs text-gray-500">{(avgDailyBaseline * calculatedBaselineUncertainty).toFixed(2)} kWh (Sm)</p>
        </div>
        
        <div className="p-2 border rounded-md bg-gray-50">
          <h4 className="text-xs font-medium text-gray-500">Meter Uncertainty</h4>
          <p className="text-base font-semibold">{meterUncertainty.toFixed(2)}%</p>
          <p className="text-xs text-gray-500">Relative error (Smet)</p>
        </div>
        
        <div className="p-2 border rounded-md bg-gray-50">
          <h4 className="text-xs font-medium text-gray-500">Combined Uncertainty</h4>
          <p className="text-base font-semibold">{combinedUncertaintyPercent.toFixed(2)}%</p>
          <p className="text-xs text-gray-500">at {confidenceLevels.find(l => l.tValue === selectedConfidenceLevel)?.label} confidence</p>
        </div>
      </div>
      
      <div className="border rounded-md mt-2 overflow-hidden shadow-sm">
        <button
          onClick={() => setShowMethodDetails(!showMethodDetails)}
          className="w-full flex justify-between items-center px-3 py-2 text-left bg-gray-50 hover:bg-gray-100 transition-colors"
        >
          <span className="text-sm font-medium">IPMVP Uncertainty Calculation Method</span>
          {showMethodDetails ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
        </button>
        
        {showMethodDetails && (
          <div className="p-3 border-t text-xs">
            <p className="italic mb-3 text-gray-600">The IPMVP uncertainty calculation follows these steps:</p>
            
            <div className="border-l-3 border-blue-400 pl-3 space-y-4">
              <div>
                <h4 className="font-medium text-xs mb-1">Step 1: Calculate baseline model uncertainty (CV)</h4>
                <div className="bg-gray-50 p-2 rounded text-xs font-mono">
                  Baseline Uncertainty = Model SE / Mean Daily Consumption<br />
                  = {modelSE.toFixed(1)} / {meanDailyConsumption.toFixed(1)}<br />
                  = {(calculatedBaselineUncertainty * 100).toFixed(2)}%
                </div>
                <p className="text-xs text-gray-600 mt-0.5">
                  This is the coefficient of variation (CV), representing the statistical error in the baseline model
                </p>
              </div>

              <div>
                <h4 className="font-medium text-xs mb-1">Step 2: Combine baseline and meter uncertainties</h4>
                <div className="bg-gray-50 p-2 rounded text-xs font-mono">
                  SE = √(Sm² + Smet²) = √({(calculatedBaselineUncertainty * 100).toFixed(2)}² + {meterUncertainty.toFixed(2)}²) = {combinedUncertaintyPercent.toFixed(2)}%
                </div>
                <p className="text-xs text-gray-600 mt-0.5">
                  Where Sm is the baseline model uncertainty and Smet is the meter uncertainty
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-xs mb-1">Step 3: Calculate daily error in kWh</h4>
                <div className="bg-gray-50 p-2 rounded text-xs font-mono">
                  Avg_Daily_Baseline = {avgDailyBaseline.toFixed(2)} kWh/day
                </div>
                <div className="bg-gray-50 p-2 rounded text-xs font-mono mt-1">
                  Error_kWh_Per_Day = Avg_Daily_Baseline × Combined Uncertainty = {avgDailyBaseline.toFixed(2)} × {combinedUncertainty.toFixed(4)} = {errorKwhPerDay.toFixed(2)} kWh/day
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-xs mb-1">Step 4: Apply confidence level factor</h4>
                <div className="bg-gray-50 p-2 rounded text-xs font-mono">
                  Daily_Uncertainty = Error_kWh_Per_Day × t-value = {errorKwhPerDay.toFixed(2)} × {selectedConfidenceLevel} = {dailyUncertainty.toFixed(2)} kWh/day
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-xs mb-1">Step 5: Calculate total uncertainty for reporting period</h4>
                <div className="bg-gray-50 p-2 rounded text-xs font-mono">
                  Total_Uncertainty_kWh = Daily_Uncertainty × √Number_of_Days = {dailyUncertainty.toFixed(2)} × √{reportingDays} = {totalUncertaintyKwh.toFixed(2)} kWh
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex items-start space-x-2 p-2 mt-2 bg-blue-50 border border-blue-100 rounded-md">
        <Info size={12} className="text-blue-500 flex-shrink-0 mt-0.5" />
        <p className="text-xs text-[#065BA9]">
          <span className="font-medium">Note:</span> 
          Baseline uncertainty is calculated as Model Standard Error ({modelSE.toFixed(1)}) divided by the 
          mean daily consumption ({meanDailyConsumption.toFixed(1)} kWh), following IPMVP guidelines.
          This coefficient of variation represents the relative uncertainty in the baseline model.
        </p>
      </div>
    </div>
  );
};

export default UncertaintyCalculator; 