import React, { useState, useEffect } from 'react';
import { X, Co<PERSON>, Trash2, MoveUp, MoveDown, ArrowUpToLine, ArrowDownToLine, Plus } from 'lucide-react';
import { ComponentConfig, TextBoxConfig, RealTimeValueConfig, MachineStatusConfig, GifConfig, DatapointDisplayConfig, CoolingLoadConfig, ControlInputConfig, TransitioningGifConfig, ClickableConfig } from '@/pages/Map/components/types';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";
import { uploadMedia } from '@/services/mapService';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Shared style constants using theme colors
const selectClassName = "text-xs px-2 py-1 rounded-md border border-border bg-background text-foreground";
const secondaryButtonClassName = "h-7 text-xs bg-background text-primary border-border hover:bg-accent";
const cardContainerClassName = "space-y-2 p-2 border border-border rounded-md bg-card";
const inputClassName = "h-11 px-4 bg-background border-border text-foreground placeholder:text-muted focus-visible:ring-1 focus-visible:ring-primary focus-visible:ring-offset-0";
const labelClassName = "text-sm font-normal text-muted";

// Update button style constants
const iconButtonClassName = "h-9 w-9 rounded-md flex items-center justify-center";
const actionButtonClassName = "bg-transparent hover:bg-transparent";

interface ComponentSettingsProps {
  component: ComponentConfig;
  onClose: () => void;
  onSave: (config: ComponentConfig) => void;
  onDelete: (id: string) => void;
  onDuplicate?: (config: ComponentConfig) => void;
  onZIndexChange?: (id: string, change: 'up' | 'down' | 'top' | 'bottom') => void;
}

export const ComponentSettings: React.FC<ComponentSettingsProps> = ({
  component,
  onClose,
  onSave,
  onDelete,
  onDuplicate,
  onZIndexChange,
}) => {
  const [localConfig, setLocalConfig] = useState<ComponentConfig>(component);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');

  useEffect(() => {
    setLocalConfig(component);
  }, [component]);

  const handleChange = (changes: Partial<typeof localConfig['properties']>) => {
    const updatedConfig = {
      ...localConfig,
      properties: {
        ...localConfig.properties,
        ...changes
      }
    } as ComponentConfig;
    setLocalConfig(updatedConfig);
    onSave(updatedConfig);
  };

  const handleSave = () => {
    onSave(localConfig);
    onClose();
  };

  const handleDelete = () => {
    onDelete(component.id);
    onClose();
  };

  const handleDuplicate = () => {
    if (onDuplicate) {
      // Create a new config with a unique ID and center position
      const duplicatedConfig = {
        ...localConfig,
        id: `${localConfig.id}-copy-${Date.now()}`,
        xPercent: 50,
        yPercent: 50,
      };
      onDuplicate(duplicatedConfig);
    }
  };

  const renderZIndexControls = () => {
    return (
      <>
        <Separator className="my-6 bg-border" />
        <div className="flex items-center justify-between">
          <Label className={labelClassName}>Layer</Label>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onZIndexChange?.(component.id, 'bottom')}
              className={`${iconButtonClassName} ${actionButtonClassName} text-muted hover:text-primary hover:bg-accent`}
              title="Send to back"
            >
              <ArrowDownToLine className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onZIndexChange?.(component.id, 'down')}
              className={`${iconButtonClassName} ${actionButtonClassName} text-muted hover:text-primary hover:bg-accent`}
              title="Move backward"
            >
              <MoveDown className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onZIndexChange?.(component.id, 'up')}
              className={`${iconButtonClassName} ${actionButtonClassName} text-muted hover:text-primary hover:bg-accent`}
              title="Move forward"
            >
              <MoveUp className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onZIndexChange?.(component.id, 'top')}
              className={`${iconButtonClassName} ${actionButtonClassName} text-muted hover:text-primary hover:bg-accent`}
              title="Bring to front"
            >
              <ArrowUpToLine className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </>
    );
  };

  const renderSettings = () => {
    switch (localConfig.type) {
      case 'textbox': {
        const config = localConfig as TextBoxConfig;
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-foreground">Text Content</Label>
              <Input
                value={config.properties.text}
                onChange={(e) => handleChange({ text: e.target.value })}
                placeholder="Enter text"
                className={inputClassName}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-foreground">Background</Label>
                <div className="relative">
                  <Input
                    type="color"
                    value={config.properties.backgroundColor}
                    onChange={(e) => handleChange({ backgroundColor: e.target.value })}
                    className={cn("h-9 cursor-pointer", inputClassName)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label className="text-foreground">Text Color</Label>
                <div className="relative">
                  <Input
                    type="color"
                    value={config.properties.textColor}
                    onChange={(e) => handleChange({ textColor: e.target.value })}
                    className={cn("h-9 cursor-pointer", inputClassName)}
                  />
                </div>
              </div>
            </div>
          </div>
        );
      }

      case 'realtime-value': {
        const config = localConfig as RealTimeValueConfig;
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-foreground">Title</Label>
              <Input
                value={config.properties.title}
                onChange={(e) => handleChange({ title: e.target.value })}
                placeholder="Title"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground">Device ID</Label>
              <Input
                value={config.properties.deviceId}
                onChange={(e) => handleChange({ deviceId: e.target.value })}
                placeholder="Device ID"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground">Datapoint</Label>
              <Input
                value={config.properties.datapoint}
                onChange={(e) => handleChange({ datapoint: e.target.value })}
                placeholder="Datapoint"
                className={inputClassName}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-foreground">Unit</Label>
                <Input
                  value={config.properties.unit}
                  onChange={(e) => handleChange({ unit: e.target.value })}
                  placeholder="Unit"
                  className={inputClassName}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-foreground">Precision</Label>
                <Input
                  type="number"
                  value={config.properties.precision}
                  onChange={(e) => handleChange({ precision: parseInt(e.target.value) || 0 })}
                  placeholder="Precision"
                  min={0}
                  max={10}
                  className={inputClassName}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-foreground">Background</Label>
                <Input
                  type="color"
                  value={config.properties.backgroundColor}
                  onChange={(e) => handleChange({ backgroundColor: e.target.value })}
                  className={cn("h-9 cursor-pointer", inputClassName)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-foreground">Text Color</Label>
                <Input
                  type="color"
                  value={config.properties.textColor}
                  onChange={(e) => handleChange({ textColor: e.target.value })}
                  className={cn("h-9 cursor-pointer", inputClassName)}
                />
              </div>
            </div>
          </div>
        );
      }

      case 'machine-status': {
        const config = localConfig as MachineStatusConfig;
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label className={labelClassName}>Device ID</Label>
              <Input
                value={config.properties.deviceId}
                onChange={(e) => handleChange({ deviceId: e.target.value })}
                placeholder="Device ID"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <Label className={labelClassName}>Device Name</Label>
              <Input
                value={config.properties.deviceName}
                onChange={(e) => handleChange({ deviceName: e.target.value })}
                placeholder="Device Name"
                className={inputClassName}
              />
            </div>
            <div className="flex items-center justify-between py-2">
              <Label className={labelClassName}>Show Name</Label>
              <Switch
                checked={config.properties.showName}
                onCheckedChange={(checked: boolean) => handleChange({ showName: checked })}
              />
            </div>
            <div className="space-y-2">
              <Label className={labelClassName}>Equipment Type</Label>
              <div className="flex gap-2">
                <Button
                  variant={config.properties.equipmentType === 'Normal' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChange({ equipmentType: 'Normal' })}
                  className={cn(iconButtonClassName, "flex-1")}
                >
                  Normal
                </Button>
                <Button
                  variant={config.properties.equipmentType === 'VSD' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChange({ equipmentType: 'VSD' })}
                  className={cn(iconButtonClassName, "flex-1")}
                >
                  VSD
                </Button>
                <Button
                  variant={config.properties.equipmentType === 'Chiller' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChange({ equipmentType: 'Chiller' })}
                  className={cn(iconButtonClassName, "flex-1")}
                >
                  Chiller
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label className={labelClassName}>Name Location</Label>
              <div className="flex gap-2">
                <Button
                  variant={config.properties.nameLocation === 'top' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChange({ nameLocation: 'top' })}
                  className={cn(iconButtonClassName, "flex-1")}
                >
                  Top
                </Button>
                <Button
                  variant={config.properties.nameLocation === 'bottom' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChange({ nameLocation: 'bottom' })}
                  className={cn(iconButtonClassName, "flex-1")}
                >
                  Bottom
                </Button>
                <Button
                  variant={config.properties.nameLocation === 'left' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChange({ nameLocation: 'left' })}
                  className={cn(iconButtonClassName, "flex-1")}
                >
                  Left
                </Button>
                <Button
                  variant={config.properties.nameLocation === 'right' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChange({ nameLocation: 'right' })}
                  className={cn(iconButtonClassName, "flex-1")}
                >
                  Right
                </Button>
              </div>
            </div>

            {/* Chiller Parameter Display Settings */}
            {config.properties.equipmentType === 'Chiller' && (
              <>
                <Separator className="my-4 bg-border" />
                <div className="space-y-2">
                  <Label className={labelClassName}>Chiller Parameters</Label>
                  <div className="space-y-2 p-3 border border-border rounded-md bg-card">
                    <div className="flex items-center justify-between py-2">
                      <Label className={labelClassName}>Show Efficiency</Label>
                      <Switch
                        checked={config.properties.chillerDisplaySettings?.showEfficiency ?? true}
                        onCheckedChange={(checked: boolean) => {
                          const currentSettings = config.properties.chillerDisplaySettings || {
                            showEfficiency: true,
                            showRLA: true,
                            showSetpoint: true,
                            showCHW: true,
                            showCDW: true
                          };
                          handleChange({
                            chillerDisplaySettings: {
                              ...currentSettings,
                              showEfficiency: checked
                            }
                          });
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <Label className={labelClassName}>Show % RLA</Label>
                      <Switch
                        checked={config.properties.chillerDisplaySettings?.showRLA ?? true}
                        onCheckedChange={(checked: boolean) => {
                          const currentSettings = config.properties.chillerDisplaySettings || {
                            showEfficiency: true,
                            showRLA: true,
                            showSetpoint: true,
                            showCHW: true,
                            showCDW: true
                          };
                          handleChange({
                            chillerDisplaySettings: {
                              ...currentSettings,
                              showRLA: checked
                            }
                          });
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <Label className={labelClassName}>Show Setpoint</Label>
                      <Switch
                        checked={config.properties.chillerDisplaySettings?.showSetpoint ?? true}
                        onCheckedChange={(checked: boolean) => {
                          const currentSettings = config.properties.chillerDisplaySettings || {
                            showEfficiency: true,
                            showRLA: true,
                            showSetpoint: true,
                            showCHW: true,
                            showCDW: true
                          };
                          handleChange({
                            chillerDisplaySettings: {
                              ...currentSettings,
                              showSetpoint: checked
                            }
                          });
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <Label className={labelClassName}>Show CHW</Label>
                      <Switch
                        checked={config.properties.chillerDisplaySettings?.showCHW ?? true}
                        onCheckedChange={(checked: boolean) => {
                          const currentSettings = config.properties.chillerDisplaySettings || {
                            showEfficiency: true,
                            showRLA: true,
                            showSetpoint: true,
                            showCHW: true,
                            showCDW: true
                          };
                          handleChange({
                            chillerDisplaySettings: {
                              ...currentSettings,
                              showCHW: checked
                            }
                          });
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <Label className={labelClassName}>Show CDW</Label>
                      <Switch
                        checked={config.properties.chillerDisplaySettings?.showCDW ?? true}
                        onCheckedChange={(checked: boolean) => {
                          const currentSettings = config.properties.chillerDisplaySettings || {
                            showEfficiency: true,
                            showRLA: true,
                            showSetpoint: true,
                            showCHW: true,
                            showCDW: true
                          };
                          handleChange({
                            chillerDisplaySettings: {
                              ...currentSettings,
                              showCDW: checked
                            }
                          });
                        }}
                      />
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        );
      }

      case 'gif': {
        const config = localConfig as GifConfig;
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-foreground">Device ID</Label>
              <Input
                value={config.properties.deviceId}
                onChange={(e) => handleChange({ deviceId: e.target.value })}
                placeholder="Device ID"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground">Datapoint</Label>
              <Input
                value={config.properties.datapoint}
                onChange={(e) => handleChange({ datapoint: e.target.value })}
                placeholder="Datapoint"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Label className="text-foreground">GIF File</Label>
                {uploadStatus === 'success' && (
                  <span className="text-xs text-success">Upload Success</span>
                )}
                {uploadStatus === 'error' && (
                  <span className="text-xs text-destructive">Upload Failed</span>
                )}
              </div>
              <div className="relative">
                <Input
                  type="file"
                  accept=".gif,.png"
                  key={uploadStatus === 'error' ? 'error' : 'default'}
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (!file) return;

                    try {
                      setUploadStatus('idle');
                      const response = await uploadMedia(file);
                      const updatedConfig = {
                        ...localConfig,
                        properties: {
                          ...localConfig.properties,
                          gifName: response.filename
                        }
                      };
                      setLocalConfig(updatedConfig);
                      onSave(updatedConfig);
                      setUploadStatus('success');
                    } catch (error) {
                      console.error('Failed to upload media:', error);
                      setUploadStatus('error');
                    }
                  }}
                  className="cursor-pointer file:mr-4 file:py-1 file:px-2 file:rounded-md file:border-0 file:text-sm file:font-medium hover:file:bg-accent"
                />
              </div>
              {config.properties.gifName && uploadStatus !== 'error' && (
                <p className="text-xs text-muted mt-1">
                  Current: {config.properties.gifName}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-foreground">Zoom Level</Label>
                <span className="text-xs text-muted min-w-[4ch]">
                  {(config.properties.zoomLevel || 1).toFixed(3)}x
                </span>
              </div>
              <div
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
              >
                <Slider
                  min={0.1}
                  max={2}
                  step={0.01}
                  value={[config.properties.zoomLevel || 1]}
                  onValueChange={(values: number[]) => handleChange({ zoomLevel: values[0] })}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        );
      }

      case 'datapoint-display': {
        const config = localConfig as DatapointDisplayConfig;
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-foreground">Title</Label>
              <Input
                value={config.properties.title}
                onChange={(e) => handleChange({ title: e.target.value })}
                placeholder="Card Title (optional)"
                className={inputClassName}
              />
            </div>
            <Separator className="bg-border" />
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-foreground">Datapoints</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newDatapoints = [...config.properties.datapoints];
                    newDatapoints.push({
                      name: 'New Datapoint',
                      deviceId: '',
                      datapoint: '',
                      precision: 2,
                      unit: '',
                    });
                    handleChange({ datapoints: newDatapoints });
                  }}
                  className={secondaryButtonClassName}
                >
                  Add Datapoint
                </Button>
              </div>
              <div className="space-y-3">
                {config.properties.datapoints.map((dp, index) => (
                  <div key={index} className={cardContainerClassName}>
                    <div className="flex items-center justify-between">
                      <Label className="text-xs text-muted">Datapoint {index + 1}</Label>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          const newDatapoints = [...config.properties.datapoints];
                          newDatapoints.splice(index, 1);
                          handleChange({ datapoints: newDatapoints });
                        }}
                        className="h-7 w-7 text-[#EF4337] hover:text-[#EF4337] hover:bg-[#EF4337]/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      <Input
                        placeholder="Display Name"
                        value={dp.name}
                        onChange={(e) => {
                          const newDatapoints = [...config.properties.datapoints];
                          newDatapoints[index] = { ...dp, name: e.target.value };
                          handleChange({ datapoints: newDatapoints });
                        }}
                        className="border-[#EDEFF9] bg-[#F9FAFF] text-[#5E5E5E] placeholder:text-[#788796]"
                      />
                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          placeholder="Device ID"
                          value={dp.deviceId}
                          onChange={(e) => {
                            const newDatapoints = [...config.properties.datapoints];
                            newDatapoints[index] = { ...dp, deviceId: e.target.value };
                            handleChange({ datapoints: newDatapoints });
                          }}
                          className="border-[#EDEFF9] bg-[#F9FAFF] text-[#5E5E5E] placeholder:text-[#788796]"
                        />
                        <Input
                          placeholder="Datapoint"
                          value={dp.datapoint}
                          onChange={(e) => {
                            const newDatapoints = [...config.properties.datapoints];
                            newDatapoints[index] = { ...dp, datapoint: e.target.value };
                            handleChange({ datapoints: newDatapoints });
                          }}
                          className="border-[#EDEFF9] bg-[#F9FAFF] text-[#5E5E5E] placeholder:text-[#788796]"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          placeholder="Unit"
                          value={dp.unit}
                          onChange={(e) => {
                            const newDatapoints = [...config.properties.datapoints];
                            newDatapoints[index] = { ...dp, unit: e.target.value };
                            handleChange({ datapoints: newDatapoints });
                          }}
                          className="border-[#EDEFF9] bg-[#F9FAFF] text-[#5E5E5E] placeholder:text-[#788796]"
                        />
                        <Input
                          type="number"
                          placeholder="Precision"
                          value={dp.precision}
                          min={0}
                          max={10}
                          onChange={(e) => {
                            const newDatapoints = [...config.properties.datapoints];
                            newDatapoints[index] = { ...dp, precision: parseInt(e.target.value) || 0 };
                            handleChange({ datapoints: newDatapoints });
                          }}
                          className="border-[#EDEFF9] bg-[#F9FAFF] text-[#5E5E5E] placeholder:text-[#788796]"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      }

      case 'cooling-load': {
        const config = localConfig as CoolingLoadConfig;
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-foreground">Max Plant Capacity (RT)</Label>
              <Input
                type="number"
                value={config.properties.maxCapacity}
                onChange={(e) => handleChange({ maxCapacity: parseInt(e.target.value) || 1000 })}
                placeholder="Max Capacity"
                min={100}
                max={100000}
                className={inputClassName}
              />
            </div>
          </div>
        );
      }

      case 'control-input': {
        const config = localConfig as ControlInputConfig;
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className={labelClassName}>Title</Label>
              <Input
                value={config.properties.title}
                onChange={(e) => handleChange({ title: e.target.value })}
                placeholder="Title"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <Label className={labelClassName}>Unit</Label>
              <Input
                value={config.properties.unit}
                onChange={(e) => handleChange({ unit: e.target.value })}
                placeholder="Unit (e.g. Hz, °C)"
                className={inputClassName}
              />
            </div>
            <Separator className="bg-border" />
            <div className="space-y-2">
              <Label className={labelClassName}>Control Settings</Label>
              <div className={cardContainerClassName}>
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      placeholder="Device ID"
                      value={config.properties.deviceId}
                      onChange={(e) => handleChange({ deviceId: e.target.value })}
                      className="bg-background border-border text-foreground placeholder:text-muted"
                    />
                    <Input
                      placeholder="Datapoint"
                      value={config.properties.datapoint}
                      onChange={(e) => handleChange({ datapoint: e.target.value })}
                      className="bg-background border-border text-foreground placeholder:text-muted"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      }

      case 'transitioning-gif': {
        const config = localConfig as TransitioningGifConfig;
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-foreground">Control Device ID</Label>
              <Input
                value={config.properties.controlDeviceId}
                onChange={(e) => handleChange({ controlDeviceId: e.target.value })}
                placeholder="Control Device ID"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground">Control Datapoint</Label>
              <Input
                value={config.properties.controlDatapoint}
                onChange={(e) => handleChange({ controlDatapoint: e.target.value })}
                placeholder="Control Datapoint"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground">Feedback Device ID</Label>
              <Input
                value={config.properties.feedbackDeviceId}
                onChange={(e) => handleChange({ feedbackDeviceId: e.target.value })}
                placeholder="Feedback Device ID"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground">Feedback Datapoint</Label>
              <Input
                value={config.properties.feedbackDatapoint}
                onChange={(e) => handleChange({ feedbackDatapoint: e.target.value })}
                placeholder="Feedback Datapoint"
                className={inputClassName}
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-foreground">Zoom Level</Label>
                <span className="text-xs text-muted min-w-[4ch]">
                  {(config.properties.zoomLevel || 1).toFixed(3)}x
                </span>
              </div>
              <div
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
              >
                <Slider
                  min={0.1}
                  max={2}
                  step={0.01}
                  value={[config.properties.zoomLevel || 1]}
                  onValueChange={(values: number[]) => handleChange({ zoomLevel: values[0] })}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        );
      }

      case 'clickable': {
        const config = localConfig as ClickableConfig;

        // Use a state variable to track the raw text input
        const [payloadText, setPayloadText] = useState(() => {
          return JSON.stringify(config.properties.payload || {}, null, 2);
        });

        // Update the payload text when the component config changes
        useEffect(() => {
          if (config.properties.payload) {
            setPayloadText(JSON.stringify(config.properties.payload, null, 2));
          }
        }, [config.id]); // Only update when component ID changes to avoid loops

        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-foreground">Popup Type</Label>
              <Select
                value={config.properties.popup_type}
                onValueChange={(value) => {
                  // Create default payload based on popup type
                  let defaultPayload = {};
                  if (value === 'chiller_popup') {
                    defaultPayload = { device_id: "chiller_1" };
                  }

                  // Update the payload text field
                  setPayloadText(JSON.stringify(defaultPayload, null, 2));

                  // Update both popup_type and payload
                  handleChange({
                    popup_type: value,
                    payload: defaultPayload
                  });
                }}
              >
                <SelectTrigger className={inputClassName}>
                  <SelectValue placeholder="Select popup type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="chiller_popup">Chiller Popup</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-foreground">Payload (JSON)</Label>
              <textarea
                value={payloadText}
                onChange={(e) => {
                  // Always update the text field, even if JSON is invalid
                  const newValue = e.target.value;
                  setPayloadText(newValue);

                  // Only update the actual payload when JSON is valid
                  try {
                    const parsedJson = JSON.parse(newValue);
                    handleChange({ payload: parsedJson });
                  } catch (error) {
                    // If it's valid JSON but with a trailing comma, try to fix it
                    try {
                      // This is a common JSON error - trailing commas
                      const fixedJson = newValue.replace(/,\s*([\}\]])/g, '$1');
                      const parsedJson = JSON.parse(fixedJson);
                      handleChange({ payload: parsedJson });
                    } catch (e) {
                      // Silently ignore JSON parse errors while typing
                      // The user will see syntax highlighting in the text field
                    }
                  }
                }}
                placeholder={config.properties.popup_type === 'chiller_popup' ?
                  '{"device_id": "chiller_1"}' :
                  // example of page redirect popup_type. This is not used in the current implementation.
                  config.properties.popup_type === 'page_redirect' ?
                  '{"redirect_url": "https://example.com"}' :
                  '{}'}
                className="w-full min-h-[120px] px-3 py-2 rounded-md border border-border bg-background text-foreground placeholder:text-muted resize-y font-mono text-sm"
                spellCheck="false"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-foreground">Size</Label>
                <span className="text-xs text-muted min-w-[4ch]">
                  {(config.properties.zoomLevel || 1).toFixed(2)}x
                </span>
              </div>
              <div
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
              >
                <Slider
                  min={0.1}
                  max={2}
                  step={0.05}
                  value={[config.properties.zoomLevel || 1]}
                  onValueChange={(values: number[]) => handleChange({ zoomLevel: values[0] })}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        );
      }

      default:
        return null;
    }
  };

  return (
    <Card
      className="fixed right-6 top-1/2 -translate-y-1/2 z-50 w-[320px] max-h-[90vh] bg-card shadow-card border border-border"
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()}
      onTouchStart={(e) => e.stopPropagation()}
    >
      <CardHeader className="py-3 px-4 border-b border-border flex-none">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base text-primary-dark font-semibold">{localConfig.label}</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              className={`${iconButtonClassName} ${actionButtonClassName} text-destructive hover:text-destructive hover:bg-destructive/10`}
              title="Delete component"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDuplicate}
              className={`${iconButtonClassName} ${actionButtonClassName} text-primary hover:text-primary hover:bg-primary/10`}
              title="Duplicate component"
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className={`${iconButtonClassName} ${actionButtonClassName} text-muted hover:text-muted hover:bg-muted/10`}
              title="Close without saving"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 space-y-6 overflow-y-auto flex-grow bg-card" style={{ maxHeight: 'calc(90vh - 4rem)' }}>
        <div className="space-y-4">
          {renderSettings()}
          {renderZIndexControls()}
        </div>
      </CardContent>
    </Card>
  );
};