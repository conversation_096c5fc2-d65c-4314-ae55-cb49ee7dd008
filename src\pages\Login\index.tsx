import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Globe, Eye, EyeOff, LogIn } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import altoLogo from '@/assets/alto_logo.svg';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();

  // AltoTech color palette
  const colors = {
    primary: '#0E7EE4', // Blue
    secondary: '#14B8B4', // Teal
  };

  useEffect(() => {
    // Animation trigger after component mount
    setLoaded(true);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await login(username, password);
      navigate('/app/map');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to login');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative flex h-screen bg-slate-900 overflow-hidden">
      {/* Simple universe gradient background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Dark space gradient base */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-950 via-[#0a1f36] to-[#0a2a33]"></div>
        
        {/* Subtle blue glow */}
        <div 
          className="absolute w-[800px] h-[800px] rounded-full opacity-10"
          style={{
            bottom: '-300px',
            right: '-200px',
            background: `radial-gradient(circle, ${colors.primary} 0%, transparent 70%)`,
            filter: 'blur(80px)'
          }}
        ></div>
        
        {/* Subtle teal glow */}
        <div 
          className="absolute w-[600px] h-[600px] rounded-full opacity-8"
          style={{
            top: '-200px',
            left: '-200px',
            background: `radial-gradient(circle, ${colors.secondary} 0%, transparent 70%)`,
            filter: 'blur(60px)'
          }}
        ></div>
        
        {/* Stars - static positions */}
        <div className="absolute inset-0">
          {/* Small stars */}
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[15%] left-[20%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[25%] left-[85%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[45%] left-[35%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[65%] left-[75%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[85%] left-[15%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[35%] left-[65%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[75%] left-[55%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[5%] left-[45%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[30%] left-[5%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[50%] left-[95%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[60%] left-[30%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[70%] left-[80%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[90%] left-[40%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[10%] left-[60%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[80%] left-[25%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[40%] left-[75%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[55%] left-[10%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[95%] left-[70%]"></div>
          <div className="absolute rounded-full w-px h-px bg-white opacity-80 top-[20%] left-[50%]"></div>

          {/* Medium stars with glow */}
          <div className="absolute rounded-full w-[2px] h-[2px] bg-white opacity-90 top-[28%] left-[33%]" style={{ boxShadow: '0 0 3px white' }}></div>
          <div className="absolute rounded-full w-[2px] h-[2px] bg-white opacity-90 top-[72%] left-[67%]" style={{ boxShadow: '0 0 3px white' }}></div>
          <div className="absolute rounded-full w-[2px] h-[2px] bg-white opacity-90 top-[17%] left-[78%]" style={{ boxShadow: '0 0 3px white' }}></div>
          <div className="absolute rounded-full w-[2px] h-[2px] bg-white opacity-90 top-[83%] left-[22%]" style={{ boxShadow: '0 0 3px white' }}></div>
          <div className="absolute rounded-full w-[2px] h-[2px] bg-white opacity-90 top-[42%] left-[88%]" style={{ boxShadow: '0 0 3px white' }}></div>
          <div className="absolute rounded-full w-[2px] h-[2px] bg-white opacity-90 top-[58%] left-[12%]" style={{ boxShadow: '0 0 3px white' }}></div>
        </div>
        
        {/* Animated stars (slowly pulsing) */}
        <div className="absolute rounded-full opacity-80 animate-pulse top-[37%] left-[23%]" style={{ 
          width: '3px', height: '3px', 
          backgroundColor: 'white',
          boxShadow: '0 0 4px white',
          animationDuration: '4s' 
        }}></div>
        
        <div className="absolute rounded-full opacity-80 animate-pulse top-[18%] left-[67%]" style={{ 
          width: '3px', height: '3px', 
          backgroundColor: 'white',
          boxShadow: '0 0 4px white',
          animationDuration: '5s',
          animationDelay: '1s' 
        }}></div>
        
        <div className="absolute rounded-full opacity-80 animate-pulse top-[78%] left-[45%]" style={{ 
          width: '3px', height: '3px', 
          backgroundColor: 'white',
          boxShadow: '0 0 4px white',
          animationDuration: '6s',
          animationDelay: '2s' 
        }}></div>
        
        {/* Shooting star animation - only one at a time */}
        <div className="absolute h-[1px] w-[150px] bg-gradient-to-r from-transparent to-white opacity-0"
          style={{
            top: '30%',
            left: '20%',
            rotate: '45deg',
            animation: 'customShootingStar 4s ease-out infinite',
            animationDelay: '2s',
            boxShadow: '0 0 4px rgba(255, 255, 255, 0.8)'
          }}></div>
        
        <div className="absolute h-[1px] w-[120px] bg-gradient-to-r from-transparent to-white opacity-0"
          style={{
            top: '70%',
            left: '60%',
            rotate: '45deg',
            animation: 'customShootingStar 3s ease-out infinite',
            animationDelay: '7s',
            boxShadow: '0 0 4px rgba(255, 255, 255, 0.8)'
          }}></div>

        <div className="absolute h-[1px] w-[100px] bg-gradient-to-r from-transparent to-white opacity-0"
          style={{
            top: '15%',
            left: '40%',
            rotate: '45deg',
            animation: 'customShootingStar 2.5s ease-out infinite',
            animationDelay: '5s',
            boxShadow: '0 0 4px rgba(255, 255, 255, 0.8)'
          }}></div>
          
        <div className="absolute h-[1px] w-[80px] bg-gradient-to-r from-transparent to-white opacity-0"
          style={{
            top: '55%',
            left: '75%',
            rotate: '45deg',
            animation: 'customShootingStar 3.2s ease-out infinite',
            animationDelay: '9s',
            boxShadow: '0 0 4px rgba(255, 255, 255, 0.8)'
          }}></div>
        
        {/* Animated nebula effect */}
        <div className="absolute opacity-10 animate-pulse" style={{
          top: '40%',
          left: '70%',
          width: '200px',
          height: '200px',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${colors.primary}50 0%, transparent 70%)`,
          filter: 'blur(20px)',
          animationDuration: '10s',
          transform: 'scale(1.2)'
        }}></div>
        
        <div className="absolute opacity-10 animate-pulse" style={{
          top: '60%',
          left: '20%',
          width: '80px',
          height: '80px',
          borderRadius: '50%',
          background: `radial-gradient(circle, ${colors.secondary}50 0%, transparent 70%)`,
          filter: 'blur(20px)',
          animationDuration: '8s',
          animationDelay: '1s',
          transform: 'scale(1.5)'
        }}></div>
      </div>

      <style>{`
        @keyframes customShootingStar {
          0% {
            transform: translateX(-1000px);
            opacity: 0;
          }
          5% {
            opacity: 0.3;
          }
          15% {
            opacity: 0.8;
          }
          30% {
            opacity: 0.3;
          }
          100% {
            transform: translate(2000px);
            opacity: 0;
          }
        }
      `}</style>

      <div className="relative w-full flex flex-col items-center justify-center p-8 z-10">
        <div className={cn(
          "w-full max-w-md transition-all duration-700 transform",
          loaded ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
        )}>
          {/* Logo */}
          <div className="flex justify-between items-center mb-12">
            <div className="relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] blur-sm opacity-70 rounded-lg"></div>
              <div className="relative bg-slate-800/90 backdrop-blur-sm px-6 py-3 rounded-lg border border-slate-700">
                <div className="flex items-center">
                  <img src={altoLogo} alt="Alto Logo" className="h-6 mr-2" />
                  <div className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4]">
                    Alto CERO
                  </div>
                </div>
              </div>
            </div>
            <button className="flex items-center gap-2 text-sm text-slate-400 hover:text-white transition-colors duration-300 backdrop-blur-sm bg-slate-800/40 px-3 py-1.5 rounded-md border border-slate-700/50">
              <Globe className="w-4 h-4" />
              <span>en-US</span>
            </button>
          </div>

          <div className="space-y-8 backdrop-blur-sm bg-slate-800/40 p-8 rounded-xl border border-slate-700/50 shadow-xl" 
               style={{boxShadow: `0 0 40px rgba(14, 126, 228, 0.1), 0 0 20px rgba(20, 184, 180, 0.05)`}}>
            <div className={cn(
              "space-y-2 transition-all duration-500 delay-300",
              loaded ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
            )}>
              <h2 className="text-3xl font-medium text-white flex items-center">
                Sign In
                <div className="ml-3 h-1 w-8 bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] rounded-full"></div>
              </h2>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div className={cn(
                  "transition-all duration-500 delay-400",
                  loaded ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
                )}>
                  <label className="block text-sm font-medium text-slate-300 mb-1.5">
                    Username
                  </label>
                  <div className="relative group">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] rounded-lg opacity-0 group-hover:opacity-20 transition duration-300"></div>
                    <input
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="relative w-full px-4 py-3 rounded-lg bg-slate-900/80 border border-slate-700 focus:border-[#0E7EE4] focus:ring-1 focus:ring-[#0E7EE4] transition-all duration-300 text-white"
                      placeholder="Enter your username"
                      required
                    />
                    <div className="absolute right-3 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-transparent transition-all duration-300" style={{
                      backgroundColor: username ? '#14B8B4' : 'transparent',
                      opacity: username ? 1 : 0
                    }}></div>
                  </div>
                </div>

                <div className={cn(
                  "transition-all duration-500 delay-500",
                  loaded ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
                )}>
                  <div className="flex justify-between mb-1.5">
                    <label className="block text-sm font-medium text-slate-300">
                      Password
                    </label>
                    <button type="button" className="text-xs text-[#0E7EE4] hover:text-[#14B8B4] transition-colors duration-300">
                      Forgot Password?
                    </button>
                  </div>
                  <div className="relative group">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] rounded-lg opacity-0 group-hover:opacity-20 transition duration-300"></div>
                    <input
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="relative w-full px-4 py-3 rounded-lg bg-slate-900/80 border border-slate-700 focus:border-[#0E7EE4] focus:ring-1 focus:ring-[#0E7EE4] transition-all duration-300 text-white"
                      placeholder="Enter your password"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-white transition-colors duration-300"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                    <div className="absolute right-10 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-transparent transition-all duration-300" style={{
                      backgroundColor: password ? '#14B8B4' : 'transparent',
                      opacity: password ? 1 : 0
                    }}></div>
                  </div>
                </div>
              </div>

              <div className={cn(
                "transition-all duration-500 delay-600",
                loaded ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
              )}>
                <button
                  type="submit"
                  disabled={loading || !username || !password}
                  className={cn(
                    "w-full py-3 rounded-lg font-medium transition-all duration-300 flex items-center justify-center gap-2 relative overflow-hidden group",
                    username && password 
                      ? "bg-gradient-to-r from-[#0E7EE4] to-[#14B8B4] text-white hover:shadow-lg hover:shadow-[#0E7EE4]/20" 
                      : "bg-slate-700/50 text-slate-400 cursor-not-allowed"
                  )}
                >
                  {/* Animated button effects */}
                  {username && password && (
                    <>
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        <div className="absolute inset-0 bg-[#0E7EE4] opacity-0 group-hover:opacity-20 animate-pulse"></div>
                      </div>
                    </>
                  )}
                  
                  {loading ? (
                    <>
                      <span>Signing in</span>
                      <span className="flex space-x-1 ml-1">
                        <span className="w-1 h-1 rounded-full bg-white animate-bounce" style={{ animationDelay: '0s' }}></span>
                        <span className="w-1 h-1 rounded-full bg-white animate-bounce" style={{ animationDelay: '0.2s' }}></span>
                        <span className="w-1 h-1 rounded-full bg-white animate-bounce" style={{ animationDelay: '0.4s' }}></span>
                      </span>
                    </>
                  ) : (
                    <>
                      <span className="relative z-10">Sign In</span>
                      <LogIn className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 relative z-10" />
                    </>
                  )}
                </button>
              </div>

              {error && (
                <div className="text-red-400 text-sm bg-red-400/10 p-3 rounded-md border border-red-400/20 animate-fadeIn">
                  {error}
                </div>
              )}
            </form>

            <div className={cn(
              "flex items-center gap-3 pt-2 transition-all duration-500 delay-700",
              loaded ? "opacity-100" : "opacity-0"
            )}>
              <div className="h-px bg-slate-700 flex-1"></div>
              <span className="text-xs text-slate-500">OR</span>
              <div className="h-px bg-slate-700 flex-1"></div>
            </div>

            <div className={cn(
              "transition-all duration-500 delay-800",
              loaded ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
            )}>
              <button
                className="w-full py-3 rounded-lg border border-slate-700 hover:bg-slate-800 hover:border-[#0E7EE4]/30 font-medium transition-all duration-300 text-white relative overflow-hidden group"
                onClick={() => window.open('https://www.altotech.ai', '_blank')}
              >
                <span className="relative z-10">Contact Support</span>
                <div className="absolute inset-0 w-0 bg-gradient-to-r from-[#0E7EE4]/10 to-[#14B8B4]/10 group-hover:w-full transition-all duration-500"></div>
                <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-[#0E7EE4]/0 via-[#14B8B4] to-[#0E7EE4]/0 translate-y-full group-hover:translate-y-0 opacity-0 group-hover:opacity-30 transition-all duration-500"></div>
              </button>
            </div>
          </div>

          <div className="text-center text-xs text-slate-500 pt-6 mt-8">
            © AltoTech Global 2025
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login; 