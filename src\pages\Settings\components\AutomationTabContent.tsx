import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from 'lucide-react';
import { ChillerScheduleCard } from './chillerScheduleCard'; // Corrected relative path
import { ScheduleSettingsModal } from '../modals/scheduleSettingsModal';
import { ScheduleConfigModal } from '../modals/scheduleConfigModal';
import { ControlParametersModal } from '../modals/controlParametersModal';
import { ChillerSettingsModal } from '../modals/chillerSettingsModal';
import { toast } from "@/components/ui/use-toast";
import { ApiSchedule, WeeklySchedule, ControlPair, TimeRange } from '../types';
import { getSchedules, getScheduleById, createSchedule, updateSchedule, deleteSchedule as deleteScheduleApi } from '@/services/scheduleService';
import { getDevices } from '@/services/deviceService';

// Define a default structure for a new schedule
const defaultNewSchedule: Partial<ApiSchedule> = { 
  name: 'New Schedule',
  description: '',
  active: false,
  controls: [],
  weekly_schedule: {
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: []
  },
  special_days: []
};

// Main component for the Automation tab content
const AutomationTabContent: React.FC = () => {
  // State for managing schedules from the API
  const [schedules, setSchedules] = useState<ApiSchedule[]>([]); 
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // State for device options (would be fetched from API in a real implementation)
  const [deviceOptions, setDeviceOptions] = useState<{id: string, name: string}[]>([]);
  
  // State for modals
  const [isScheduleSettingsOpen, setIsScheduleSettingsOpen] = useState(false);
  const [isScheduleConfigOpen, setIsScheduleConfigOpen] = useState(false);
  const [isControlParamsOpen, setIsControlParamsOpen] = useState(false);
  const [isChillerSettingsOpen, setIsChillerSettingsOpen] = useState(false);
  
  // State for the currently selected/editing schedule
  const [currentSchedule, setCurrentSchedule] = useState<ApiSchedule | null>(null);
  
  // State for Control Parameters
  const [offDelay, setOffDelay] = useState<number>(300); // Example default: 5 minutes
  const [onDelay, setOnDelay] = useState<number>(300); // Example default: 5 minutes
  const [leadLagEnabled, setLeadLagEnabled] = useState<boolean>(false); 

  // Fetch schedules from the API
  const fetchSchedules = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getSchedules();      
      setSchedules(data);
    } catch (err) {
      console.error('Error fetching schedules:', err);
      setError('Failed to load schedules. Please try again later.');
      toast({
        variant: "destructive",
        title: "Error loading schedules",
        description: err instanceof Error ? err.message : "Unknown error occurred"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch device options from the API
  const fetchDeviceOptions = async () => {
    try {
      const devices = await getDevices();
      const options = devices.map((device: { id: string; name: string }) => ({
        id: device.id,
        name: device.name || `Device ${device.id}`
      }));
      setDeviceOptions(options);
    } catch (err) {
      console.error('Error fetching devices:', err);
      toast({
        variant: "destructive",
        title: "Error loading devices",
        description: err instanceof Error ? err.message : "Unknown error occurred"
      });
      // Fallback to empty array if devices can't be fetched
      setDeviceOptions([]);
    }
  };

  // Save a schedule to the API
  const saveSchedule = async (schedule: ApiSchedule) => {
    try {
      let savedSchedule: ApiSchedule;

      if (schedule.id && !schedule.id.startsWith('new-')) {
        // Update existing schedule
        savedSchedule = await updateSchedule(schedule.id, schedule);
      } else {
        // Create new schedule with a proper ID
        // If we have a temporary ID, replace it with a permanent one
        // or just use the provided ID if it exists
        const scheduleToCreate: ApiSchedule = {
          ...schedule,
          // If it's a temporary ID, generate a permanent one based on the name
          id: schedule.id && !schedule.id.startsWith('new-') 
            ? schedule.id 
            : schedule.name.toLowerCase().replace(/\s+/g, '_')
        };
        
        savedSchedule = await createSchedule(scheduleToCreate);
      }
      
      // Update local state
      setSchedules(prev => {
        const index = prev.findIndex(s => s.id === schedule.id);
        if (index !== -1) {
          // Update existing schedule
          const newSchedules = [...prev];
          newSchedules[index] = savedSchedule;
          return newSchedules;
        } else {
          // Add new schedule
          return [...prev, savedSchedule];
        }
      });
      
      toast({
        title: "Schedule Saved",
        description: `Schedule "${savedSchedule.name}" was saved successfully.`
      });
      
      return savedSchedule;
    } catch (err) {
      console.error('Error saving schedule:', err);
      toast({
        variant: "destructive",
        title: "Error Saving Schedule",
        description: err instanceof Error ? err.message : "Unknown error occurred"
      });
      throw err;
    }
  };

  // Delete a schedule from the API
  const deleteSchedule = async (scheduleId: string) => {
    try {
      // Don't attempt to delete a schedule that hasn't been saved yet
      if (scheduleId.startsWith('new-')) {
        setSchedules(prev => prev.filter(s => s.id !== scheduleId));
        return;
      }
      
      await deleteScheduleApi(scheduleId);
      
      // Update local state
      setSchedules(prev => prev.filter(s => s.id !== scheduleId));
      
      toast({
        title: "Schedule Deleted",
        description: "The schedule was deleted successfully."
      });
    } catch (err) {
      console.error('Error deleting schedule:', err);
      toast({
        variant: "destructive",
        title: "Error Deleting Schedule",
        description: err instanceof Error ? err.message : "Unknown error occurred"
      });
    }
  };

  // Fetch schedules and device options on component mount
  useEffect(() => {
    fetchSchedules();
    fetchDeviceOptions();
  }, []);

  // Handlers for opening modals
  const handleScheduleClick = (schedule: ApiSchedule) => {
    setCurrentSchedule(schedule);
    setIsScheduleSettingsOpen(true);
  };

  const handleScheduleConfigClick = (schedule: ApiSchedule) => {
    setCurrentSchedule(schedule);
    setIsScheduleConfigOpen(true);
  };

  const handleAddScheduleClick = () => {
    // Create a temporary ID for the new schedule
    const newScheduleWithId = { 
      ...defaultNewSchedule, 
      id: `new-${Date.now()}`, // Temporary ID until saved
    } as ApiSchedule; 
    setCurrentSchedule(newScheduleWithId);
    setIsScheduleConfigOpen(true); // Open config modal for new schedule
  };

  // Handler for saving a schedule from the config modal - using synchronous version for modal prop compatibility
  const handleSaveSchedule = (updatedSchedule: ApiSchedule) => {
    saveSchedule(updatedSchedule).catch(error => {
      console.error("Error saving schedule:", error);
    });
  };
  
  // Handler for saving schedule settings (weekly schedule)
  const handleSaveScheduleSettings = (weeklySchedule: WeeklySchedule) => {
    if (!currentSchedule) return;
    
    const scheduleToSave: ApiSchedule = {
      ...currentSchedule,
      weekly_schedule: weeklySchedule
    };
    
    saveSchedule(scheduleToSave).then(() => {
      setCurrentSchedule(null);
      setIsScheduleSettingsOpen(false);
    }).catch(error => {
      console.error("Error saving schedule settings:", error);
    });
  };

  return (
    <div className="p-4 space-y-6 bg-background rounded-lg"> 
      {isLoading && <div className="text-center py-8">Loading schedules...</div>}
      
      {error && <div className="text-red-500 p-4 border border-red-200 rounded-md bg-red-50">{error}</div>}
      
      {!isLoading && !error && (
        <ChillerScheduleCard 
          schedules={schedules}
          onScheduleClick={handleScheduleClick}
          onScheduleConfigClick={handleScheduleConfigClick}
          onControlParamsClick={() => setIsControlParamsOpen(true)}
          onChillerSettingsClick={() => setIsChillerSettingsOpen(true)}
          onAddScheduleClick={handleAddScheduleClick}
        />
      )}

      {/* Modals */}
      {currentSchedule && (
        <ScheduleSettingsModal
          open={isScheduleSettingsOpen}
          onOpenChange={setIsScheduleSettingsOpen}
          schedule={currentSchedule}
          onSave={handleSaveScheduleSettings}
        />
      )}
      
      {isScheduleConfigOpen && currentSchedule && (
        <ScheduleConfigModal
          open={isScheduleConfigOpen}
          onOpenChange={setIsScheduleConfigOpen}
          schedule={currentSchedule}
          deviceOptions={deviceOptions}
          onSave={handleSaveSchedule}
          onDelete={deleteSchedule}
        />
      )}
      
      <ControlParametersModal
        open={isControlParamsOpen}
        onOpenChange={setIsControlParamsOpen}
        offDelay={offDelay}
        onDelay={onDelay}
        leadLagEnabled={leadLagEnabled}
        onOnDelayChange={setOnDelay} 
        onOffDelayChange={setOffDelay} 
        onLeadLagChange={setLeadLagEnabled} 
      />
      
      <ChillerSettingsModal
        open={isChillerSettingsOpen}
        onOpenChange={setIsChillerSettingsOpen}
      />
    </div>
  );
};

export default AutomationTabContent; 