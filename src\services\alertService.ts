import { api } from './api';

export interface Alert {
  status: 'normal' | 'alert';
  priority: number;
  name: string;
  type: string;
}

export interface AlertResponse {
  'air-side': Alert[];
  'water-side': Alert[];
}

export const getAlerts = async (type: 'air-side' | 'water-side'): Promise<Alert[]> => {
  try {
    const response = await api.get(`/alerts/?type=${type}`);

    const data = response.data as AlertResponse;
    return data[type];
  } catch (error) {
    console.error('Error fetching alerts:', error);
    throw error;
  }
};

export const getActiveAlerts = async (type: 'air-side' | 'water-side'): Promise<Alert[]> => {
  try {
    const alerts = await getAlerts(type);
    return alerts.filter(alert => alert.status === 'alert');
  } catch (error) {
    console.error('Error fetching active alerts:', error);
    throw error;
  }
}; 