import React, { useEffect, useState } from 'react';
import { Spinner } from '../../components/ui/spinner';
import { useAuth } from '../../contexts/AuthContext';
import { getDashAppUrl } from '@/utils/dash';

const Dashboard: React.FC = () => {
  const { site } = useAuth();
  const dashboardAppUrl = getDashAppUrl('plant-overview', site?.id);
  
  return (
    <div className="w-full h-[calc(100vh-71px)] overflow-hidden bg-background">
      <iframe 
        src={dashboardAppUrl}
        className="w-full h-full border-none"
        title="Alto Dashboard"
        sandbox="allow-same-origin allow-scripts allow-forms"
      />
    </div>
  );
};

export default Dashboard;