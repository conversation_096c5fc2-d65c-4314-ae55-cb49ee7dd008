import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface IAQFilterDropdownProps {
  selectedMetric: string;
  onSelect: (metric: string) => void;
}

const IAQ_METRICS: { key: string; label: string }[] = [
  { key: 'co2', label: 'CO2' },
  { key: 'temperature', label: 'Temperature' },
  { key: 'humidity', label: 'Humidity' },
  { key: 'pm25', label: 'PM25' },
  { key: 'noise', label: 'Noise' },
  { key: 'illuminance', label: 'Illuminance' },
  { key: 'pmv', label: 'PMV' },
];

const IAQFilterDropdown: React.FC<IAQFilterDropdownProps> = ({
  selectedMetric,
  onSelect,
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="btn-outline btn-sm px-3 rounded border text-sm">
          {IAQ_METRICS.find(m => m.key === selectedMetric)?.label || 'Select'} ▼
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {IAQ_METRICS.map(({ key, label }) => (
          <DropdownMenuItem
            key={key}
            onClick={() => onSelect(key)}
            className="text-sm"
          >
            {label} {selectedMetric === key && '✓'}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default IAQFilterDropdown;
