// Define types for tab visibility configuration
export interface TabVisibility {
  id: string;
  label: string;
  visibleTo: {
    admin: boolean;
    operator: boolean;
    viewer: boolean;
  };
}

// Default tab configuration if none exists
export const defaultTabConfig: TabVisibility[] = [
  {
    id: "autopilot",
    label: "Autopilot",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "map",
    label: "Plant",
    visibleTo: { admin: true, operator: true  , viewer: true }
  },
  {
    id: "air-side",
    label: "Air Side",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "overview-dashboard",
    label: "Dashboard",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "historical-data",
    label: "Historical",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "maintenance",
    label: "Maintenance",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "analytics",
    label: "Analytics",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "settings",
    label: "Settings",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "saving-report",
    label: "Saving Report",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "technical-report",
    label: "Technical Report",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "afdd",
    label: "AFDD",
    visibleTo: { admin: true, operator: true, viewer: true }
  },
  {
    id: "room-analytics",
    label: "Room Analytics",
    visibleTo: { admin: true, operator: true, viewer: true }
  }
];

// Local storage key for tab visibility settings
const STORAGE_KEY = 'tab_visibility_settings';

/**
 * Create a sample JSON file with default tab visibility settings
 * @returns Default tab visibility settings JSON string
 */
export const getDefaultTabVisibilityJson = (): string => {
  return JSON.stringify(defaultTabConfig, null, 2);
};

/**
 * Download a sample tab visibility JSON file with default settings
 */
export const downloadSampleTabVisibilityFile = (): void => {
  try {
    // Create a JSON string from the default settings
    const settingsJson = getDefaultTabVisibilityJson();

    // Create a blob from the JSON string
    const blob = new Blob([settingsJson], { type: 'application/json' });

    // Create a URL for the blob
    const url = URL.createObjectURL(blob);

    // Create a link element to trigger the download
    const link = document.createElement('a');
    link.href = url;
    link.download = 'default-tab-visibility-settings.json';

    // Append the link to the document, click it, and remove it
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Revoke the URL to free up memory
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Failed to download sample tab visibility file:', error);
  }
};

/**
 * Get tab visibility settings from storage
 * @returns Array of tab visibility configurations
 */
export const getTabVisibilitySettings = async (): Promise<TabVisibility[]> => {
  try {
    // Get from localStorage
    const storedSettings = localStorage.getItem(STORAGE_KEY);
    if (storedSettings) {
      try {
        const parsedSettings = JSON.parse(storedSettings) as TabVisibility[];
        console.log('Loaded tab visibility settings from localStorage:', parsedSettings);
        return parsedSettings;
      } catch (parseError) {
        console.error('Error parsing stored tab visibility settings:', parseError);
      }
    }

    // If no settings in localStorage, use default config and save it
    console.log('Using default tab visibility configuration');
    localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultTabConfig));
    return defaultTabConfig;
  } catch (error) {
    console.error('Error getting tab visibility settings:', error);
    return defaultTabConfig;
  }
};

/**
 * Save tab visibility settings to localStorage
 * @param settings Tab visibility settings to save
 * @returns Success indicator
 */
export const saveTabVisibilitySettings = async (settings: TabVisibility[]): Promise<boolean> => {
  try {
    // Save to localStorage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    console.log('Saved tab visibility settings to localStorage');
    return true;
  } catch (error) {
    console.error('Error saving tab visibility settings:', error);
    throw error;
  }
};

/**
 * Check if a tab should be visible to a user with the given role
 * @param tabId The ID of the tab to check
 * @param userRole The role of the user
 * @param isSuperuser Whether the user is a superuser
 * @param tabSettings Tab visibility settings to check against
 * @returns Whether the tab should be visible
 */
export const isTabVisibleToUser = (
  tabId: string,
  userRole: string | undefined,
  isSuperuser: boolean,
  tabSettings: TabVisibility[]
): boolean => {
  // Superusers can see everything
  if (isSuperuser) return true;

  // Find the tab in settings
  const tab = tabSettings.find(t => t.id === tabId);
  if (!tab) return false;

  // Check visibility based on role
  if (userRole === 'admin' && tab.visibleTo.admin) return true;
  if (userRole === 'operator' && tab.visibleTo.operator) return true;
  if (userRole === 'viewer' && tab.visibleTo.viewer) return true;

  return false;
};