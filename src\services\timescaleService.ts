import { api } from './api';

// Common interfaces
interface Metadata {
  timestamp: string;
  version: string;
}

export interface ApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: Metadata;
  data: T;
}

// Interfaces for aggregated data
interface AggregatedDataPoint {
  timestamp: string;
  value: number;
}

interface AggregatedDataResponse {
  site_id: string;
  device_id: string;
  datapoint: string;
  model: string;
  values: AggregatedDataPoint[];
}

interface AggregatedDataQuery {
  site_id: string;
  device_id: string;
  datapoints: string[];
  start_timestamp: string;
  end_timestamp: string;
  resampling?: string;
}

interface DailyEnergyQuery {
  site_id: string;
  device_id: string;
  datapoints: string[];
  start_timestamp: string;
  end_timestamp: string;
}

export interface DailyEnergyDataPoint {
  timestamp: string;
  value: number;
}

export interface DailyEnergyDataResponse {
  timestamp: string;
  start_datetime: string;
  end_datetime: string;
  site_id: string;
  device_id: string;
  datapoint: string;
  value: number;
}

// Interface for energy usage data
interface HourlyBreakdown {
  timestamp: string;
  value: number;
}

interface EnergyUsageData {
  date: string;
  site_id: string;
  total_energy_usage: number;
  unit: string;
  hourly_breakdown: HourlyBreakdown[];
}

interface EnergyUsageQuery {
  site_id: string;
  device_id: string;
  start_timestamp: string;  // ISO format
  end_timestamp: string;    // ISO format
}

// Interface for weather data
interface WeatherData {
  timestamp: string;
  start_datetime: string;
  end_datetime: string;
  site_id: string;
  device_id: string;
  max_drybulb_temperature: number;
  max_wetbulb_temperature: number;
  max_humidity: number;
  min_drybulb_temperature: number;
  min_wetbulb_temperature: number;
  min_humidity: number;
  mean_drybulb_temperature: number;
  mean_wetbulb_temperature: number;
  mean_humidity: number;
  median_drybulb_temperature: number;
  median_wetbulb_temperature: number;
  median_humidity: number;
}

interface WeatherDataQuery {
  site_id: string;
  start_timestamp: string;
  end_timestamp: string;
}

export const fetchAggregatedData = async (query: AggregatedDataQuery): Promise<ApiResponse<AggregatedDataResponse[]>> => {
  try {
    const response = await api.post<ApiResponse<AggregatedDataResponse[]>>(
      '/historical_data/timescaledb/aggregated_data/query/',
      query,
      { timeout: 10000 }
    );

    if (!response.data.success) {
      throw new Error(response.data.message);
    }

    return response.data;
  } catch (err) {
    const error = err as { code?: string; message: string };
    console.error('Aggregated Data API Error:', error);
    
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out. Please try again.');
    }
    throw new Error(`API request failed: ${error.message}`);
  }
};

export const fetchHourlyData = async (query: AggregatedDataQuery): Promise<ApiResponse<AggregatedDataResponse[]>> => {
  try {
    const response = await api.post<ApiResponse<AggregatedDataResponse[]>>(
      '/historical_data/timescaledb/hourly_data/query/',
      query,
      { timeout: 5000 }
    );

    if (!response.data.success) {
      throw new Error(response.data.message);
    }

    return response.data;
  } catch (err) {
    const error = err as { code?: string; message: string };
    console.error('Hourly Data API Error:', error);
    
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out. Please try again.');
    }
    throw new Error(`API request failed: ${error.message}`);
  }
};

export const fetchDailyData = async (query: DailyEnergyQuery): Promise<ApiResponse<DailyEnergyDataResponse[]>> => {
  try {
    const response = await api.post<ApiResponse<DailyEnergyDataResponse[]>>(
      '/historical_data/timescaledb/daily_energy_data/query/',
      query,
      { timeout: 5000 }
    );

    if (!response.data.success) {
      throw new Error(response.data.message);
    }

    return response.data;
  } catch (err) {
    const error = err as { code?: string; message: string };
    console.error('Daily Energy Data API Error:', error);
    
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out. Please try again.');
    }
    throw new Error(`API request failed: ${error.message}`);
  }
};

export const fetchEnergyUsage = async (query: EnergyUsageQuery): Promise<ApiResponse<EnergyUsageData>> => {
  try {
    const response = await api.post<ApiResponse<EnergyUsageData>>(
      '/historical_data/energy/daily_usage/',
      query,
      { timeout: 5000 }
    );

    if (!response.data.success) {
      throw new Error(response.data.message);
    }

    return response.data;
  } catch (err) {
    const error = err as { code?: string; message: string };
    console.error('Energy Usage API Error:', error);
    
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out. Please try again.');
    }
    throw new Error(`API request failed: ${error.message}`);
  }
};

export const fetchDailyWeatherData = async (query: WeatherDataQuery): Promise<ApiResponse<WeatherData[]>> => {
  try {
    const response = await api.post<ApiResponse<WeatherData[]>>(
      '/historical_data/timescaledb/daily_weather_data/query/',
      query,
      { timeout: 5000 }
    );

    if (!response.data.success) {
      throw new Error(response.data.message);
    }

    return response.data;
  } catch (err) {
    const error = err as { code?: string; message: string };
    console.error('Daily Weather Data API Error:', error);
    
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out. Please try again.');
    }
    throw new Error(`API request failed: ${error.message}`);
  }
}; 