import <PERSON> from 'papaparse';

export const parseCSV = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      dynamicTyping: true,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          reject(results.errors);
          return;
        }

        // Process the data to ensure it has the required columns
        const data = results.data as any[];
        
        if (!data.length) {
          reject(new Error('CSV file contains no data'));
          return;
        }

        // Check if the data has any date column, more flexible detection
        const headers = Object.keys(data[0]).map(header => header.toLowerCase());
        const dateColumnNames = ['date', 'datetime', 'timestamp', 'time', 'day'];
        const dateColumnIndex = headers.findIndex(header => 
          dateColumnNames.some(dateCol => header.includes(dateCol))
        );
        
        if (dateColumnIndex === -1) {
          reject(new Error('CSV file must contain a date column (such as "date", "datetime", or "timestamp")'));
          return;
        }

        // Get the actual date column name (with original case)
        const actualDateColumnName = Object.keys(data[0])[dateColumnIndex];
        
        // Normalize the data to ensure it has a 'date' column
        if (actualDateColumnName.toLowerCase() !== 'date') {
          data.forEach(row => {
            row.date = row[actualDateColumnName];
          });
        }
        
        resolve(data);
      },
      error: (error) => {
        reject(error);
      }
    });
  });
};

export const exportToCSV = (data: any[], filename: string): void => {
  const csv = Papa.unparse(data);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};