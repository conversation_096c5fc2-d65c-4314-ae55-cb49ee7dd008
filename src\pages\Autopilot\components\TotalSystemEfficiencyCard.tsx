import React from 'react';
import { BarGauge } from "@/components/ui/bar-gauge";
import { useRealtime } from "@/contexts/RealtimeContext";

// Define constants for the gauge
const COLORS = ['#14B8B4', '#FEBE54', '#FF7A00', '#EF4337'];
const LABELS = ['Excellent', 'Good', 'Fair', 'Improve'];
const THRESHOLDS = [0.0, 0.7, 0.8, 0.9, 1.2];

const TotalSystemEfficiencyCard: React.FC = () => {
  // Use the realtime context to get data
  const { getValue } = useRealtime();
  
  // Get the TSE value from realtime context, or use mock data if not available
  const airSideEfficiency = getValue('air_distribution_system', 'efficiency');
  const waterSideEfficiency = getValue('plant', 'efficiency');
  // const tseValue = getValue('total_hvac_system', 'efficiency');
  const tseValue = (airSideEfficiency + waterSideEfficiency);
  
  return (
    <div className="w-full h-full p-[10px] alto-card">
      <div className="flex flex-col">
        {/* Title Row */}
        <div className="w-full flex justify-between items-center">
          <h2 className="text-card-foreground text-lg font-semibold tracking-[0.01em]">
            Total System Efficiency (TSE)
          </h2>
        </div>
        
        {/* BarGauge Container */}
        <div className="w-full flex flex-col justify-end items-center gap-0.5 min-h-[61px]">
          <div className="w-full max-w-full">
            <BarGauge
              labels={LABELS}
              colors={COLORS}
              value={tseValue}
              threshold={THRESHOLDS}
              showValue={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TotalSystemEfficiencyCard;
