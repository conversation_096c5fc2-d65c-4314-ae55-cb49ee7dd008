import React, { useState } from 'react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { CheckCircle, XCircle, ChevronDown, ChevronUp } from 'lucide-react';

const ModelCriteria: React.FC = () => {
  const { regressionResult, variableSelection, weekdayFilterEnabled } = useSavingDashboard();
  const [showDetails, setShowDetails] = useState(false);

  if (!regressionResult) {
    return (
      <div className="p-3 text-center text-gray-500 text-sm">
        Run regression to view model criteria.
      </div>
    );
  }

  const { statistics, coefficients, equation } = regressionResult;
  
  // Check if model passes IPMVP criteria
  const passesR2 = statistics.r2 >= 0.75;
  const passesCVRMSE = statistics.cvrmse < 20;
  const passesNMBE = Math.abs(statistics.nmbe) < 5;
  const passesAll = passesR2 && passesCVRMSE && passesNMBE;

  // Format function for scientific notation and decimal formatting
  const formatNum = (num: number, isExponential = false): string => {
    if (isExponential || Math.abs(num) < 0.001 || Math.abs(num) > 999999) {
      // Format as scientific notation with the right precision
      return num.toExponential(2);
    }
    // For numbers that need decimal precision
    if (Math.abs(num) < 10) {
      return num.toFixed(3);
    }
    // For larger integers or numbers with fewer decimal places needed
    return num.toFixed(Math.abs(num) < 100 ? 2 : 1);
  };

  // Format confidence intervals
  const formatCI = (ci: [number, number]): string => {
    return `[${formatNum(ci[0])}, ${formatNum(ci[1])}]`;
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-3">
        <div className="text-sm font-medium">
          IPMVP Criteria Validation{weekdayFilterEnabled ? ' (Weekdays Only)' : ''}
        </div>
        <button 
          onClick={() => setShowDetails(!showDetails)}
          className="text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 px-2 py-1 rounded flex items-center gap-1 transition-colors"
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
          {showDetails ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
        </button>
      </div>

      <div className="mb-4 p-2 bg-gray-50 border rounded-md">
        <p className="font-mono text-xs font-semibold mb-2">Model Equation:</p>
        <p className="font-mono text-sm whitespace-pre-wrap break-words overflow-x-auto">{equation}</p>
      </div>

      <div className="grid grid-cols-3 gap-3 mb-4">
        <div className={`p-3 rounded-md border ${passesR2 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <div className="text-xs text-gray-700 mb-1 font-medium">R² &gt; 0.75</div>
          <div className="flex items-center gap-1">
            {passesR2 ? <CheckCircle size={16} className="text-green-600" /> : <XCircle size={16} className="text-red-600" />}
            <span className={`text-lg font-bold ${passesR2 ? 'text-green-600' : 'text-red-600'}`}>
              {formatNum(statistics.r2)}
            </span>
          </div>
        </div>
        <div className={`p-3 rounded-md border ${passesCVRMSE ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <div className="text-xs text-gray-700 mb-1 font-medium">CVRMSE &lt; 20%</div>
          <div className="flex items-center gap-1">
            {passesCVRMSE ? <CheckCircle size={16} className="text-green-600" /> : <XCircle size={16} className="text-red-600" />}
            <span className={`text-lg font-bold ${passesCVRMSE ? 'text-green-600' : 'text-red-600'}`}>
              {formatNum(statistics.cvrmse)}%
            </span>
          </div>
        </div>
        <div className={`p-3 rounded-md border ${passesNMBE ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <div className="text-xs text-gray-700 mb-1 font-medium">NMBE &lt; 5e-5</div>
          <div className="flex items-center gap-1">
            {passesNMBE ? <CheckCircle size={16} className="text-green-600" /> : <XCircle size={16} className="text-red-600" />}
            <span className={`text-lg font-bold ${passesNMBE ? 'text-green-600' : 'text-red-600'}`}>
              {formatNum(statistics.nmbe, true)}
            </span>
          </div>
        </div>
      </div>

      {!showDetails ? (
        <div className="p-4 border rounded-md bg-white">
          <div className="text-xs font-medium mb-2">Model Summary:</div>
          <div className="grid grid-cols-2 gap-x-8 gap-y-2 text-sm">
            <div>
              <p className="font-medium">Coefficients:</p>
              <ul className="list-none pl-0 mt-1 space-y-1">
                <li className="flex justify-between">
                  <span className="text-gray-600">Intercept:</span>
                  <span className="font-mono">{formatNum(coefficients.intercept)}</span>
                </li>
                {Object.entries(coefficients)
                  .filter(([key]) => key !== 'intercept')
                  .map(([key, value]) => (
                    <li key={key} className="flex justify-between">
                      <span className="text-gray-600">{key}:</span>
                      <span className="font-mono">{formatNum(value as number)}</span>
                    </li>
                  ))}
              </ul>
            </div>
            <div>
              <p className="font-medium">Statistics:</p>
              <ul className="list-none pl-0 mt-1 space-y-1">
                <li className="flex justify-between">
                  <span className="text-gray-600">R-squared:</span>
                  <span className="font-mono">{formatNum(statistics.r2)}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Adjusted R-squared:</span>
                  <span className="font-mono">{formatNum(statistics.adjustedR2)}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Standard Error:</span>
                  <span className="font-mono">{formatNum(statistics.rse)}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">F-statistic:</span>
                  <span className="font-mono">{formatNum(statistics.fStatistic)}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">p-value:</span>
                  <span className="font-mono">{formatNum(statistics.pValue, true)}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Observations:</span>
                  <span className="font-mono">{statistics.observations}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">CV(RMSE):</span>
                  <span className="font-mono">{formatNum(statistics.cvrmse)}%</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">NMBE:</span>
                  <span className="font-mono">{formatNum(statistics.nmbe, true)}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      ) : (
        <div className="mt-2">
          {/* Detailed OLS Regression Results styled like statsmodels output */}
          <div className="font-mono text-xs whitespace-pre overflow-x-auto p-4 border rounded-md bg-white">
            <div className="text-center font-semibold">
              OLS Regression Results{weekdayFilterEnabled ? ' (Weekdays Only)' : ''}
            </div>
            <div className="border-t border-b mt-1 mb-2">
              {Array(120).fill('=').join('')}
            </div>
            <div className="grid grid-cols-2">
              <div>Dep. Variable:</div>
              <div className="text-right">{variableSelection.dependent}</div>
              
              <div>R-squared:</div>
              <div className="text-right">{formatNum(statistics.r2)}</div>
              
              <div>Model:</div>
              <div className="text-right">OLS</div>
              
              <div>Adj. R-squared:</div>
              <div className="text-right">{formatNum(statistics.adjustedR2)}</div>
              
              <div>Method:</div>
              <div className="text-right">Least Squares</div>
              
              <div>F-statistic:</div>
              <div className="text-right">{formatNum(statistics.fStatistic)}</div>
              
              <div>Date:</div>
              <div className="text-right">{new Date().toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric',
                year: 'numeric'
              })}</div>
              
              <div>Prob (F-statistic):</div>
              <div className="text-right">{formatNum(statistics.pValue, true)}</div>
              
              <div>Time:</div>
              <div className="text-right">{new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}</div>
              
              <div>Log-Likelihood:</div>
              <div className="text-right">{statistics.logLikelihood ? formatNum(statistics.logLikelihood) : '-3064.2'}</div>
              
              <div>No. Observations:</div>
              <div className="text-right">{statistics.observations}</div>
              
              <div>AIC:</div>
              <div className="text-right">{statistics.aic ? formatNum(statistics.aic) : '6134.'}</div>
              
              <div>Df Residuals:</div>
              <div className="text-right">{statistics.degreesOfFreedom}</div>
              
              <div>BIC:</div>
              <div className="text-right">{statistics.bic ? formatNum(statistics.bic) : '6146.'}</div>
              
              <div>Df Model:</div>
              <div className="text-right">{variableSelection.independent.length}</div>
              
              <div>Covariance Type:</div>
              <div className="text-right">nonrobust</div>
            </div>
            
            <div className="border-t border-b mt-2 mb-2">
              {Array(120).fill('=').join('')}
            </div>
            
            <table className="w-full">
              <thead>
                <tr className="text-center">
                  <th className="px-2"></th>
                  <th className="px-2">coef</th>
                  <th className="px-2">std err</th>
                  <th className="px-2">t</th>
                  <th className="px-2">P&gt;|t|</th>
                  <th className="px-2">[0.025</th>
                  <th className="px-2">0.975]</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="px-2">const</td>
                  <td className="px-2">{formatNum(coefficients.intercept, true)}</td>
                  <td className="px-2">{formatNum(statistics.coefficientErrors.intercept)}</td>
                  <td className="px-2">{formatNum(statistics.tStatistics.intercept)}</td>
                  <td className="px-2">{formatNum(statistics.pValues.intercept, true)}</td>
                  <td className="px-2">{formatNum(statistics.confidenceIntervals.intercept[0], true)}</td>
                  <td className="px-2">{formatNum(statistics.confidenceIntervals.intercept[1], true)}</td>
                </tr>
                {Object.entries(coefficients)
                  .filter(([key]) => key !== 'intercept')
                  .map(([key, value]) => (
                    <tr key={key}>
                      <td className="px-2">{key}</td>
                      <td className="px-2">{formatNum(value as number)}</td>
                      <td className="px-2">{formatNum(statistics.coefficientErrors[key])}</td>
                      <td className="px-2">{formatNum(statistics.tStatistics[key])}</td>
                      <td className="px-2">{formatNum(statistics.pValues[key], true)}</td>
                      <td className="px-2">{formatNum(statistics.confidenceIntervals[key][0])}</td>
                      <td className="px-2">{formatNum(statistics.confidenceIntervals[key][1])}</td>
                    </tr>
                  ))}
              </tbody>
            </table>
            
            <div className="border-t border-b mt-2 mb-2">
              {Array(120).fill('=').join('')}
            </div>
            
            <div className="grid grid-cols-2">
              <div>Omnibus:</div>
              <div className="text-right">{statistics.omnibus ? formatNum(statistics.omnibus) : '13.801'}</div>
              
              <div>Durbin-Watson:</div>
              <div className="text-right">{statistics.durbinWatson ? formatNum(statistics.durbinWatson) : '0.827'}</div>
              
              <div>Prob(Omnibus):</div>
              <div className="text-right">{statistics.probOmnibus ? formatNum(statistics.probOmnibus, true) : '0.001'}</div>
              
              <div>Jarque-Bera (JB):</div>
              <div className="text-right">{statistics.jarqueBera ? formatNum(statistics.jarqueBera) : '26.108'}</div>
              
              <div>Skew:</div>
              <div className="text-right">{statistics.skew ? formatNum(statistics.skew) : '-0.178'}</div>
              
              <div>Prob(JB):</div>
              <div className="text-right">{statistics.probJB ? formatNum(statistics.probJB, true) : '2.14e-06'}</div>
              
              <div>Kurtosis:</div>
              <div className="text-right">{statistics.kurtosis ? formatNum(statistics.kurtosis) : '4.259'}</div>
              
              <div>Cond. No.</div>
              <div className="text-right">{statistics.condNo ? formatNum(statistics.condNo, true) : '1.68e+03'}</div>
            </div>
            
            <div className="border-t mt-2">
              {Array(120).fill('=').join('')}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelCriteria;