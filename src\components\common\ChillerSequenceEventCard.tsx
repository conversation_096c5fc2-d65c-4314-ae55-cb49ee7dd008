import React, { useState, useEffect } from 'react';
import BaseEventCard, { BaseEventCardProps } from './BaseEventCard';
import { cn } from '@/lib/utils';
import { StartChillerSequenceEvent, StopChillerSequenceEvent } from '@/services/actionService';
import { format, parseISO } from 'date-fns';

// Helper function (can be moved to utils if used elsewhere)
const formatRemainingTimeDisplay = (timeString: string): React.ReactNode => {
  const [hours, minutes, seconds] = timeString.split(':').map(Number);
  if (hours === 0 && minutes === 0 && seconds === 0) return "Now";
  if (hours > 0) return `in ${hours} ${hours === 1 ? 'hr' : 'hrs'} ${minutes} ${minutes === 1 ? 'min' : 'mins'}`;
  if (minutes > 0) return `in ${minutes} ${minutes === 1 ? 'min' : 'mins'} ${seconds} ${seconds === 1 ? 'sec' : 'secs'}`;
  return `in ${seconds} ${seconds === 1 ? 'sec' : 'secs'}`;
};

const formatDeviceName = (deviceId: string): string => {
  return deviceId.replace('chiller', 'CH').toUpperCase().replace(/_/g, '-');
};

interface DeviceTag {
  id: string;
  name: string;
}

// Props specific to this card, using the specific event types
interface ChillerSequenceEventCardProps {
  event: StartChillerSequenceEvent | StopChillerSequenceEvent;
  // Props needed by BaseEventCard shell
  isActive?: boolean;
  showHourMarker?: boolean;
  time: string; // HH:mm format
  // Props needed for internal content rendering
  remainingTime: string; // H:MM:SS format for calculation
  eventStatus: 'pending' | 'in-progress' | 'completed';
  className?: string;
}

// Helper function to format seconds to MM:SS
const formatSecondsToMMSS = (totalSeconds: number): string => {
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = Math.floor(totalSeconds % 60); // Use floor to avoid decimals
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

const ChillerSequenceEventCard: React.FC<ChillerSequenceEventCardProps> = ({
  event,
  isActive = false,
  showHourMarker = false,
  time,
  remainingTime,
  eventStatus,
  className,
}) => {
  // State for current time to update countdown
  const [now, setNow] = useState(new Date());

  useEffect(() => {
    // Only run timer if the event is in progress and in stopping_delay stage
    if (eventStatus === 'in-progress' && event.payload.stage === 'stopping_delay') {
      const timerId = setInterval(() => {
        setNow(new Date());
      }, 1000); // Update every second

      return () => clearInterval(timerId); // Cleanup timer on unmount or stage change
    }
  }, [eventStatus, event.payload.stage]);

  // --- Calculate specific content --- 
  const chillerId = event.payload.chiller_id || 'unknown_chiller';
  const chillerName = formatDeviceName(chillerId);
  const postCirculation = event.payload.post_circulation
  const postCirculationDelay = event.payload.post_circulation_delay
  const title = event.action_type === 'start_chiller_sequence' ? `Start ${chillerName}` : `Stop ${chillerName}`; 
  const equipmentTags = (event.payload.group_equipment || []).map(equip => ({
    id: equip,
    name: formatDeviceName(equip),
  }));
  const deviceList: DeviceTag[] = [{ id: chillerId, name: chillerName }, ...equipmentTags];

  const scheduledTime = parseISO(event.scheduled_time);
  // Initial planned time (may be modified for post_circulation)
  let plannedTime = format(scheduledTime, 'HH:mm'); 

  // Modify plannedTime if status is pending and post_circulation is true
  if (eventStatus === 'pending' && postCirculation === true && postCirculationDelay) {
    try {
      // Convert post_circulation_delay from seconds to minutes
      const otMinutes = Math.floor(postCirculationDelay / 60);
      plannedTime = `${plannedTime} (+${otMinutes} minutes OT)`;
    } catch (e) {
      console.error("Error formatting post_circulation time for pending event:", e);
      // Keep original plannedTime on error
    }
  }

  const sequenceStage = (() => {
    const stage = event.payload.stage;
    switch (stage) {
      case 'standby':
        return 'Standby';
      case 'starting_valve_and_pump':
        return 'Starting Valve & Pump';
      case 'starting_chiller':
        return 'Starting Chiller';
      case 'running':
        return 'Running';
      case 'stopping_chiller':
        return 'Stopping Chiller';
      case 'stopping_delay':
        // Calculate countdown for stopping_delay
        const stopTimestamp = event.payload.chiller_stop_timestamp;
        const stoppingDelay = event.payload.chiller_stopping_delay;
        if (stopTimestamp && stoppingDelay) {
          const endTimeSeconds = stopTimestamp + stoppingDelay;
          const nowSeconds = now.getTime() / 1000;
          const remainingDelaySeconds = Math.max(0, endTimeSeconds - nowSeconds);
          const countdown = formatSecondsToMMSS(remainingDelaySeconds);
          return `Stopping Delay (${countdown})`;
        }
        return 'Stopping Delay'; // Fallback if timestamps missing
      case 'post_circulation':
        // Modify plannedTime display for post_circulation
        const circulateStart = event.payload.post_circulation_start_timestamp;
        const circulateDelay = event.payload.post_circulation_delay;
        if (circulateStart && circulateDelay) {
          try {
            const circulateEndTime = new Date((circulateStart + circulateDelay) * 1000); 
            const circulateEndTimeFormatted = format(circulateEndTime, 'HH:mm');
            plannedTime = `${plannedTime} (Circulate until ${circulateEndTimeFormatted})`;
          } catch (e) {
            console.error("Error formatting post_circulation end time:", e);
            // Keep original plannedTime on error
          }
        }
        return 'Post Circulation'; // Return stage name
      case 'stopping_valve_and_pump':
        return 'Stopping Valve & Pump';
      default:
        return 'In Progress';
    }
  })();

  return (
    <BaseEventCard 
      time={time} 
      isActive={isActive} 
      showHourMarker={showHourMarker} 
      className={className}
    >
      {/* --- Render card content here --- */}
      <div className="flex flex-col gap-1.5">
        {/* Top Row - Time Info & Status */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-1">
            {/* Clock Icon */}
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" className={isActive ? "text-card" : "text-muted-foreground"}>
               <path fillRule="evenodd" clipRule="evenodd" d="M2.91666 6.99996C2.91666 4.74474 4.74477 2.91663 6.99999 2.91663C9.25521 2.91663 11.0833 4.74474 11.0833 6.99996C11.0833 9.25518 9.25521 11.0833 6.99999 11.0833C4.74477 11.0833 2.91666 9.25518 2.91666 6.99996ZM6.99999 3.56136C5.10085 3.56136 3.56139 5.10082 3.56139 6.99996C3.56139 8.8991 5.10085 10.4386 6.99999 10.4386C8.89913 10.4386 10.4386 8.8991 10.4386 6.99996C10.4386 5.10082 8.89913 3.56136 6.99999 3.56136Z" fill="currentColor"/>
               <path fillRule="evenodd" clipRule="evenodd" d="M7.00181 4.42114C7.17985 4.42114 7.32418 4.56547 7.32418 4.74351V6.86826L8.42562 7.96983C8.55151 8.09573 8.55149 8.29985 8.42559 8.42573C8.29969 8.55162 8.09558 8.5516 7.9697 8.4257L6.77385 7.22972C6.7134 7.16926 6.67944 7.08727 6.67944 7.00178V4.74351C6.67944 4.56547 6.82377 4.42114 7.00181 4.42114Z" fill="currentColor"/>
             </svg>
            <span className={cn("text-xs", isActive ? "text-card" : "text-muted-foreground")}>
              {plannedTime}
            </span>
          </div>
          {/* Status/Remaining Time */}
          <span className={cn("text-xs font-semibold", isActive ? "text-card" : "text-primary")}>
             {eventStatus === 'pending' ? formatRemainingTimeDisplay(remainingTime) :
              eventStatus === 'in-progress' ? (
                // Apply shimmer effect to sequenceStage text
                <span className={cn(
                  "animate-shimmer", // Base animation class
                  isActive ? "shimmer-active" : "shimmer-active-secondary" // Conditional gradient
                )}>
                  {sequenceStage}
                </span>
              ) : (
                'Completed'
              )}
           </span>
        </div>

        {/* Bottom Row - Title and Device List */}
        <div className="flex justify-between items-center">
          <span className={cn("text-sm font-semibold", isActive ? "text-card" : "text-primary")}>
            {title}
          </span>
          <div className="flex items-center gap-1 w-[185px] overflow-x-auto justify-end">
            {deviceList.map((device) => (
              <div
                key={device.id}
                className={cn(
                  "h-4 px-0.5 rounded inline-flex items-center",
                  isActive ? "bg-[#C0E2FF]" : "bg-card"
                )}
              >
                <span className={cn(
                  "text-[9px] leading-none whitespace-nowrap text-[#052745]"
                )}>
                  {device.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </BaseEventCard>
  );
};

export default ChillerSequenceEventCard;
