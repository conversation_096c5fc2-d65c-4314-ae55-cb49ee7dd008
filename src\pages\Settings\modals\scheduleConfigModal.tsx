// Schedule Configuration Modal Component
// Handles configuration of schedule name and the list of device/datapoint control pairs

import React, { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Plus, Trash2 } from 'lucide-react';
import { toast } from "@/components/ui/use-toast";
import { ApiSchedule, ControlPair, WeeklySchedule, TimeRange } from '../types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ScheduleConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  schedule: ApiSchedule | null;
  deviceOptions: { id: string, name: string }[];
  onSave: (schedule: ApiSchedule) => void;
  onDelete?: (scheduleId: string) => void;
}

export function ScheduleConfigModal({
  open,
  onOpenChange,
  schedule,
  deviceOptions,
  onSave,
  onDelete
}: ScheduleConfigModalProps) {
  // Form state
  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [active, setActive] = useState<boolean>(false);
  const [controls, setControls] = useState<ControlPair[]>([]);
  
  // Reset form state when schedule changes
  useEffect(() => {
    if (schedule) {
      setName(schedule.name);
      setDescription(schedule.description || '');
      setActive(schedule.active);
      setControls(schedule.controls || []);
    } else {
      // Default values for new schedule
      setName('');
      setDescription('');
      setActive(true);
      setControls([]);
    }
  }, [schedule, open]);

  const handleAddControl = () => {
    if (deviceOptions.length === 0) {
      toast({
        title: "No devices available",
        description: "Please add devices before configuring controls.",
        variant: "destructive"
      });
      return;
    }

    // Add a new empty control
    const newControl: ControlPair = {
      device_id: deviceOptions[0].id,
      datapoint: 'status_write' // Default datapoint
    };
    
    setControls([...controls, newControl]);
  };

  const handleRemoveControl = (index: number) => {
    const newControls = [...controls];
    newControls.splice(index, 1);
    setControls(newControls);
  };

  const handleControlChange = (index: number, field: keyof ControlPair, value: string) => {
    const newControls = [...controls];
    newControls[index] = { ...newControls[index], [field]: value };
    setControls(newControls);
  };

  const handleDelete = () => {
    if (onDelete && schedule?.id) {
      onDelete(schedule.id);
      onOpenChange(false);
    }
  };

  const handleSave = () => {
    // Validate form
    if (!name) {
      toast({
        title: "Missing Name",
        description: "Please provide a name for the schedule.",
        variant: "destructive"
      });
      return;
    }

    if (controls.length === 0) {
      toast({
        title: "No Controls Added",
        description: "Please add at least one control to the schedule.",
        variant: "destructive"
      });
      return;
    }

    // Ensure we have a valid ID (needed for type compatibility)
    if (!schedule || !schedule.id) {
      toast({
        title: "Invalid Schedule",
        description: "Cannot save schedule without an ID.",
        variant: "destructive"
      });
      return;
    }

    // Create schedule object with current state
    const updatedSchedule: ApiSchedule = {
      id: schedule.id,
      name,
      description,
      active,
      controls,
      // Preserve existing weekly schedule if it exists
      weekly_schedule: schedule.weekly_schedule || {
        // Create empty weekly schedule if none exists
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: []
      },
      special_days: schedule.special_days || []
    };

    // Call onSave and close modal
    onSave(updatedSchedule);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-white border border-[#DBE4FF] rounded-lg shadow-[1px_3px_20px_0px_rgba(57,124,221,0.30)] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-[#065BA9] text-lg font-semibold">
            {schedule?.id ? 'Edit Schedule' : 'Create New Schedule'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-2 overflow-y-auto">
          {/* Name input */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-[#065BA9]">Schedule Name</Label>
            <Input
              id="name"
              placeholder="Enter schedule name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="border-[#DBE4FF] focus:border-[#0E7EE4] focus:ring-[#0E7EE4]/10"
            />
          </div>

          {/* Description textarea */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-[#065BA9]">Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="Enter schedule description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="h-20 resize-none border-[#DBE4FF] focus:border-[#0E7EE4] focus:ring-[#0E7EE4]/10"
            />
          </div>

          {/* Active switch */}
          <div className="flex items-center justify-between">
            <Label htmlFor="active" className="text-[#065BA9]">Active</Label>
            <Switch
              id="active"
              checked={active}
              onCheckedChange={setActive}
            />
          </div>

          {/* Controls section */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-[#065BA9]">Controls</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddControl}
                className="text-[#0E7EE4] border-[#DBE4FF] hover:bg-[#F9FAFF] hover:border-[#0E7EE4]"
              >
                <Plus className="h-4 w-4 mr-1" /> Add Control
              </Button>
            </div>

            {controls.length === 0 ? (
              <div className="text-sm text-muted-foreground p-4 text-center bg-[#F9FAFF] rounded-md">
                No controls added yet. Click "Add Control" to start.
              </div>
            ) : (
              <div className="space-y-3 max-h-[40vh] overflow-y-auto pr-1">
                {controls.map((control, index) => (
                  <div key={index} className="flex items-center gap-2 p-3 border border-[#DBE4FF] rounded-md bg-[#F9FAFF]">
                    <div className="grid grid-cols-2 gap-2 flex-1">
                      {/* Device ID selection */}
                      <div>
                        <Label className="text-xs text-[#065BA9] mb-1 block">Device</Label>
                        <Select
                          value={control.device_id}
                          onValueChange={(value) => handleControlChange(index, 'device_id', value)}
                        >
                          <SelectTrigger className="w-full text-sm border-[#DBE4FF] hover:border-[#0E7EE4] hover:bg-[#F9FAFF]">
                            <SelectValue placeholder="Select device" />
                          </SelectTrigger>
                          <SelectContent>
                            {deviceOptions.map(device => (
                              <SelectItem key={device.id} value={device.id}>
                                {device.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      {/* Datapoint input */}
                      <div>
                        <Label className="text-xs text-[#065BA9] mb-1 block">Datapoint</Label>
                        <Input
                          value={control.datapoint}
                          onChange={(e) => handleControlChange(index, 'datapoint', e.target.value)}
                          className="text-sm border-[#DBE4FF]"
                          placeholder="Enter datapoint"
                        />
                      </div>
                    </div>
                    
                    {/* Remove button */}
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveControl(index)}
                      className="h-8 w-8 text-red-500 hover:bg-red-50 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="pt-2 mt-auto">
          {schedule?.id && onDelete && (
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              className="mr-auto"
            >
              Delete Schedule
            </Button>
          )}
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="text-[#065BA9] border-[#DBE4FF] hover:bg-[#F9FAFF] hover:border-[#0E7EE4]"
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            className="bg-[#0E7EE4] text-white hover:bg-[#0E7EE4]/90"
          >
            {schedule?.id ? 'Update Schedule' : 'Create Schedule'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}