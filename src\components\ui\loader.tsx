import React from "react";
import { cn } from "@/lib/utils";

interface LoaderProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg";
}

const Loader = React.forwardRef<HTMLDivElement, LoaderProps>(
  ({ className, size = "md", ...props }, ref) => {
    const sizeClasses = {
      sm: "w-5 h-5",
      md: "w-10 h-10",
      lg: "w-16 h-16",
    };

    return (
      <div
        ref={ref}
        className={cn("relative", className)}
        {...props}
      >
        <div 
          className={cn(
            "rounded-full animate-spin",
            sizeClasses[size],
            "bg-conic-gradient",
            "blur-[3px]"
          )}
          style={{
            background: "conic-gradient(from 90deg at 50% 50%, rgba(6, 91, 169, 0.8) 0deg, #065BA9 20deg, rgba(6, 91, 169, 0.5) 180deg, rgba(6, 91, 169, 0) 270deg 360deg)",
            WebkitMask: "radial-gradient(transparent 45%, black 46%)",
            mask: "radial-gradient(transparent 45%, black 46%)"
          }}
        />
      </div>
    );
  }
);

Loader.displayName = "Loader";

export { Loader };
