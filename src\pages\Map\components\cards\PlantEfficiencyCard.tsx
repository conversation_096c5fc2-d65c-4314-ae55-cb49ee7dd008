import { useRealtime } from "@/contexts/RealtimeContext";
import { BarGauge } from "@/components/ui/bar-gauge";

const COLORS = ['#14B8B4', '#FEBE54', '#FF7A00', '#EF4337'];
const LABELS = ['Excellent', 'Good', 'Fair', 'Improve'];
const THRESHOLDS = [0.4, 0.7, 0.85, 1.0, 1.2];


export function PlantEfficiencyCard() {
  // Mock data waiting for real-time API
  const { getValue } = useRealtime();
  const efficiency = getValue('plant', 'efficiency');
  const heatBalance = getValue('plant', 'heat_balance');

  return (
    <div className="w-full h-full p-[8px] alto-card inline-flex justify-start items-center gap-2.5">
      <div className="flex-1 flex flex-col justify-start items-start w-full">
        {/* Title Row */}
        <div className="w-full flex justify-between items-center mt-[-15px]">
          <div className="text-[#065BA9] text-sm font-semibold font-inter tracking-[0.01em]">
            Plant Efficiency
          </div>
          <div className="text-[#788796] text-[10px] font-normal font-inter">
            Heat Balance: {heatBalance == null ? '-' : heatBalance.toFixed(1) + ' %'}
          </div>
        </div>
  
        {/* BarGauge Container */}
        <div className="w-full flex flex-col justify-end items-center gap-0.5 min-h-[61px]">
          <div className="w-full max-w-[276px]">
            <BarGauge
              labels={LABELS}
              colors={COLORS}
              value={efficiency}
              threshold={THRESHOLDS}
              showValue={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
}