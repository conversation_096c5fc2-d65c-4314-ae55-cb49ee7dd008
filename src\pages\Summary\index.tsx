import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "../../contexts/AuthContext";
import {
  ConsumptionChart,
  EfficiencyHeatmap,
  EfficiencySummary,
  EnergyUsageSummary,
  ComparisonByDate,
} from "./components";

import { DateRange } from "react-day-picker";
import { DatePickerWithRange } from "@/components/ui/date-range-picker"; 
import { Loader } from "@/components/ui/loader"; 

import { DateTime } from "luxon";
import { fetchDailyData } from "../../services/timescaleService";
import { getSiteId } from "../../services/authService";
import { AlertCircle } from "lucide-react";

interface DailyDataItem {
  value: number;
  timestamp: string;
}

interface SummaryMetrics {
  totalEnergy: number;
  totalCooling: number;
  totalEfficiency: number;
  plantEfficiency: number;
  plantPower: number;
  plantLoad: number;
  airEfficiency: number;
  airPower: number;
}

const calculateSummaryMetrics = (
  plantEnergyData: DailyDataItem[],
  airEnergyData: DailyDataItem[],
  dailyCoolingData: DailyDataItem[]
): SummaryMetrics => {
  // Calculate total values from daily data by summing all values in the arrays
  const plantPower = plantEnergyData.reduce((sum, item) => sum + item.value, 0);
  const airPower = airEnergyData.reduce((sum, item) => sum + item.value, 0);
  
  const totalEnergy = plantPower + airPower;
  const totalCooling = dailyCoolingData.reduce((sum, item) => sum + item.value, 0);
  
  // Calculate efficiencies
  const plantEfficiency = plantPower > 0 ? plantPower / totalCooling : 0;
  const airEfficiency = airPower > 0 ? airPower / totalCooling : 0;
  const totalEfficiency = plantEfficiency + airEfficiency;

  // Calculate average load value
  const plantLoad = dailyCoolingData.length > 0 
    ? totalCooling / dailyCoolingData.length
    : 0;

  return {
    totalEnergy: parseFloat(totalEnergy.toFixed(2)),
    totalCooling: parseFloat(totalCooling.toFixed(2)),
    totalEfficiency: parseFloat(totalEfficiency.toFixed(2)),
    plantEfficiency: parseFloat(plantEfficiency.toFixed(2)),
    plantPower: parseFloat(plantPower.toFixed(2)),
    plantLoad: parseFloat(plantLoad.toFixed(2)),
    airEfficiency: parseFloat(airEfficiency.toFixed(2)),
    airPower: parseFloat(airPower.toFixed(2)),
  };
};

const Summary: React.FC = () => {
  const { site } = useAuth();
  const timezone = site?.timezone || 'Asia/Bangkok';
  const today = DateTime.now().setZone(timezone);
  const oneWeekAgo = today.minus({ days: 7 });

  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: oneWeekAgo.toJSDate(),
    to: today.toJSDate(),
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Add state for summary metrics
  const [summaryMetrics, setSummaryMetrics] = useState<SummaryMetrics>({
    totalEnergy: 0,
    totalCooling: 0,
    totalEfficiency: 0,
    plantEfficiency: 0,
    plantPower: 0,
    plantLoad: 0,
    airEfficiency: 0,
    airPower: 0,
  });

  // Function to fetch summary metrics using daily data
  const fetchSummaryMetrics = useCallback(async (start: Date, end: Date) => {
    if (!start || !end) return;
    
    try {
      setLoading(true);
      setError(null);
      const siteId = getSiteId() || '';
      
      // Convert dates to timezone-aware DateTime objects and then to strings
      const timezone = site?.timezone || 'Asia/Bangkok';
      const startDateTime = DateTime.fromJSDate(start).setZone(timezone).startOf('day');
      const endDateTime = DateTime.fromJSDate(end).setZone(timezone).endOf('day');
      const startIso = startDateTime.toString();
      const endIso = endDateTime.toString();
      
      // Make parallel API calls with individual error handling
      const [plantResponse, airResponse] = await Promise.allSettled([
        fetchDailyData({
          site_id: siteId,
          device_id: 'plant',
          datapoints: ['daily_energy', 'daily_cooling_energy'],
          start_timestamp: startIso,
          end_timestamp: endIso
        }),
        fetchDailyData({
          site_id: siteId,
          device_id: 'air_distribution_system',
          datapoints: ['daily_energy'],
          start_timestamp: startIso,
          end_timestamp: endIso
        })
      ]);

      // Handle individual API responses
      const validPlantResponse = plantResponse.status === 'fulfilled' ? plantResponse.value : { success: false, data: null };
      const validAirResponse = airResponse.status === 'fulfilled' ? airResponse.value : { success: false, data: null };

      // Check if all requests failed
      if (plantResponse.status === 'rejected' && airResponse.status === 'rejected') {
        throw new Error('All API requests failed');
      }

      // Set warning if some requests failed
      if (plantResponse.status === 'rejected' || airResponse.status === 'rejected') {
        setError("Some data couldn't be loaded. Results may be incomplete.");
      }
      
      // Process summary metrics if we have any valid data
      if (validPlantResponse.success || validAirResponse.success) {
        const plantEnergyData = validPlantResponse.success && Array.isArray(validPlantResponse.data) ? 
          validPlantResponse.data.filter(item => item.datapoint === 'daily_energy').map(item => ({
            value: item.value,
            timestamp: item.timestamp
          })) : [];
        const airEnergyData = validAirResponse.success && Array.isArray(validAirResponse.data) ? 
          validAirResponse.data.filter(item => item.datapoint === 'daily_energy').map(item => ({
            value: item.value,
            timestamp: item.timestamp
          })) : [];
        const dailyCoolingData = validPlantResponse.success && Array.isArray(validPlantResponse.data) ? 
          validPlantResponse.data.filter(item => item.datapoint === 'daily_cooling_energy').map(item => ({
            value: item.value,
            timestamp: item.timestamp
          })) : [];

        console.log(validPlantResponse.data);

        // Calculate metrics using the extracted function
        const metrics = calculateSummaryMetrics(
          plantEnergyData,
          airEnergyData,
          dailyCoolingData
        );
        console.log(metrics);
        setSummaryMetrics(metrics);
      }
    } catch (error) {
      console.error("Error fetching summary metrics:", error);
      setError("Failed to fetch metrics. Showing previous values.");
      // Keep previous values instead of resetting to zero
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Fetch metrics on mount and when dateRange changes
  useEffect(() => {
    // Check if dateRange and its properties exist before fetching
    if (dateRange?.from && dateRange?.to) {
      fetchSummaryMetrics(dateRange.from, dateRange.to);
    } 
  }, [dateRange, fetchSummaryMetrics]);

  // Define the handler for the shadcn DateRangePicker
  const handleDateChange = (newDateRange: DateRange | undefined) => {
    setDateRange(newDateRange);
    // Fetching is handled by the useEffect hook watching dateRange
  };

  return (
    <div className="w-full p-4 bg-[#f9faff]">
      {/* Main two-column layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-2">
        {/* Left Column */}
        <div className="flex flex-col gap-2">
          <div className="rounded-lg alto-card p-2 relative">
            {loading && (
              <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
                <Loader />
              </div>
            )}
            <div className="flex items-center justify-between mb-1">
              <p className="text-[#065BA9] text-[18px] font-semibold">
                System Summary
              </p>
              <div className="flex items-center gap-2">
                <DatePickerWithRange 
                  date={dateRange} 
                  onDateChange={handleDateChange} 
                />
              </div>
            </div>
            {error && (
              <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 p-2 rounded mb-2">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            )}
            <div className="flex w-full items-center gap-2 pt-2">
              {/* Energy Usage Summary */}
              {/* <CoolingLoadSummary
                plantLoad={loading ? 0 : summaryMetrics.plantLoad || 0}
              /> */}
              <EnergyUsageSummary
                plantPower={loading ? 0 : summaryMetrics.plantPower || 0}
                airPower={loading ? 0 : summaryMetrics.airPower || 0}
                totalPower={loading ? 0 : (summaryMetrics.plantPower + summaryMetrics.airPower) || 0}
              />
              <EfficiencySummary
                plantEfficiency={loading ? 0 : summaryMetrics.plantEfficiency || 0}
                airEfficiency={loading ? 0 : summaryMetrics.airEfficiency || 0}
                totalEfficiency={loading ? 0 : summaryMetrics.totalEfficiency || 0}
              />
            </div>
          </div>
          {/* Consumption Chart */}
          <ConsumptionChart />
          {/* Efficiency Heatmap */}
          <EfficiencyHeatmap />
        </div>

        {/* Right Column */}
        <div className="flex flex-col gap-6">
          {/* ComparisonByDate now manages its own data */}
          <ComparisonByDate dateRange={dateRange} />
        </div>
      </div>
    </div>
  );
};

export default Summary;