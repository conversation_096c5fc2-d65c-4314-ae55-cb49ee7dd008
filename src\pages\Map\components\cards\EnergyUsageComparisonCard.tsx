import { useEffect, useState, useRef } from "react";
import { useRealtime } from "@/contexts/RealtimeContext";
import { useAuth } from "@/contexts/AuthContext";
import { fetchDailyData, fetchDailyWeatherData, fetchEnergyUsage } from '@/services/timescaleService';
import { getSiteId } from '@/services/authService';
import { DateTime } from 'luxon';

interface UsageData {
  label: string;
  value: number | null;
  unit: string;
  dbt: number | null;
  humidity: number | null;
}

export function EnergyUsageComparisonCard() {
  const { getValue } = useRealtime();
  const { site } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<UsageData[]>([
    {
      label: 'Yesterday',
      value: null,
      unit: 'kWh',
      dbt: null,
      humidity: null
    },
    {
      label: 'Today',
      value: null,
      unit: 'kWh',
      dbt: null,
      humidity: null
    }
  ]);

  // Use a ref to store the getValue function to avoid too frequent dependency updates
  const getValueRef = useRef(getValue);
  
  // Update the ref when getValue changes
  useEffect(() => {
    getValueRef.current = getValue;
  }, [getValue]);
  
  // Main data fetching effect without getValue dependency
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get timezone from AuthContext or default to Asia/Bangkok
        const timezone = site?.timezone || 'Asia/Bangkok';
        
        // Get current time in the site's timezone using Luxon
        const now = DateTime.now().setZone(timezone);
        
        // Create today at start of day in the site's timezone
        const today_start = now.startOf('day');
        const today_end = now.endOf('day');
        
        // Create yesterday at start/end of day in the site's timezone
        const yesterday_start = now.minus({ days: 1 }).startOf('day');
        const yesterday_end = now.minus({ days: 1 }).endOf('day');

        const siteId = getSiteId() || '';
        
        // Initialize default values
        let yesterdayPlantEnergy = null;
        let yesterdayDbt = null;
        let yesterdayHumidity = null;
        let todayPlantEnergy = null;
        
        try {
          // Fetch all data in parallel
          const [
            todayPlantResponse,
            yesterdayPlantResponse,
            yesterdayWeatherResponse
          ] = await Promise.all([
            // Fetch today's plant energy
            fetchEnergyUsage({
              site_id: siteId,
              device_id: 'plant',
              start_timestamp: today_start.toString(),
              end_timestamp: today_end.toString()
            }),
            
            // Fetch yesterday's plant energy
            fetchDailyData({
              site_id: siteId,
              device_id: 'plant',
              datapoints: ['daily_energy'],
              start_timestamp: yesterday_start.toString(),
              end_timestamp: yesterday_end.toString()
            }),
            
            // Fetch yesterday's weather data
            fetchDailyWeatherData({
              site_id: siteId,
              start_timestamp: yesterday_start.toString(),
              end_timestamp: yesterday_end.toString()
            })
          ]);
          
          // Process today's plant energy
          if (todayPlantResponse.success && todayPlantResponse.data) {
            todayPlantEnergy = todayPlantResponse.data.total_energy_usage;
          }
          
          // Process yesterday's plant energy
          if (yesterdayPlantResponse.success && yesterdayPlantResponse.data) {
            const energyData = yesterdayPlantResponse.data.find(item => item.datapoint === 'daily_energy');
            if (energyData) {
              yesterdayPlantEnergy = energyData.value;
            }
          }
          
          // Process yesterday's weather data
          if (yesterdayWeatherResponse.success && yesterdayWeatherResponse.data.length > 0) {
            const weatherData = yesterdayWeatherResponse.data[0];
            yesterdayDbt = weatherData.mean_drybulb_temperature;
            yesterdayHumidity = weatherData.mean_humidity;
          }
        } catch (innerErr) {
          console.error('Error fetching data:', innerErr);
        }

        // Get realtime weather data for today using the ref
        const todayDbt = getValueRef.current('outdoor_weather_station', 'drybulb_temperature');
        const todayHumidity = getValueRef.current('outdoor_weather_station', 'humidity');
        
        // Update all data at once
        setData([
          {
            label: 'Yesterday',
            value: yesterdayPlantEnergy,
            unit: 'kWh',
            dbt: yesterdayDbt,
            humidity: yesterdayHumidity
          },
          {
            label: 'Today',
            value: todayPlantEnergy,
            unit: 'kWh',
            dbt: todayDbt,
            humidity: todayHumidity
          }
        ]);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      }
    };

    // Fetch data immediately on mount
    fetchData();

    // Set up an interval to fetch data every 1 minute
    const intervalId = setInterval(fetchData, 60000);

    // Cleanup function to clear the interval when the component unmounts
    return () => clearInterval(intervalId);
  }, []); // Empty dependency array means this only runs on mount/unmount

  if (error) {
    return (
      <div className="w-full h-full p-2 alto-card flex flex-col justify-between items-start">
        <div className="text-[#065BA9] text-sm font-semibold">
          Energy Usage
        </div>
        <div className="flex items-center justify-center w-full h-full text-red-500 text-xs">
          Error loading data: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full p-2 alto-card flex flex-col justify-between items-start">
      <div className="text-[#065BA9] text-sm font-semibold">
        Energy Usage
      </div>

      <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-1">
        {data.map((data, index) => (
          <div 
            key={index}
            className="p-1.5 bg-[#F9FAFF] rounded-lg border border-[#EDEFF9] overflow-x-auto"
          >
            <div className="flex flex-col min-w-max">
              <div className="text-[#788796] text-[11px] font-normal">
                {data.label}
              </div>
              
              <div className="flex items-center gap-1">
                <div className="text-[#0E7EE4] text-[14px] sm:text-base font-semibold">
                  {data.value != null ? data.value.toLocaleString(undefined, { maximumFractionDigits: 0 }) : '-'}
                </div>
                <div className="text-[#788796] text-[13px] font-normal">
                  {data.unit}
                </div>
              </div>

              <div className="flex gap-0.5 mt-1">
                <div className="h-4 px-1 bg-[#EDEFF9] rounded flex items-center gap-1">
                  <div className="text-[#5E5E5E] text-[8px] font-normal">DBT</div>
                  <div className="text-[#0E7EE4] text-[8px] font-bold">
                    {data.dbt != null ? data.dbt.toFixed(1) + ' °F' : '-'}
                  </div>
                </div>
                <div className="h-4 px-1 bg-[#EDEFF9] rounded flex items-center gap-1">
                  <div className="text-[#5E5E5E] text-[8px] font-normal">RH</div>
                  <div className="text-[#0E7EE4] text-[8px] font-bold">
                    {data.humidity != null ? data.humidity.toFixed(1) + ' %' : '-'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 