import React from "react";
import { BarGauge } from "@/components/ui/bar-gauge";
import { useRealtime } from "@/contexts/RealtimeContext";

// Define constants for the gauge
const COLORS = ["#14B8B4", "#FEBE54", "#FF7A00", "#EF4337"];
const LABELS = ["Excellent", "Good", "Fair", "Improve"];

interface EfficiencyCardProps {
  thresholds: number[];
  deviceId: string;
  title: string;
}

const EfficiencyCard: React.FC<EfficiencyCardProps> = ({
  thresholds = [0.0, 0.6, 0.7, 0.8, 1.0],
  deviceId = "air_distribution_system",
  title = "Air-Side Efficiency",
}) => {
  // Use the realtime context to get data
  const { getValue } = useRealtime();

  // Get the value from realtime context based on provided dataPoint
  const efficiencyValue = getValue(deviceId, "efficiency");

  return (
    <div className="w-full h-full p-2 bg-white rounded-xl border border-[#EDEFF9] shadow-card flex flex-col justify-between">
      {/* Title Row */}
      <div className="w-full flex justify-between items-center">
        <h2 className="text-card-foreground text-lg font-semibold tracking-[0.01em]">
          {title}
        </h2>
      </div>

      {/* BarGauge Container */}
      <div className="w-full flex flex-col justify-end items-center gap-0.5 min-h-[50px]">
        <div className="w-full max-w-full">
          <BarGauge
            labels={LABELS}
            colors={COLORS}
            value={efficiencyValue}
            threshold={thresholds}
            showValue={true}
          />
        </div>
      </div>
    </div>
  );
};

export default EfficiencyCard;
