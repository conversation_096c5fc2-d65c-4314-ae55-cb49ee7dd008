import { createClient } from "@supabase/supabase-js";

// Dynamically get the Supabase URL
const getSupabaseUrl = () => {
  if (import.meta.env.VITE_SUPABASE_URL) {
    return import.meta.env.VITE_SUPABASE_URL;
  }
  
  // Get the current hostname (localhost, 0.0.0.0, etc.)
  const hostname = window.location.hostname;
  const protocol = window.location.protocol; // 'http:' or 'https:'
  return `${protocol}//${hostname}/realtime`;
};

const SUPABASE_URL = getSupabaseUrl();
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error('VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY environment variables must be defined');
}

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);