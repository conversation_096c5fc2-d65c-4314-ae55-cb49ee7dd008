// Base configuration for all components
export interface BaseComponentConfig {
  id: string;
  type: 'textbox' | 'realtime-value' | 'machine-status' | 'gif' | 'autopilot' | 'plant-efficiency' | 'table' | 'datapoint-display' | 'cooling-load' | 'next-event-schedule' | 'control-input' | 'transitioning-gif' | 'clickable';
  xPercent: number;
  yPercent: number;
  label: string;
}

export interface MapConfig {
  zoomLevel: number;
  xOffset: number;
  yOffset: number;
}

export interface TableSettings {
  riserTable?: {
    enabled: boolean;
    rows: Array<{
      id: string;
      displayName: string;
      deviceId: string;
      controlDeviceId: string;
    }>;
    datapoints: {
      flow: string;
      pressure: string;
      temperature: string;
    };
  };
  indoorAirQualityTable?: {
    enabled: boolean;
    rows: Array<{
      id: number;
      displayName: string;
      deviceId: string;
    }>;
    datapoints: {
      temperature: string;
      humidity: string;
      co2: string;
    };
  };
}

export interface SaveMapConfig {
  name?: string;
  site?: string;
  components: ComponentConfig[];
  properties: {
    toolbarPosition: { x: number; y: number };
    mapConfig: MapConfig;
    tableConfig: TableSettings;
  };
}

// TextBox component properties
export interface TextBoxProperties {
  text: string;
  backgroundColor: string;
  textColor: string;
}

// RealTimeValue component properties
export interface RealTimeValueProperties {
  title: string;
  deviceId: string;
  datapoint: string;
  unit: string;
  precision: number;
  backgroundColor: string;
  textColor: string;
}

// MachineStatus component properties
export interface MachineStatusProperties {
  deviceId: string;
  deviceName: string;
  showName: boolean;
  nameLocation?: 'top' | 'bottom' | 'left' | 'right';
  equipmentType: 'Normal' | 'VSD' | 'Chiller';
  isManualOverride?: boolean;
  // Display settings for Chiller parameters
  chillerDisplaySettings?: {
    showEfficiency: boolean;
    showRLA: boolean;
    showSetpoint: boolean;
    showCHW: boolean;
    showCDW: boolean;
  };
}

// Gif component properties
export interface GifProperties {
  deviceId: string;
  datapoint: string;
  gifName: string;
  zoomLevel: number;
}

// TransitioningGif component properties
export interface TransitioningGifProperties {
  controlDeviceId: string;
  controlDatapoint: string;
  feedbackDeviceId: string;
  feedbackDatapoint: string;
  zoomLevel: number;
}

// Plant Efficiency component properties
export interface PlantEfficiencyProperties {
}

// TableCard component properties
export interface TableCellConfig {
  type: 'text' | 'datapoint';
  value: string | [string, string];  // string for text, [device_id, datapoint] for datapoint
  bgcolor?: string;
}

// Component configurations with properties
export interface TextBoxConfig extends BaseComponentConfig {
  type: 'textbox';
  properties: TextBoxProperties;
}

export interface RealTimeValueConfig extends BaseComponentConfig {
  type: 'realtime-value';
  properties: RealTimeValueProperties;
}

export interface MachineStatusConfig extends BaseComponentConfig {
  type: 'machine-status';
  properties: MachineStatusProperties;
}

export interface GifConfig extends BaseComponentConfig {
  type: 'gif';
  properties: GifProperties;
}

export interface TransitioningGifConfig extends BaseComponentConfig {
  type: 'transitioning-gif';
  properties: TransitioningGifProperties;
}

export interface PlantEfficiencyConfig extends BaseComponentConfig {
  type: 'plant-efficiency';
  properties: PlantEfficiencyProperties;
}

// DatapointDisplay component properties
export interface DatapointDisplayProperties {
  title: string;
  datapoints: Array<{
    name: string;
    deviceId: string;
    datapoint: string;
    precision: number;
    unit: string;
  }>;
}

export interface DatapointDisplayConfig extends BaseComponentConfig {
  type: 'datapoint-display';
  properties: DatapointDisplayProperties;
}

// CoolingLoad component properties
export interface CoolingLoadProperties {
  maxCapacity: number;  // Maximum plant cooling capacity in RT
}

export interface CoolingLoadConfig extends BaseComponentConfig {
  type: 'cooling-load';
  properties: CoolingLoadProperties;
}

// NextEventSchedule component properties
export interface NextEventScheduleProperties {
  // Add any specific properties here if needed
}

export interface NextEventScheduleConfig extends BaseComponentConfig {
  type: 'next-event-schedule';
  properties: NextEventScheduleProperties;
}

// ControlInput component properties
export interface ControlInputProperties {
  title: string;
  deviceId: string;
  datapoint: string;
  unit: string;
}

export interface ControlInputConfig extends BaseComponentConfig {
  type: 'control-input';
  properties: ControlInputProperties;
}

// Add new interface for Clickable properties
export interface ClickableProperties {
  popup_type: string;
  payload: Record<string, any>;
  zoomLevel?: number;
}

// Add new config type
export interface ClickableConfig extends BaseComponentConfig {
  type: 'clickable';
  properties: ClickableProperties;
}

// Union type of all possible component configurations
export type ComponentConfig = TextBoxConfig | RealTimeValueConfig | MachineStatusConfig | GifConfig | TransitioningGifConfig | PlantEfficiencyConfig | DatapointDisplayConfig | CoolingLoadConfig | NextEventScheduleConfig | ControlInputConfig | ClickableConfig;

// Available component definitions
export const AVAILABLE_COMPONENTS = [
  {
    type: 'textbox' as const,
    label: 'Text Box',
    icon: '',
    defaultConfig: {
      properties: {
        text: 'New Text',
        backgroundColor: '#ffffff',
        textColor: '#000000',
      },
    },
  },
  {
    type: 'realtime-value' as const,
    label: 'Real-time Value',
    icon: '',
    defaultConfig: {
      properties: {
        deviceId: '',
        datapoint: '',
        unit: '',
        precision: 2,
        backgroundColor: '#ffffff',
        textColor: '#000000',
        title: 'New Value',
      },
    },
  },
  {
    type: 'machine-status' as const,
    label: 'Machine Status',
    icon: '',
    defaultConfig: {
      properties: {
        deviceId: '',
        deviceName: '',
        showName: true,
        nameLocation: 'top' as const,
        equipmentType: 'Normal'
      },
    },
  },
  {
    type: 'gif' as const,
    label: 'GIF',
    icon: '',
    defaultConfig: {
      properties: {
        deviceId: '',
        datapoint: '',
        gifName: '',
        zoomLevel: 1,
      },
    },
  },
  {
    type: 'datapoint-display' as const,
    label: 'Datapoint Display',
    icon: '',
    defaultConfig: {
      properties: {
        title: 'New Datapoint Display',
        datapoints: []
      },
    },
  },
  {
    type: 'control-input' as const,
    label: 'Control Input',
    icon: '',
    defaultConfig: {
      properties: {
        title: 'New Control',
        deviceId: '',
        datapoint: '',
        unit: '',
      },
    },
  },
  {
    type: 'transitioning-gif' as const,
    label: 'Transitioning GIF',
    icon: '',
    defaultConfig: {
      properties: {
        controlDeviceId: '',
        controlDatapoint: '',
        feedbackDeviceId: '',
        feedbackDatapoint: '',
        zoomLevel: 1,
      },
    },
  },
  {
    type: 'clickable' as const,
    label: 'Clickable',
    icon: '',
    defaultConfig: {
      properties: {
        popup_type: 'chiller_popup',
        payload: {
          device_id: ''
        },
        zoomLevel: 1
      },
    },
  },
] as const;

export type ComponentType = typeof AVAILABLE_COMPONENTS[number]['type'];
