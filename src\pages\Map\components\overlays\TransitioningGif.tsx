import React from 'react';
import { TransitioningGifConfig } from '@/pages/Map/components/types';
import { useRealtime } from '@/contexts/RealtimeContext';
import { useActions } from '@/contexts/ActionContext';
import transitioningGif from '@/assets/transitioning.gif';

interface TransitioningGifProps {
  config: TransitioningGifConfig;
  isEditMode?: boolean;
  onClick?: (e: React.MouseEvent) => void;
}

export const TransitioningGif: React.FC<TransitioningGifProps> = ({ 
  config, 
  isEditMode, 
  onClick 
}) => {
  const { getValue } = useRealtime();
  const { isTransitioning } = useActions();
  
  const { 
    controlDeviceId, 
    controlDatapoint, 
    feedbackDeviceId, 
    feedbackDatapoint, 
    zoomLevel = 1 
  } = config.properties;
  
  // Get current values from realtime context
  const controlValue = getValue(controlDeviceId, controlDatapoint);
  const feedbackValue = getValue(feedbackDeviceId, feedbackDatapoint);
  
  // Check if values are different (showing transitioning state) or if there's a pending action
  const valuesDifferent = controlValue !== feedbackValue;
  const hasPendingAction = isTransitioning(controlDeviceId, controlDatapoint);
  const isInTransition = valuesDifferent || hasPendingAction;
  
  
  // Default base height for the GIF
  const baseHeight = 200;
  
  // Render the component
  const renderContent = () => {
    // In edit mode, always show the transitioning GIF with reduced opacity
    if (isEditMode) {
      return (
        <img 
          src={transitioningGif} 
          alt="Transitioning"
          className="w-auto object-contain transition-opacity duration-200"
          style={{ 
            position: 'absolute',
            left: '50%',
            top: '50%',
            transform: `translate(-50%, -50%) scale(${zoomLevel})`,
            opacity: 0.5,
            height: `${baseHeight}px`,
            maxWidth: 'none'
          }}
        />
      );
    }
    
    // In normal mode, only show the GIF if we're transitioning
    if (isInTransition) {
      return (
        <img 
          src={transitioningGif} 
          alt="Transitioning"
          className="w-auto object-contain transition-opacity duration-200"
          style={{ 
            position: 'absolute',
            left: '50%',
            top: '50%',
            transform: `translate(-50%, -50%) scale(${zoomLevel})`,
            height: `${baseHeight}px`,
            maxWidth: 'none'
          }}
        />
      );
    }
    
    // Not transitioning - show nothing
    return null;
  };
  
  return (
    <div
      onClick={onClick}
      className={`relative ${isEditMode ? 'ring-1 ring-primary cursor-move' : ''}`}
      style={{ 
        minWidth: `${baseHeight * zoomLevel}px`, 
        minHeight: `${baseHeight * zoomLevel}px`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      {renderContent()}
    </div>
  );
};