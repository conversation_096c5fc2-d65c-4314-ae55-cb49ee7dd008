/**
 * Dash URL utility for connecting to Alto Dash
 * Dynamically detects the current hostname and uses port 8801 if no specific URL is provided
 */

/**
 * Gets the base URL for Alto Dash applications
 * Uses the environment variable if provided, otherwise constructs a URL based on the current hostname
 * @returns The base Dash URL
 */
export const getDashUrl = (): string => {
  // Use the environment variable if provided
  if (import.meta.env.VITE_ALTO_DASH_URL) {
    return import.meta.env.VITE_ALTO_DASH_URL;
  }
  
  // Get the current hostname and protocol
  // Default to localhost if running in a non-browser environment
  const hostname = typeof window !== 'undefined' ? window.location.hostname : 'localhost';
  const protocol = typeof window !== 'undefined' ? window.location.protocol : 'http:';
  
  // Return a URL with the same hostname but port 8801
  return `${protocol}//${hostname}`;
};

/**
 * Constructs a full URL for a specific Dash app with the appropriate parameters
 * @param appName The name of the Dash app (e.g., 'afdd', 'analytics', 'dashboard')
 * @param siteId The site ID to pass to the Dash app
 * @param additionalParams Any additional URL parameters to include
 * @returns The complete Dash app URL
 */
export const getDashAppUrl = (
  appName: string,
  siteId: string | undefined,
  additionalParams: Record<string, string> = {}
): string => {
  const baseUrl = getDashUrl();
  const effectiveSiteId = siteId || ''; // Default site ID if none provided
  
  // Start with the required parameters
  const params = new URLSearchParams({
    site_id: effectiveSiteId,
    hide_header: 'true'
  });
  
  // Add any additional parameters
  Object.entries(additionalParams).forEach(([key, value]) => {
    params.append(key, value);
  });
  
  return `${baseUrl}/dashboard/apps/${appName}?${params.toString()}`;
};