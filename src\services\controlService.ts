import { api } from "./api";
import { getSiteId } from "./authService";

interface ControlRequest {
  site_id: string;
  device_id: string;
  datapoint: string;
  value: any;
  command_type: string;
  execute_at: string;
  priority: number;
  source: string;
  status: string;
  passing_conditions: any;
  reconsider_policy: any;
}

interface AutopilotResponse {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: any;
}

export const sendControl = async (deviceId: string, datapoint: string, value: any): Promise<void> => {
  const siteId = getSiteId();
  if (!siteId) {
    throw new Error("No site_id found in local storage");
  }

  const request: ControlRequest = {
    site_id: siteId,
    device_id: deviceId,
    datapoint,
    value,
    command_type: "manual",
    execute_at: new Date().toISOString(), // Execute immediately
    priority: 1,
    source: "manual",
    status: "pending",
    passing_conditions: null,
    reconsider_policy: null
  };

  console.log(`Sending control to device ${deviceId}: ${datapoint} = ${value}`);
  console.log('Request payload:', JSON.stringify(request, null, 2));

  await api.post('/manual-control/', request);
};

/**
 * Updates the autopilot status for a specific device
 * @param deviceId The ID of the device to update
 * @param isAutopilotMode Whether the device should be in autopilot mode (true) or manual mode (false)
 * @returns Promise that resolves when the update is complete
 */
export const updateAutopilotStatus = async (deviceId: string, isAutopilotMode: boolean): Promise<void> => {
  try {
    // Convert isAutopilotMode to autopilot status
    // If isAutopilotMode is true, autopilot should be "active"
    // If isAutopilotMode is false, autopilot should be "inactive"
    const autopilotStatus = isAutopilotMode ? "active" : "inactive";
    
    console.log(`Updating autopilot status for device ${deviceId} to ${autopilotStatus}`);
    
    // Call the Django endpoint to update the autopilot status
    const response = await api.patch<AutopilotResponse>(`/autopilot/by-device/${deviceId}/`, {
      autopilot_status: autopilotStatus
    });
    
    console.log('Autopilot update response:', response.data);
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to update autopilot status');
    }
  } catch (error) {
    console.error('Error updating autopilot status:', error);
    throw error;
  }
};