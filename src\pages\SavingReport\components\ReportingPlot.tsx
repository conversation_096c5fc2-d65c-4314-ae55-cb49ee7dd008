import React, { useState, useEffect, useRef, useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import { useSavingDashboard } from '../contexts/SavingDashboardContext';
import { AlertCircle } from 'lucide-react';
import * as echarts from 'echarts';
import type { EChartsOption } from 'echarts';
import 'echarts-gl';
import { Button } from '@/components/ui/button';

type PlotType = 'scatter' | 'time' | 'bar';
type BarGrouping = 'daily' | 'monthly';
type MonthlyAggregation = 'sum' | 'average';

interface ReportingPlotProps {
  type?: 'scatter' | 'timeseries' | 'bar';
  isFullscreenComponent?: boolean;
}

const ReportingPlot: React.FC<ReportingPlotProps> = ({ type = 'scatter', isFullscreenComponent = false }) => {
  const {
    reportingData,
    regressionResult,
    variableSelection,
    reportingRange,
    baselineData,
    weekdayFilterEnabled,
    excludedReportingRowIds
  } = useSavingDashboard();
  const [chartKey, setChartKey] = useState<string>('report-chart-0');
  const [barGrouping, setBarGrouping] = useState<BarGrouping>('monthly');
  const [monthlyAggregation, setMonthlyAggregation] = useState<MonthlyAggregation>('sum');
  const chartRef = useRef<ReactECharts>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Get the unit label based on the dependent variable
  const getUnitLabel = () => {
    if (variableSelection.dependent === 'plant_energy' || variableSelection.dependent === 'total_energy') {
      return 'kWh';
    } else if (variableSelection.dependent === 'plant_efficiency' || variableSelection.dependent === 'total_efficiency') {
      return 'kW/Ton';
    } else if (variableSelection.dependent === 'temperature') {
      return '°C';
    } else if (variableSelection.dependent === 'humidity') {
      return '%';
    } else if (variableSelection.dependent === 'cooling_load' || variableSelection.dependent === 'airside_energy') {
      return 'kWh';
    } else if (variableSelection.dependent === 'cdd') {
      return 'degree days';
    } else {
      return 'units';
    }
  };

  const unitLabel = getUnitLabel();
  
  // Map the external type to our internal PlotType
  const getInternalType = (): PlotType => {
    if (type === 'timeseries') return 'time';
    if (type === 'bar') return 'bar';
    return 'scatter';
  };
  
  // Update chart key when variables or plot type changes
  useEffect(() => {
    const plotType = getInternalType();
    setChartKey(`report-${plotType}-${variableSelection.independent.join('-')}-${variableSelection.dependent}-${Date.now()}`);
  }, [type, variableSelection.independent, variableSelection.dependent, barGrouping, monthlyAggregation]);

  const toggleBarGrouping = () => {
    setBarGrouping(prev => prev === 'daily' ? 'monthly' : 'daily');
  };

  const toggleMonthlyAggregation = () => {
    setMonthlyAggregation(prev => prev === 'average' ? 'sum' : 'average');
  };

  // Filter data based on reporting range, weekday filter, AND exclusion status
  const filteredData = useMemo(() => {
    return reportingData.filter(row => {
      // Apply date range filter
      if (reportingRange.start && reportingRange.end) {
        const rowDate = new Date(row.date);
        const startDate = new Date(reportingRange.start);
        const endDate = new Date(reportingRange.end);
        if (rowDate < startDate || rowDate > endDate) {
          return false;
        }
      }
      
      // Apply weekday filter
      if (weekdayFilterEnabled) {
        const dayOfWeek = new Date(row.date).getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          return false;
        }
      }
      
      // Apply exclusion filter
      if (row.id !== undefined && excludedReportingRowIds.has(row.id)) {
        return false;
      }
      
      // If all checks pass, include the row
      return true;
    });
  }, [reportingData, reportingRange, weekdayFilterEnabled, excludedReportingRowIds]);

  if (filteredData.length === 0 || !regressionResult) {
    return (
      <div className="p-3 text-center text-gray-500 text-sm">
        No reporting data available for the selected date range.
      </div>
    );
  }

  // Color palette based on design system
  const colorPalette = [
    '#0F172A', // Black Blue
    '#1E40AF', // Dark Blue  
    '#3B82F6', // Mid Blue
    '#60A5FA', // Blue
    '#93C5FD', // Light Blue
    '#BFDBFE', // Bleached Blue
    '#EFF6FF', // White Blue
    
    '#14532D', // Dark Green
    '#16A34A', // Mid Green
    '#22C55E', // Green
    '#86EFAC', // Light Green
    '#BBFCD3', // Bleached Green
    '#F0FDF4', // White Green
    
    '#7C2D12', // Dark Orange
    '#EA580C', // Mid Orange
    '#F97316', // Orange
    '#FB923C', // Light Orange
    '#FDBA74', // Bleached Orange
    '#FFF7ED', // White Orange
  ];

  // Generate options based on the plot type
  const getOptions = (): EChartsOption => {
    const selectedPlotType = getInternalType();
    
    switch (selectedPlotType) {
      case 'scatter':
        if (variableSelection.independent.length > 2) {
          return getTooManyVariablesMessage();
        } else if (variableSelection.independent.length === 2) {
          return get3DScatterOptions();
        } else {
          return getScatterOptions();
        }
      case 'time':
        return getTimeSeriesOptions();
      case 'bar':
        return getBarOptions();
      default:
        return getScatterOptions();
    }
  };

  const getTooManyVariablesMessage = (): EChartsOption => {
    return {
      title: {
        text: 'Cannot generate scatter plot',
        subtext: 'Too many independent variables selected (more than 2)',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold' as const
        },
        subtextStyle: {
          fontSize: 14
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '10%',
        bottom: '10%'
      }
    };
  };

  const getScatterOptions = (): EChartsOption => {
    const independentVar = variableSelection.independent[0];
    
    // Include baseline data series separately from the baselinePeriodData and reportingPeriodData
    const baselineDataSeries = baselineData.map(row => ({
      value: [row[independentVar], row[variableSelection.dependent || 'plant_energy']],
      date: row.date
    }));
    
    // Separate reporting data into baseline and reporting periods based on reporting range
    const baselinePeriodData = filteredData.filter(row => 
      !reportingRange.start || new Date(row.date) < new Date(reportingRange.start)
    ).map(row => ({
      value: [row[independentVar], row.actual_kwh],
      date: row.date
    }));
    
    const reportingPeriodData = filteredData.filter(row => 
      reportingRange.start && new Date(row.date) >= new Date(reportingRange.start)
    ).map(row => ({
      value: [row[independentVar], row.actual_kwh],
      date: row.date
    }));
    
    // Find the actual min and max values from all data sources
    const allIndependentValues = [
      ...filteredData.map(row => row[independentVar]), 
      ...baselineData.map(row => row[independentVar])
    ];
    
    const minIndependentValue = Math.min(...allIndependentValues);
    const maxIndependentValue = Math.max(...allIndependentValues);
    
    // Create prediction line points with more points for smoother line
    const predictionLine = [];
    const steps = 50; // Increase number of points for smoother line
    for (let i = 0; i <= steps; i++) {
      const x = minIndependentValue + (maxIndependentValue - minIndependentValue) * (i / steps);
      // Use the regression equation to predict y value
      const predictedY = regressionResult.predictionFunction({ [independentVar]: x });
      predictionLine.push([x, predictedY]);
    }
    
    // Get metric name for the Y-axis label
    let metricName = "Energy Consumption";
    if (variableSelection.dependent) {
      if (variableSelection.dependent === 'plant_efficiency' || variableSelection.dependent === 'total_efficiency') {
        metricName = "Efficiency";
      } else if (variableSelection.dependent === 'temperature') {
        metricName = "Temperature";
      } else if (variableSelection.dependent === 'humidity') {
        metricName = "Humidity";
      } else if (variableSelection.dependent === 'cooling_load') {
        metricName = "Cooling Load";
      } else if (variableSelection.dependent === 'airside_energy') {
        metricName = "Airside Energy";
      } else if (variableSelection.dependent === 'cdd') {
        metricName = "Cooling Degree Days";
      }
    }

    return {
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          if (params.seriesName === 'Prediction Line') {
            return `${independentVar}: ${params.value[0].toFixed(2)}<br/>Predicted: ${params.value[1].toFixed(2)} ${unitLabel}`;
          }
          const date = params.data && params.data.date ? 
            `Date: ${new Date(params.data.date).toLocaleDateString()}<br/>` : '';
          if (params.seriesName === 'Baseline Data') {
            return `${date}${independentVar}: ${params.value[0].toFixed(2)}<br/>Actual: ${params.value[1].toFixed(2)} ${unitLabel}`;
          }
          return `${date}${independentVar}: ${params.value[0].toFixed(2)}<br/>Actual: ${params.value[1].toFixed(2)} ${unitLabel}`;
        }
      },
      legend: {
        data: ['Baseline Data', 'Reporting Period', 'Prediction Line'],
        top: 10,
        right: 10,
        textStyle: {
          fontSize: 10
        }
      },
      xAxis: {
        type: 'value',
        name: independentVar,
        nameLocation: 'middle',
        nameGap: 25,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value',
        name: `${metricName} (${unitLabel})`,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 10
        }
      },
      series: [
        {
          name: 'Baseline Data',
          type: 'scatter',
          symbolSize: 4,
          data: baselineDataSeries,
          itemStyle: {
            color: '#0E7EE4' 
          }
        },
        {
          name: 'Reporting Period',
          type: 'scatter',
          symbolSize: 4,
          data: reportingPeriodData,
          itemStyle: {
            color: '#F5B141'
          }
        },
        {
          name: 'Prediction Line',
          type: 'line',
          data: predictionLine,
          showSymbol: false,
          smooth: true,
          lineStyle: {
            type: 'dashed',
            width: 2,
            color: '#065BA9'
          }
        }
      ],
      grid: {
        left: '10%',
        right: '5%',
        top: '15%',
        bottom: '15%'
      }
    };
  };

  const get3DScatterOptions = () => {
    if (variableSelection.independent.length !== 2) {
      return getScatterOptions();
    }
    
    const xVar = variableSelection.independent[0];
    const yVar = variableSelection.independent[1];
    const zVar = 'actual_kwh';
    
    // Add baseline data series
    const baselineDataSeries = baselineData.map(row => ({
      value: [row[xVar], row[yVar], row[variableSelection.dependent || 'plant_energy']],
      date: row.date
    }));
    
    // Split reporting data into baseline and reporting periods based on reporting range
    const baselinePeriodData = filteredData.filter(row => 
      !reportingRange.start || new Date(row.date) < new Date(reportingRange.start)
    ).map(row => ({
      value: [row[xVar], row[yVar], row.actual_kwh],
      date: row.date
    }));
    
    const reportingPeriodData = filteredData.filter(row => 
      reportingRange.start && new Date(row.date) >= new Date(reportingRange.start)
    ).map(row => ({
      value: [row[xVar], row[yVar], row.actual_kwh],
      date: row.date
    }));
    
    // Create baseline prediction plane from all data points
    const allXValues = [...filteredData.map(row => row[xVar]), ...baselineData.map(row => row[xVar])];
    const allYValues = [...filteredData.map(row => row[yVar]), ...baselineData.map(row => row[yVar])];
    const xMin = Math.min(...allXValues);
    const xMax = Math.max(...allXValues);
    const yMin = Math.min(...allYValues);
    const yMax = Math.max(...allYValues);
    
    // Generate grid points for the prediction plane
    const gridSize = 20; // Increase for smoother plane
    const predictionPlane = [];
    
    for (let i = 0; i <= gridSize; i++) {
      for (let j = 0; j <= gridSize; j++) {
        const x = xMin + (xMax - xMin) * (i / gridSize);
        const y = yMin + (yMax - yMin) * (j / gridSize);
        
        // Calculate z using regression model
        const predictedZ = regressionResult.predictionFunction({ [xVar]: x, [yVar]: y });
        
        predictionPlane.push([x, y, predictedZ]);
      }
    }
    
    // Get metric name for the Z-axis label
    let metricName = "Consumption";
    if (variableSelection.dependent) {
      if (variableSelection.dependent === 'plant_efficiency' || variableSelection.dependent === 'total_efficiency') {
        metricName = "Efficiency";
      } else if (variableSelection.dependent === 'temperature') {
        metricName = "Temperature";
      } else if (variableSelection.dependent === 'humidity') {
        metricName = "Humidity";
      } else if (variableSelection.dependent === 'cooling_load') {
        metricName = "Cooling Load";
      } else if (variableSelection.dependent === 'airside_energy') {
        metricName = "Airside Energy";
      } else if (variableSelection.dependent === 'cdd') {
        metricName = "Cooling Degree Days";
      }
    }
    
    // Return a more generic object for 3D charts since we use echarts-gl extension
    return {
      legend: {
        data: ['Baseline Data', 'Reporting Period', 'Prediction Plane'],
        top: 10,
        right: 10,
        textStyle: {
          fontSize: 10
        }
      },
      tooltip: {
        formatter: function(params: any) {
          const date = params.data && params.data.date ? 
            `Date: ${new Date(params.data.date).toLocaleDateString()}<br/>` : '';
            
          if (params.seriesName === 'Prediction Plane') {
            return `${xVar}: ${params.value[0].toFixed(2)}<br/>`
              + `${yVar}: ${params.value[1].toFixed(2)}<br/>`
              + `Predicted: ${params.value[2].toFixed(2)} ${unitLabel}`;
          }
          return `${date}${xVar}: ${params.value[0].toFixed(2)}<br/>`
            + `${yVar}: ${params.value[1].toFixed(2)}<br/>`
            + `Actual: ${params.value[2].toFixed(2)} ${unitLabel}`;
        }
      },
      grid3D: {
        viewControl: {
          projection: 'perspective',
          autoRotate: true,
          autoRotateSpeed: 10,
          distance: 200
        }
      },
      xAxis3D: {
        name: xVar,
        type: 'value',
        nameTextStyle: {
          fontSize: 12
        }
      },
      yAxis3D: {
        name: yVar,
        type: 'value',
        nameTextStyle: {
          fontSize: 12
        }
      },
      zAxis3D: {
        name: `${metricName} (${unitLabel})`,
        type: 'value',
        nameTextStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          name: 'Baseline Data',
          type: 'scatter3D',
          data: baselineDataSeries,
          symbolSize: 4,
          itemStyle: {
            color: '#55A6F2',
            opacity: 0.8
          },
          emphasis: {
            itemStyle: {
              color: '#22C55E',
              opacity: 1
            }
          }
        },
        {
          name: 'Reporting Period',
          type: 'scatter3D',
          data: reportingPeriodData,
          symbolSize: 4,
          itemStyle: {
            color: '#F5B141',
            opacity: 0.8
          },
          emphasis: {
            itemStyle: {
              color: '#F5B141',
              opacity: 1
            }
          }
        },
        {
          name: 'Prediction Plane',
          type: 'surface',
          data: predictionPlane,
          shading: 'color',
          itemStyle: {
            color: '#0E7EE4',
            opacity: 0.5
          },
          wireframe: {
            show: true,
            lineStyle: {
              color: '#0E7EE4',
              width: 1,
              opacity: 0.7
            }
          }
        }
      ]
    } as any; // Use type assertion for 3D chart options
  };

  const getTimeSeriesOptions = (): EChartsOption => {
    // Format data for time series display
    const dates = filteredData.map(row => row.date);
    const baselineValues = filteredData.map(row => row.baseline_kwh);
    const actualValues = filteredData.map(row => row.actual_kwh);
    
    // Get metric name for the Y-axis label
    let metricName = "Energy";
    if (variableSelection.dependent) {
      if (variableSelection.dependent === 'plant_efficiency' || variableSelection.dependent === 'total_efficiency') {
        metricName = "Efficiency";
      } else if (variableSelection.dependent === 'temperature') {
        metricName = "Temperature";
      } else if (variableSelection.dependent === 'humidity') {
        metricName = "Humidity";
      } else if (variableSelection.dependent === 'cooling_load') {
        metricName = "Cooling Load";
      } else if (variableSelection.dependent === 'airside_energy') {
        metricName = "Airside Energy";
      } else if (variableSelection.dependent === 'cdd') {
        metricName = "Cooling Degree Days";
      }
    }
    
    return {
      title: {
        // text: `${metricName} Over Time${weekdayFilterEnabled ? ' (Weekdays Only)' : ''}`,
        left: 'left',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const date = new Date(params[0].axisValue).toLocaleDateString();
          let tooltipText = `<b>${date}</b><br/>`;
          
          params.forEach((param: any) => {
            const color = param.color;
            const seriesName = param.seriesName;
            const value = param.value.toFixed(2);
            tooltipText += `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span> ${seriesName}: ${value} ${unitLabel}<br/>`;
          });
          
          return tooltipText;
        }
      },
      legend: {
        data: ['Baseline (Model Equation)', 'Actual Consumption'],
        top: 10,
        right: 10,
        textStyle: {
          fontSize: 10
        }
      },
      xAxis: {
        type: 'category',
        data: dates,
        // name: 'Date',
        nameLocation: 'middle',
        nameGap: 25,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          rotate: 45,
          fontSize: 10,
          formatter: function(value: string) {
            return value.substring(0, 10);
          }
        }
      },
      yAxis: {
        type: 'value',
        name: `${metricName} (${unitLabel})`,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 10
        }
      },
      series: [
        {
          name: 'Baseline (Model Equation)',
          type: 'line',
          data: baselineValues,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#0E7EE4'
          }
        },
        {
          name: 'Actual Consumption',
          type: 'line',
          data: actualValues,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#F5B141'
          }
        }
      ],
      grid: {
        left: '10%',
        right: '5%',
        top: '15%',
        bottom: '15%'
      }
    };
  };

  const getBarOptions = (): EChartsOption => {
    // Sort data by date
    const sortedData = [...filteredData].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    let dates: string[];
    let baselineValues: number[];
    let actualValues: number[];
    let savingsValues: number[];

    if (barGrouping === 'monthly') {
      // Group data by month for bar chart
      const monthlyData: { [key: string]: { baseline: number[], actual: number[] } } = {};
      
      sortedData.forEach(row => {
        const date = new Date(row.date);
        const monthYear = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
        
        if (!monthlyData[monthYear]) {
          monthlyData[monthYear] = { baseline: [], actual: [] };
        }
        
        monthlyData[monthYear].baseline.push(row.baseline_kwh);
        monthlyData[monthYear].actual.push(row.actual_kwh);
      });
      
      // Prepare data for the chart
      dates = Object.keys(monthlyData);
      
      baselineValues = dates.map(month => {
        const values = monthlyData[month].baseline;
        if (monthlyAggregation === 'sum') {
          return values.reduce((sum, val) => sum + val, 0);
        } else {
          return values.reduce((sum, val) => sum + val, 0) / values.length;
        }
      });
      
      actualValues = dates.map(month => {
        const values = monthlyData[month].actual;
        if (monthlyAggregation === 'sum') {
          return values.reduce((sum, val) => sum + val, 0);
        } else {
          return values.reduce((sum, val) => sum + val, 0) / values.length;
        }
      });
      
      // Calculate savings
      savingsValues = dates.map((month, index) => baselineValues[index] - actualValues[index]);
    } else {
      // Use daily data
      dates = sortedData.map(row => row.date);
      baselineValues = sortedData.map(row => row.baseline_kwh);
      actualValues = sortedData.map(row => row.actual_kwh);
      savingsValues = sortedData.map(row => row.baseline_kwh - row.actual_kwh);
    }
    
    // Format percentage savings
    const percentageSavings = dates.map((date, index) => {
      const saving = baselineValues[index] - actualValues[index];
      return baselineValues[index] > 0 ? (saving / baselineValues[index] * 100) : 0;
    });
    
    // Get metric name for the title
    let metricName = "Energy Consumption";
    if (variableSelection.dependent) {
      if (variableSelection.dependent === 'plant_efficiency' || variableSelection.dependent === 'total_efficiency') {
        metricName = "Efficiency";
      } else if (variableSelection.dependent === 'temperature') {
        metricName = "Temperature";
      } else if (variableSelection.dependent === 'humidity') {
        metricName = "Humidity";
      } else if (variableSelection.dependent === 'cooling_load') {
        metricName = "Cooling Load";
      } else if (variableSelection.dependent === 'airside_energy') {
        metricName = "Airside Energy";
      } else if (variableSelection.dependent === 'cdd') {
        metricName = "Cooling Degree Days";
      }
    }
    
    return {
      title: {
        text: `${barGrouping === 'monthly' ? 'Monthly' : 'Daily'} ${metricName} ${barGrouping === 'monthly' ? `(${monthlyAggregation === 'average' ? 'Average' : 'Sum'})` : ''}${weekdayFilterEnabled ? ' (Weekdays Only)' : ''}`,
        left: 'left',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: any) {
          const date = params[0].axisValue;
          const baselineValue = params[0].value;
          const actualValue = params[1].value;
          const savingValue = baselineValue - actualValue;
          const savingPercent = baselineValue > 0 ? (savingValue / baselineValue * 100).toFixed(2) : 0;
          
          return `<b>${date}</b><br/>
            <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${params[0].color};"></span> Baseline (Model Equation): ${baselineValue.toFixed(2)} ${unitLabel}<br/>
            <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${params[1].color};"></span> Actual: ${actualValue.toFixed(2)} ${unitLabel}<br/>
            <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${params[2].color};"></span> Saving: ${savingValue.toFixed(2)} ${unitLabel} (${savingPercent}%)`;
        }
      },
      legend: {
        data: ['Baseline (Model Equation)', 'Actual', 'Savings'],
        top: 10,
        right: 10,
        textStyle: {
          fontSize: 10
        }
      },
      xAxis: {
        type: 'category',
        data: dates,
        nameLocation: 'middle',
        nameGap: 25,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          rotate: 45,
          fontSize: 10,
          formatter: function(value: string) {
            return barGrouping === 'monthly' ? value : value.substring(0, 10);
          }
        }
      },
      yAxis: {
        type: 'value',
        name: `${metricName} (${unitLabel})`,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 10
        }
      },
      series: [
        {
          name: 'Baseline (Model Equation)',
          type: 'bar',
          data: baselineValues,
          itemStyle: {
            color: '#0E7EE4',
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: 'Actual',
          type: 'bar',
          data: actualValues,
          itemStyle: {
            color: '#F5B141',
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: 'Savings',
          type: 'bar',
          data: savingsValues,
          itemStyle: {
            color: '#14B0BC',
            borderRadius: [4, 4, 0, 0]
          }
        }
      ],
      grid: {
        left: '10%',
        right: '5%',
        top: '15%',
        bottom: '15%'
      }
    };
  };

  const chartOptions = getOptions();

  // Determine chart height based on fullscreen status
  const chartHeight = isFullscreenComponent ? '100%' : '320px';

  return (
    <div className={isFullscreenComponent ? 'h-[90vh]' : 'h-auto'} ref={containerRef}>
      <div className="flex justify-end mb-1 space-x-2">
        {getInternalType() === 'bar' && (
          <>
            <Button
              onClick={toggleBarGrouping}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              {barGrouping === 'daily' ? 'Switch to Monthly' : 'Switch to Daily'}
            </Button>
            
            {barGrouping === 'monthly' && (
              <Button
                onClick={toggleMonthlyAggregation}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                {monthlyAggregation === 'average' ? 'Switch to Sum' : 'Switch to Average'}
              </Button>
            )}
          </>
        )}
      </div>
      
      {getInternalType() === 'scatter' && variableSelection.independent.length > 2 ? (
        <div className="flex flex-col items-center justify-center h-[300px] bg-gray-50 border rounded-md p-4">
          <AlertCircle className="h-10 w-10 text-amber-500 mb-2" />
          <h3 className="text-lg font-medium text-gray-800">Cannot display scatter plot</h3>
          <p className="text-gray-600 text-center mt-1">
            Too many independent variables selected (more than 2).<br />
            Please select 1 variable for 2D or 2 variables for 3D scatter plot.
          </p>
        </div>
      ) : (
        <ReactECharts
          ref={chartRef}
          option={chartOptions as any}
          style={{ height: chartHeight }}
          opts={{ renderer: 'canvas' }}
          key={chartKey}
        />
      )}
    </div>
  );
};

export default ReportingPlot;