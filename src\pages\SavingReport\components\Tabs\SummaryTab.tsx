import React, { useState, useEffect, useMemo } from 'react';
import { useSavingDashboard } from '../../contexts/SavingDashboardContext';
import { format, isValid, parse } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { DataTable, TableColumn } from '@/components/ui/data-table';
import { Switch } from '@/components/ui/switch';
import { FileDown, Settings } from 'lucide-react';
import { toast } from 'sonner';
import ExportButton from '../ExportButton';
import ElectricityRateDialog, { ElectricityRateValues } from '../ElectricityRateDialog';

// Define interfaces for our summary data
interface DailySummary {
  date: string;
  baseline: number;
  actual: number;
  savings: number;
  savingsPercentage: number;
  [key: string]: any; // Add this to allow for additional properties
}

interface MonthlySummary {
  month: string;
  baseline: number;
  actual: number;
  savings: number;
  savingsPercentage: number;
}

interface YearlySummary {
  year: string;
  baseline: number;
  actual: number;
  savings: number;
  savingsPercentage: number;
}

// Function to format savings percentages with color
const formatSavingsPercentage = (value: any) => {
  // Parse the string to get the numeric value
  const numericValue = typeof value === 'string'
    ? parseFloat(value.replace(/[^\d.-]/g, ''))
    : typeof value === 'number'
      ? value
      : 0;

  return {
    textColor: numericValue >= 0 ? '#22c55e' : '#ef4444', // green for positive, red for negative
  };
};

const SummaryTab: React.FC = () => {
  // Get the current date for default selections
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

  // Get data from contexts
  const {
    reportingData,
    reportingRange,
    variableSelection,
    baselineData,
    baselineRange,
    regressionResult,
    summaryStats,
    excludedBaselineRowIds,
    thbPerKwh,
    electricityRateMap,
    getElectricityRate,
    setElectricityRate,
    getCurrentElectricityRate
  } = useSavingDashboard();

  // State for selected year and month - initialize from reporting range
  const [selectedYear, setSelectedYear] = useState<number>(() => {
    // Try to get year from reporting range end date
    if (reportingRange && reportingRange.end) {
      const endDate = new Date(reportingRange.end);
      if (isValid(endDate)) {
        return endDate.getFullYear();
      }
    }
    return currentYear;
  });
  const [selectedMonth, setSelectedMonth] = useState<number>(0); // 0 means 'All Months'

  // State for loading
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // State for showing independent variables
  const [showIndependentVars, setShowIndependentVars] = useState<boolean>(false);

  // State for electricity rate dialog
  const [isRateDialogOpen, setIsRateDialogOpen] = useState<boolean>(false);

  // Calculate daily summary data directly from reportingData
  const dailySummaryData = useMemo<DailySummary[]>(() => {
    if (!reportingData || reportingData.length === 0) return [];

    // Filter data for selected year and month
    return reportingData
      .filter(row => {
        // Check if row has the required data
        if (!row.date || !row.baseline_kwh || !row.actual_kwh) return false;

        const date = new Date(row.date);
        if (!isValid(date)) return false;

        const rowYear = date.getFullYear();
        const rowMonth = date.getMonth() + 1;

        if (rowYear !== selectedYear) return false;
        if (selectedMonth !== 0 && rowMonth !== selectedMonth) return false;

        return true;
      })
      .map(row => {
        const baseline = row.baseline_kwh || 0;
        const actual = row.actual_kwh || 0;
        const savings = baseline - actual;
        const savingsPercentage = baseline !== 0 ? (savings / baseline) * 100 : 0;

        // Create the base object with the standard fields
        const dailyData: DailySummary = {
          date: row.date,
          baseline,
          actual,
          savings,
          savingsPercentage
        };

        // Add independent variables if showing them
        if (showIndependentVars && variableSelection.independent.length > 0) {
          // Add each independent variable from the original row data
          variableSelection.independent.forEach(varName => {
            if (row[varName] !== undefined) {
              dailyData[varName] = row[varName];
            }
          });
        }

        return dailyData;
      })
      .sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateA.getTime() - dateB.getTime();
      });
  }, [reportingData, selectedYear, selectedMonth, showIndependentVars, variableSelection.independent]);

  // Calculate monthly summary data
  const monthlySummaryData = useMemo<MonthlySummary[]>(() => {
    if (!reportingData || reportingData.length === 0 || !reportingRange) return [];

    // Group by month
    const monthlyData = new Map<string, { baseline: number; actual: number; count: number }>();

    // Create Date objects from reporting range for filtering
    const startDate = reportingRange.start ? new Date(reportingRange.start) : null;
    const endDate = reportingRange.end ? new Date(reportingRange.end) : null;

    reportingData
      .filter(row => {
        // Check if row has the required data
        if (!row.date || !row.baseline_kwh || !row.actual_kwh) return false;

        const date = new Date(row.date);
        if (!isValid(date)) return false;

        // Only include data within the reporting period
        if (startDate && date < startDate) return false;
        if (endDate && date > endDate) return false;

        return true;
      })
      .forEach(row => {
        const date = new Date(row.date);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

        const baseline = row.baseline_kwh || 0;
        const actual = row.actual_kwh || 0;

        const existingData = monthlyData.get(monthKey) || { baseline: 0, actual: 0, count: 0 };
        monthlyData.set(monthKey, {
          baseline: existingData.baseline + baseline,
          actual: existingData.actual + actual,
          count: existingData.count + 1
        });
      });

    // Create an array to hold all months for the selected year
    const allMonthsData: MonthlySummary[] = [];

    // Add all 12 months for the selected year
    for (let month = 1; month <= 12; month++) {
      const monthKey = `${selectedYear}-${String(month).padStart(2, '0')}`;
      const data = monthlyData.get(monthKey) || { baseline: 0, actual: 0, count: 0 };

      const date = new Date(selectedYear, month - 1, 1);

      allMonthsData.push({
        month: format(date, 'MMM yyyy').toUpperCase(),
        baseline: data.baseline,
        actual: data.actual,
        savings: data.baseline - data.actual,
        savingsPercentage: data.baseline !== 0 ? ((data.baseline - data.actual) / data.baseline) * 100 : 0
      });
    }

    return allMonthsData;
  }, [reportingData, reportingRange, selectedYear]);

  // Calculate yearly summary data
  const yearlySummaryData = useMemo<YearlySummary[]>(() => {
    if (!reportingData || reportingData.length === 0 || !reportingRange) return [];

    // Group data by year
    const yearlyData = new Map<number, { baseline: number; actual: number }>();

    // Create Date objects from reporting range for filtering
    const startDate = reportingRange.start ? new Date(reportingRange.start) : null;
    const endDate = reportingRange.end ? new Date(reportingRange.end) : null;

    reportingData
      .filter(row => {
        // Check if row has the required data
        if (!row.date || !row.baseline_kwh || !row.actual_kwh) return false;

        const date = new Date(row.date);
        if (!isValid(date)) return false;

        // Only include data within the reporting period
        if (startDate && date < startDate) return false;
        if (endDate && date > endDate) return false;

        return true;
      })
      .forEach(row => {
        const date = new Date(row.date);
        const year = date.getFullYear();
        const baseline = row.baseline_kwh || 0;
        const actual = row.actual_kwh || 0;

        const existingData = yearlyData.get(year) || { baseline: 0, actual: 0 };
        yearlyData.set(year, {
          baseline: existingData.baseline + baseline,
          actual: existingData.actual + actual
        });
      });

    // Convert map to array
    const yearlyEntries = Array.from(yearlyData.entries())
      .map(([year, data]) => {
        const baseline = data.baseline;
        const actual = data.actual;
        const savings = baseline - actual;
        const savingsPercentage = baseline !== 0 ? (savings / baseline) * 100 : 0;

        return {
          year: year.toString(),
          baseline,
          actual,
          savings,
          savingsPercentage
        };
      })
      .sort((a, b) => parseInt(a.year) - parseInt(b.year)); // Sort by year ascending

    // Add an additional future year
    if (yearlyEntries.length > 0) {
      const latestYear = Math.max(...yearlyEntries.map(entry => parseInt(entry.year)));
      const futureYear = latestYear + 1;

      yearlyEntries.push({
        year: futureYear.toString(),
        baseline: 0,
        actual: 0,
        savings: 0,
        savingsPercentage: 0
      });
    } else if (selectedYear) {
      // If no data but we have a selected year, add the next year
      yearlyEntries.push({
        year: (selectedYear + 1).toString(),
        baseline: 0,
        actual: 0,
        savings: 0,
        savingsPercentage: 0
      });
    }

    // Sort by year descending for display
    return yearlyEntries.sort((a, b) => parseInt(b.year) - parseInt(a.year));
  }, [reportingData, reportingRange, selectedYear]);

  // Available years for selection
  const availableYears = useMemo(() => {
    const years = new Set<number>();

    // Add years from reporting range if available
    if (reportingRange && reportingRange.start) {
      const startDate = new Date(reportingRange.start);
      if (isValid(startDate)) {
        years.add(startDate.getFullYear());
      }
    }

    if (reportingRange && reportingRange.end) {
      const endDate = new Date(reportingRange.end);
      if (isValid(endDate)) {
        years.add(endDate.getFullYear());
      }
    }

    // Add current year if not already included
    years.add(currentYear);

    // Add selected year if not already included
    years.add(selectedYear);

    return Array.from(years).sort((a, b) => b - a); // Sort in descending order
  }, [currentYear, reportingRange, selectedYear]);

  // Effect to update selected year when reporting range changes
  useEffect(() => {
    if (reportingRange && reportingRange.end) {
      const endDate = new Date(reportingRange.end);
      if (isValid(endDate)) {
        setSelectedYear(endDate.getFullYear());
      }
    }
  }, [reportingRange]);

  useEffect(() => {
    setIsLoading(true);
    // Just a brief loading indicator
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);
    return () => clearTimeout(timer);
  }, [selectedYear, selectedMonth]);

  // Function to format numbers with commas and fixed decimal places
  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num === 0) return "";
    return num.toLocaleString('en-US', { minimumFractionDigits: decimals, maximumFractionDigits: decimals });
  };

  // Handler for saving electricity rate values
  const handleSaveRateValues = (values: ElectricityRateValues) => {
    setElectricityRate(values);
  };

  // Get the current electricity rate values for the selected year/month
  const currentRateValues = useMemo(() => {
    return getCurrentElectricityRate(selectedYear, selectedMonth);
  }, [getCurrentElectricityRate, selectedYear, selectedMonth]);

  return (
    <div className="space-y-4">
      {/* Year and Month Selectors */}
      <div className="flex flex-wrap gap-4 mb-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Year</label>
          <Select
            value={selectedYear.toString()}
            onValueChange={(value) => setSelectedYear(parseInt(value, 10))}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Year" />
            </SelectTrigger>
            <SelectContent>
              {availableYears.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Month</label>
          <Select
            value={selectedMonth.toString()}
            onValueChange={(value) => setSelectedMonth(parseInt(value, 10))}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Month" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">All Months</SelectItem>
              {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                <SelectItem key={month} value={month.toString()}>
                  {format(new Date(2000, month - 1, 1), 'MMMM')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-end">
          <ExportButton
            variant="outline"
            className="flex items-center gap-1"
            selectedYear={selectedYear}
            selectedMonth={selectedMonth}
            thbPerKwh={thbPerKwh}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="show-independent-vars"
            checked={showIndependentVars}
            onCheckedChange={setShowIndependentVars}
          />
          <label
            htmlFor="show-independent-vars"
            className="text-sm font-medium cursor-pointer"
          >
            Show Independent Variables
          </label>
        </div>

        <div className="flex items-center space-x-2">
          <label className="text-sm font-medium">THB per kWh:</label>
          <div className="px-2 py-1 border rounded-md w-20 text-sm bg-gray-50">
            {thbPerKwh.toFixed(4)}
          </div>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => setIsRateDialogOpen(true)}
          >
            <Settings size={14} />
            Configure
          </Button>
        </div>

        <ElectricityRateDialog
          open={isRateDialogOpen}
          onOpenChange={setIsRateDialogOpen}
          electricityRateMap={electricityRateMap}
          onSave={handleSaveRateValues}
          reportingRange={reportingRange}
        />

      </div>

      {/* Data Tables - Two-column layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column - Daily Data */}
        <div className="space-y-6">
          {/* Daily Comparison */}
          <div>
            {dailySummaryData.length > 0 ? (
              <DataTable
                columns={useMemo(() => {
                  // Base columns that are always shown
                  const baseColumns: TableColumn[] = [
                    { key: 'date', header: 'Date', textAlign: 'center' },
                    { key: 'baseline', header: 'Baseline (kWh)', textAlign: 'center' },
                    { key: 'actual', header: 'Actual (kWh)', textAlign: 'center' },
                    { key: 'savings', header: 'Savings (kWh)', textAlign: 'center' },
                    {
                      key: 'savingsPercentage',
                      header: 'Savings (%)',
                      formatCell: formatSavingsPercentage,
                      textAlign: 'center'
                    }
                  ];

                  // If we're showing independent variables and we have data
                  if (showIndependentVars && variableSelection.independent.length > 0) {
                    // Get the independent variables from the variableSelection
                    const additionalColumns = variableSelection.independent.map(varName => ({
                      key: varName,
                      header: varName.charAt(0).toUpperCase() + varName.slice(1).replace(/_/g, ' '),
                      textAlign: 'center' as const
                    }));

                    return [...baseColumns, ...additionalColumns];
                  }

                  return baseColumns;
                }, [dailySummaryData, showIndependentVars])}
                data={useMemo(() => {
                  return dailySummaryData.map(day => {
                    const baseData: Record<string, any> = {
                      date: format(new Date(day.date), 'MMM dd, yyyy'),
                      baseline: formatNumber(day.baseline),
                      actual: formatNumber(day.actual),
                      savings: formatNumber(day.savings),
                      savingsPercentage: `${formatNumber(day.savingsPercentage)}%`
                    };

                    // The independent variables are already added in the dailySummaryData generation
                    // We just need to format them here
                    if (showIndependentVars && variableSelection.independent.length > 0) {
                      variableSelection.independent.forEach(varName => {
                        if (day[varName] !== undefined) {
                          baseData[varName] = typeof day[varName] === 'number' ? formatNumber(day[varName]) : day[varName];
                        } else {
                          baseData[varName] = '-'; // Placeholder for missing values
                        }
                      });
                    }

                    return baseData;
                  });
                }, [dailySummaryData, showIndependentVars])}
              />
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                No daily data available for the selected period.
              </div>
            )}
          </div>
        </div>

        {/* Right Column - Monthly and Yearly Data */}
        <div className="space-y-1">

					{/* Monthly Summary */}
          <h3 className="text-lg font-semibold">Monthly</h3>
          <div>
              <DataTable
                  columns={[
                  { key: 'month', header: 'Month', separator: true, textAlign: 'center' },
                  { key: 'baseline', header: 'Baseline (kWh)', textAlign: 'center' },
                  { key: 'actual', header: 'Actual (kWh)', textAlign: 'center' },
                  { key: 'savings', header: 'Savings (kWh)', textAlign: 'center' },
                  { key: 'savingsPercentage', header: 'Savings (%)', formatCell: formatSavingsPercentage, textAlign: 'center' }
                  ]}
                  data={monthlySummaryData.map(month => ({
                  month: month.month,
                  baseline: formatNumber(month.baseline),
                  actual: formatNumber(month.actual),
                  savings: formatNumber(month.savings),
                  savingsPercentage: `${formatNumber(month.savingsPercentage)}` + (month.savingsPercentage !== 0 ? ' %' : '')
                  }))}
              />
          </div>

          {/* Yearly Summary - First Table */}
          <h3 className="text-lg font-semibold pt-2">Yearly</h3>
          <div>
            <DataTable
              columns={[
                { key: 'year', header: 'Year', textAlign: 'center' , separator: true},
                { key: 'baseline', header: 'Baseline (kWh)', textAlign: 'center'},
                { key: 'actual', header: 'Actual (kWh)', textAlign: 'center' },
                { key: 'savings', header: 'Savings (kWh)', textAlign: 'center' },
                { key: 'savingsPercentage', header: 'Savings (%)', formatCell: formatSavingsPercentage, textAlign: 'center' }
              ]}
              data={yearlySummaryData
                .sort((a, b) => parseInt(a.year) - parseInt(b.year)) // Sort in ascending order
                .map(year => ({
                year: year.year,
                baseline: formatNumber(year.baseline),
                actual: formatNumber(year.actual),
                savings: formatNumber(year.savings),
                savingsPercentage: `${formatNumber(year.savingsPercentage)}` + (year.savingsPercentage !== 0 ? ' %' : '')
              }))}
            />
          </div>

          {/* Comparative Table */}
          <h3 className="text-lg font-semibold pt-4">Comparative</h3>
          <div className="mt-2">
            <DataTable
              headerGroups={[
                {
                  // First header row with year column spanning cells
                  empty: { label: "", width: "w-[120px]" , separator: true},
                  year1: { label: selectedYear - 1, colSpan: 2, bgColor: "", separator: true },
                  year2: { label: selectedYear, colSpan: 2, bgColor: "" }
                }
              ]}
              columns={[
                { key: 'month', header: 'Month', separator: true, textAlign: 'center' },
                { key: `kwh${selectedYear - 1}`, header: 'kWh', textAlign: 'center' , separator: true},
                // { key: `rth${selectedYear - 1}`, header: 'RTH', textAlign: 'center', separator: true },
                { key: `kwh${selectedYear}`, header: 'kWh', textAlign: 'center' },
                // { key: `rth${selectedYear}`, header: 'RTH', textAlign: 'center' }
              ]}
              data={[
                {
                  month: 'Jan',
                  [`kwh${selectedYear - 1}`]: '572,476',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '410,810',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Feb',
                  [`kwh${selectedYear - 1}`]: '629,752',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '491,385',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Mar',
                  [`kwh${selectedYear - 1}`]: '708,447',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '624,284',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Apr',
                  [`kwh${selectedYear - 1}`]: '772,741',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'May',
                  [`kwh${selectedYear - 1}`]: '774,199',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Jun',
                  [`kwh${selectedYear - 1}`]: '712,326',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Jul',
                  [`kwh${selectedYear - 1}`]: '706,045',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Aug',
                  [`kwh${selectedYear - 1}`]: '727,417',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Sep',
                  [`kwh${selectedYear - 1}`]: '708,868',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Oct',
                  [`kwh${selectedYear - 1}`]: '706,513',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Nov',
                  [`kwh${selectedYear - 1}`]: '630,140',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                },
                {
                  month: 'Dec',
                  [`kwh${selectedYear - 1}`]: '548,823',
                  [`rth${selectedYear - 1}`]: '',
                  [`kwh${selectedYear}`]: '',
                  [`rth${selectedYear}`]: ''
                }
              ]}
            />
          </div>

        </div>
      </div>
    </div>
  );
};

export default SummaryTab;
