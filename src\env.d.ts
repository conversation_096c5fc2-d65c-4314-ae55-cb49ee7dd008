/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_DJANGO_API_URL: string;
  readonly VITE_SUPABASE_URL: string;
  readonly VITE_SUPABASE_ANON_KEY: string;
  readonly VITE_ALTO_DASH_URL: string;
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_VERSION: string;

  // Sidebar Navigation Configuration
  readonly VITE_SHOW_MAP?: string;
  readonly VITE_SHOW_DASHBOARD?: string;
  readonly VITE_SHOW_HISTORICAL_DATA?: string;
  readonly VITE_SHOW_MAINTENANCE?: string;
  readonly VITE_SHOW_ANALYTICS?: string;
  readonly VITE_SHOW_SETTINGS?: string;
  readonly VITE_SHOW_SAVING_REPORT?: string;
  readonly VITE_SHOW_TECHNICAL_REPORT?: string;
  readonly VITE_SHOW_AFDD?: string;
  readonly VITE_SHOW_ROOM_ANALYTICS?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}