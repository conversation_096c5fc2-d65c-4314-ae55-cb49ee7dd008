import { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';

export type Language = 'en' | 'th';

export interface ManualImage {
  src: string;
  alt: string;
  caption?: string;
  width?: string;
}

export interface ManualMultilingualContent {
  en: string;
  th: string;
}

export interface ManualSubsection {
  id: string;
  title: ManualMultilingualContent;
  content: ManualMultilingualContent;
}

export interface ManualSection {
  id: string;
  title: ManualMultilingualContent;
  content: ManualMultilingualContent;
  images?: ManualImage[];
  subsections?: ManualSubsection[];
}

export interface ManualChapter {
  id: string;
  title: ManualMultilingualContent;
  icon: ReactNode;
  sections: ManualSection[];
}

export interface SearchResult {
  chapterId: string;
  chapterTitle: string;
  sectionId: string;
  sectionTitle: string;
  subsectionId?: string;
  subsectionTitle?: string;
  contentSnippet: string;
  match: string;
} 