import React, { useState, useCallback, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { MachineStatusConfig } from '@/pages/Map/components/types';
import { useRealtime } from '@/contexts/RealtimeContext';
import { useAutopilot } from '@/contexts/AutopilotContext';
import { useDevice } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';
import { Switch } from '@/components/ui/switch';
import manual_icon from '@/assets/manual_icon.svg';
import maintenance_icon from '@/assets/maintenance_icon.svg';
import edit_icon from '@/assets/edit_icon.svg';
import { updateAutopilotStatus } from '@/services/controlService';

interface MachineStatusProps {
  config: MachineStatusConfig;
  isEditMode?: boolean;
  onClick?: (e: React.MouseEvent) => void;
}

const STATUS_COLORS = {
  on: 'bg-[#14B8B4]',
  off: 'bg-[#788796]',
  alarm: 'bg-[#EF4337]',
  maintenance: 'bg-[#FEBE54]',
} as const;

type MachineStatusType = keyof typeof STATUS_COLORS;

interface PopupMenuProps {
  deviceId: string;
  deviceName: string;
  equipmentType: 'Normal' | 'VSD' | 'Chiller';
  onClose: () => void;
}

interface ConfirmationModalProps {
  isOpen: boolean;
  action: 'start' | 'stop';
  onConfirm: () => Promise<void>;
  onCancel: () => void;
}

const AutoManualSwitch: React.FC<{
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
}> = ({ checked, onCheckedChange, disabled }) => {
  return (
    <div 
      className="relative inline-flex items-center" 
      onClick={(e) => e.stopPropagation()}
    >
      <Switch
        checked={checked}
        onCheckedChange={onCheckedChange}
        disabled={disabled}
        size="sm"
        className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-[#0E7EE4] data-[state=checked]:to-[#14B8B4]"
      />
      <div className="absolute inset-0 flex justify-between items-center px-[6px] pointer-events-none">
        <span className="text-[8px] font-medium text-white">A</span>
        <span className="text-[8px] font-medium text-white">M</span>
      </div>
    </div>
  );
};

const LoadingSpinner = () => (
  <div className="animate-spin">
    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
    </svg>
  </div>
);

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  action,
  onConfirm,
  onCancel,
}) => {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handleConfirmClick = async () => {
    setStatus('loading');
    try {
      await onConfirm();
      setStatus('success');
      setTimeout(() => {
        setStatus('idle');
        onCancel();
      }, 1000);
    } catch (error) {
      setStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Failed to send command');
      setTimeout(() => {
        setStatus('idle');
      }, 2000);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="absolute inset-0 bg-black/20 backdrop-blur-[1px] rounded-[7px] flex items-center justify-center z-50">
      <div className="bg-white rounded-md p-3 shadow-lg w-[90%]">
        {status === 'idle' && (
          <>
            <div className="text-[10px] font-medium text-foreground mb-2 text-center">
              Confirm?
            </div>
            <div className="flex gap-2">
              <button
                className="flex-1 h-5 text-[8px] rounded bg-muted/10 hover:bg-muted/20 text-muted-foreground text-center"
                onClick={onCancel}
              >
                Cancel
              </button>
              <button
                className={cn(
                  "flex-1 h-5 text-[8px] rounded text-white text-center",
                  action === 'start' ? "bg-success hover:bg-success/90" : "bg-destructive hover:bg-destructive/90"
                )}
                onClick={handleConfirmClick}
              >
                Confirm
              </button>
            </div>
          </>
        )}

        {status === 'loading' && (
          <div className="flex flex-col items-center justify-center py-1">
            <LoadingSpinner />
            <div className="text-[10px] font-medium text-foreground mt-2 text-center">
              Sending command...
            </div>
          </div>
        )}

        {status === 'success' && (
          <div className="flex flex-col items-center justify-center py-1">
            <div className="text-success">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M20 6L9 17L4 12"/>
              </svg>
            </div>
            <div className="text-[10px] font-medium text-success mt-1 text-center">
              Command sent successfully
            </div>
          </div>
        )}

        {status === 'error' && (
          <div className="flex flex-col items-center justify-center py-1">
            <div className="text-destructive">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M15 9L9 15M9 9L15 15"/>
              </svg>
            </div>
            <div className="text-[10px] font-medium text-destructive mt-1 text-center">
              {errorMessage}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const PopupMenu: React.FC<PopupMenuProps> = ({ deviceId, deviceName, equipmentType, onClose }) => {
  const { getValue, setValue } = useRealtime();
  const { isDeviceInAutopilotMode } = useAutopilot();
  const { isDeviceUnderMaintenance } = useDevice();
  const statusRead = getValue(deviceId, 'status_read');
  const alarm = getValue(deviceId, 'alarm');
  const frequencyRead = getValue(deviceId, 'frequency_read');
  
  // Actual values to be submitted
  const [inputSpeed, setInputSpeed] = useState<number | null>(null);
  const [inputSetpoint, setInputSetpoint] = useState<number | null>(null);
  const [inputLimit, setInputLimit] = useState<number | null>(null);
  
  // Text values for editing (can be any string)
  const [speedText, setSpeedText] = useState<string>('');
  const [setpointText, setSetpointText] = useState<string>('');
  const [limitText, setLimitText] = useState<string>('');
  
  const [editMode, setEditMode] = useState<{
    speed: boolean;
    setpoint: boolean;
    limit: boolean;
  }>({
    speed: false,
    setpoint: false,
    limit: false
  });
  const [confirmation, setConfirmation] = useState<{ isOpen: boolean; action: 'start' | 'stop' } | null>(null);
  const [isUpdatingAutopilot, setIsUpdatingAutopilot] = useState(false);

  useEffect(() => {
    if (equipmentType === 'VSD' && frequencyRead !== undefined && inputSpeed === null) {
      setInputSpeed(frequencyRead);
      setSpeedText(frequencyRead.toFixed(1));
    } else if (equipmentType === 'Chiller') {
      const setpoint = getValue(deviceId, 'setpoint_write');
      const limit = getValue(deviceId, 'demand_limit_setpoint_write');
      if (setpoint !== undefined && inputSetpoint === null) {
        setInputSetpoint(setpoint);
        setSetpointText(setpoint.toFixed(1));
      }
      if (limit !== undefined && inputLimit === null) {
        setInputLimit(limit);
        setLimitText(limit.toFixed(1));
      }
    }
  }, [equipmentType, frequencyRead, deviceId, getValue, inputSpeed, inputSetpoint, inputLimit]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.popup-content')) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleFrequencyChange = async () => {
    // Validate and parse only on submission
    const parsedValue = parseFloat(speedText);
    if (!isNaN(parsedValue) && parsedValue >= 0 && parsedValue <= 60) {
      await setValue(deviceId, 'frequency_write', parsedValue);
      setInputSpeed(parsedValue);
      setEditMode(prev => ({ ...prev, speed: false }));
    } else {
      // If invalid, revert to the current value
      setSpeedText(inputSpeed ? inputSpeed.toFixed(1) : '0.0');
    }
  };

  const handleSetpointChange = async () => {
    // Validate and parse only on submission
    const parsedValue = parseFloat(setpointText);
    if (!isNaN(parsedValue) && parsedValue >= 35 && parsedValue <= 65) {
      await setValue(deviceId, 'setpoint_write', parsedValue);
      setInputSetpoint(parsedValue);
      setEditMode(prev => ({ ...prev, setpoint: false }));
    } else {
      // If invalid, revert to the current value
      setSetpointText(inputSetpoint ? inputSetpoint.toFixed(1) : '35.0');
    }
  };

  const handleLimitChange = async () => {
    // Validate and parse only on submission
    const parsedValue = parseFloat(limitText);
    if (!isNaN(parsedValue) && parsedValue >= 0 && parsedValue <= 100) {
      await setValue(deviceId, 'demand_limit_setpoint_write', parsedValue);
      setInputLimit(parsedValue);
      setEditMode(prev => ({ ...prev, limit: false }));
    } else {
      // If invalid, revert to the current value
      setLimitText(inputLimit ? inputLimit.toFixed(1) : '0.0');
    }
  };

  const beginEdit = (field: 'speed' | 'setpoint' | 'limit') => {
    if (field === 'speed') {
      // Initialize text field with current value when editing begins
      setSpeedText(inputSpeed !== null ? inputSpeed.toFixed(1) : '');
    } else if (field === 'setpoint') {
      setSetpointText(inputSetpoint !== null ? inputSetpoint.toFixed(1) : '');
    } else if (field === 'limit') {
      setLimitText(inputLimit !== null ? inputLimit.toFixed(1) : '');
    }
    setEditMode(prev => ({ ...prev, [field]: true }));
  };

  const handleManualControl = async (checked: boolean) => {
    try {
      setIsUpdatingAutopilot(true);
      await updateAutopilotStatus(deviceId, checked);
    } catch (error) {
      console.error('Failed to update autopilot status:', error);
    } finally {
      setIsUpdatingAutopilot(false);
    }
  };

  const handleStartStop = async (action: 'start' | 'stop') => {
    setConfirmation({ isOpen: true, action });
  };

  const handleConfirm = async () => {
    if (!confirmation) return;
    
    try {
      await setValue(deviceId, 'status_write', confirmation.action === 'start');
    } catch (error) {
      throw new Error('Failed to send command to device');
    }
  };

  return (
    <div 
      className="popup-content absolute top-0 right-0 translate-x-[calc(100%+8px)] -translate-y-[calc(100%+8px)] z-50"
      style={{
        width: 120,
        padding: 6,
        background: 'white',
        boxShadow: '4px 8px 20px rgba(112, 144, 176, 0.24)',
        borderRadius: 7,
        backdropFilter: 'blur(20px)',
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <ConfirmationModal
        isOpen={!!confirmation}
        action={confirmation?.action || 'start'}
        onConfirm={handleConfirm}
        onCancel={() => setConfirmation(null)}
      />

      <div className="flex flex-col gap-2">
        {/* Status Section */}
        <div className="flex justify-between items-start">
          <div className="flex flex-col">
            <div className="text-[#5E5E5E] text-[8px] font-normal">{deviceName}</div>
            <div className="flex items-center gap-2">
              <div className="w-[6px] h-[6px] rounded-full" 
                style={{ 
                  backgroundColor: alarm ? STATUS_COLORS.alarm : 
                                 statusRead ? STATUS_COLORS.on : 
                                 STATUS_COLORS.off 
                }} 
              />
              <div className="text-[#2E8285] text-[8px] font-normal">
                {alarm ? 'Alarm' : statusRead ? 'Running' : 'Stopped'}
              </div>
            </div>
          </div>
          <AutoManualSwitch
            checked={isDeviceInAutopilotMode(deviceId)}
            onCheckedChange={handleManualControl}
            disabled={alarm || isUpdatingAutopilot}
          />
        </div>

        {/* Current Speed Read Section (VSD only) */}
        {equipmentType === 'VSD' && (
          <div className="flex justify-between items-center">
            <div className="text-[#5E5E5E] text-[8px] font-normal">Current Speed</div>
            <div className="text-[#2E8285] text-[8px] font-normal">
              {frequencyRead == null ? '-' : frequencyRead.toFixed(1)} Hz
            </div>
          </div>
        )}

        <div className="w-full h-[1px] bg-[#EDEFF9]" />

        {/* Start/Stop Buttons */}
        <div className="flex gap-1 mt-1">
          <button
            className={cn(
              "flex-1 h-6 rounded-md text-[10px] font-medium transition-colors text-center",
              "bg-success text-white hover:bg-success/90"
            )}
            onClick={() => handleStartStop('start')}
          >
            Start
          </button>
          <button
            className={cn(
              "flex-1 h-6 rounded-md text-[10px] font-medium transition-colors text-center", 
              "bg-destructive text-destructive-foreground hover:bg-destructive/90"
            )}
            onClick={() => handleStartStop('stop')}
          >
            Stop
          </button>
        </div>

        {/* Controls Section */}
        {equipmentType === 'VSD' && (
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <div className="text-[#5E5E5E] text-[8px] font-normal">Speed</div>
            </div>
            <div className="flex items-center gap-2">
              <div className={cn(
                "h-6 min-w-[48px] w-full px-2 py-1 rounded-[4px] flex justify-between items-center",
                editMode.speed ? "bg-white border border-[#55A6F2]" : "bg-[#EDEFF9]"
              )}>
                {editMode.speed ? (
                  <>
                    <input
                      type="text"
                      value={speedText}
                      onChange={(e) => setSpeedText(e.target.value)}
                      className="w-full text-[#0E7EE4] text-[8px] font-bold focus:outline-none bg-transparent"
                    />
                    <span className="text-[#6B7280] text-[8px] font-normal">Hz</span>
                  </>
                ) : (
                  <div className="w-full text-center text-[#0E7EE4] text-[8px] font-bold">
                    {inputSpeed !== null ? inputSpeed.toFixed(1) : '-'} Hz
                  </div>
                )}
              </div>
              <button
                onClick={() => {
                  if (editMode.speed) {
                    handleFrequencyChange();
                  } else {
                    beginEdit('speed');
                  }
                }}
                className="flex items-center justify-center"
              >
                {editMode.speed ? (
                  <div className="w-4 h-4 bg-[#0E7EE4] rounded flex items-center justify-center">
                    <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                      <path d="M4 8.24234L7 11.1514L12 5.33325" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                ) : (
                  <img src={edit_icon} alt="Edit" className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        )}

        {/* Chiller Controls */}
        {equipmentType === 'Chiller' && (
          <div className="flex flex-col gap-2">
            {/* Setpoint Control */}
            <div className="flex flex-col gap-2">
              <div className="text-[#5E5E5E] text-[8px] font-normal">Setpoint</div>
              <div className="flex items-center gap-2">
                <div className={cn(
                  "h-6 min-w-[48px] w-full px-2 py-1 rounded-[4px] flex justify-between items-center",
                  editMode.setpoint ? "bg-white border border-[#55A6F2]" : "bg-[#EDEFF9]"
                )}>
                  {editMode.setpoint ? (
                    <>
                      <input
                        type="text"
                        value={setpointText}
                        onChange={(e) => setSetpointText(e.target.value)}
                        className="w-full text-[#0E7EE4] text-[8px] font-bold focus:outline-none bg-transparent"
                      />
                      <span className="text-[#6B7280] text-[8px] font-normal">°F</span>
                    </>
                  ) : (
                    <div className="w-full text-center text-[#0E7EE4] text-[8px] font-bold">
                      {inputSetpoint !== null ? inputSetpoint.toFixed(1) : '-'} °F
                    </div>
                  )}
                </div>
                <button
                  onClick={() => {
                    if (editMode.setpoint) {
                      handleSetpointChange();
                    } else {
                      beginEdit('setpoint');
                    }
                  }}
                  className="flex items-center justify-center"
                >
                  {editMode.setpoint ? (
                    <div className="w-4 h-4 bg-[#0E7EE4] rounded flex items-center justify-center">
                      <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                        <path d="M4 8.24234L7 11.1514L12 5.33325" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  ) : (
                    <img src={edit_icon} alt="Edit" className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            {/* % Limit Control */}
            <div className="flex flex-col gap-2">
              <div className="text-[#5E5E5E] text-[8px] font-normal">% Limit</div>
              <div className="flex items-center gap-2">
                <div className={cn(
                  "h-6 min-w-[48px] w-full px-2 py-1 rounded-[4px] flex justify-between items-center",
                  editMode.limit ? "bg-white border border-[#55A6F2]" : "bg-[#EDEFF9]"
                )}>
                  {editMode.limit ? (
                    <>
                      <input
                        type="text"
                        value={limitText}
                        onChange={(e) => setLimitText(e.target.value)}
                        className="w-full text-[#0E7EE4] text-[8px] font-bold focus:outline-none bg-transparent"
                      />
                      <span className="text-[#6B7280] text-[8px] font-normal">%</span>
                    </>
                  ) : (
                    <div className="w-full text-center text-[#0E7EE4] text-[8px] font-bold">
                      {inputLimit !== null ? inputLimit.toFixed(1) : '-'} %
                    </div>
                  )}
                </div>
                <button
                  onClick={() => {
                    if (editMode.limit) {
                      handleLimitChange();
                    } else {
                      beginEdit('limit');
                    }
                  }}
                  className="flex items-center justify-center"
                >
                  {editMode.limit ? (
                    <div className="w-4 h-4 bg-[#0E7EE4] rounded flex items-center justify-center">
                      <svg width="12" height="12" viewBox="0 0 16 16" fill="none">
                        <path d="M4 8.24234L7 11.1514L12 5.33325" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  ) : (
                    <img src={edit_icon} alt="Edit" className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export const MachineStatus: React.FC<MachineStatusProps> = ({ config, isEditMode, onClick }) => {
  const { getValue } = useRealtime();
  const { isDeviceInAutopilotMode } = useAutopilot();
  const { isDeviceUnderMaintenance } = useDevice();
  const [showPopup, setShowPopup] = useState(false);
  const [popupPosition, setPopupPosition] = useState<{ x: number; y: number } | null>(null);
  const statusRead = getValue(config.properties.deviceId, 'status_read');
  const alarm = getValue(config.properties.deviceId, 'alarm');
  const frequencyRead = getValue(config.properties.deviceId, 'frequency_read');
  
  const mapValueToStatus = (statusRead: boolean, alarm: boolean): MachineStatusType => {
    if (alarm) return 'alarm';
    if (statusRead) return 'on';
    return 'off';
  };

  const status = mapValueToStatus(statusRead, alarm);

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode) {
      onClick?.(e);
    } else {
      const rect = e.currentTarget.getBoundingClientRect();
      setPopupPosition({
        x: rect.right,
        y: rect.top
      });
      setShowPopup(true);
    }
  }, [isEditMode, onClick]);

  const handleClosePopup = () => {
    setShowPopup(false);
    setPopupPosition(null);
  };

  const getInfoBoxPosition = () => {
    switch (config.properties.nameLocation) {
      case 'top':
        return '-translate-x-1/2 -translate-y-full top-1/2 left-1/2';
      case 'bottom':
        return '-translate-x-1/2 translate-y-full bottom-1/2 left-1/2';
      case 'left':
        return '-translate-y-1/2 -translate-x-full top-1/2 left-1/2';
      case 'right':
        return '-translate-y-1/2 translate-x-full top-1/2 right-1/2';
      default:
        return '-translate-x-1/2 -translate-y-full top-0 left-1/2';
    }
  };

  const getInfoBoxDirection = () => {
    switch (config.properties.nameLocation) {
      case 'top':
        return 'pb-2';
      case 'bottom':
        return 'pt-1';
      case 'left':
        return 'pr-2';
      case 'right':
        return 'pl-2';
      default:
        return 'pb-2';
    }
  };

  return (
    <div className="relative">
      {config.properties.equipmentType === 'Chiller' ? (
        <div 
          onClick={handleClick}
          className="inline-flex relative cursor-pointer"
        >
          {/* Status Circle (Always Centered) */}
          <div className="relative w-[14px] h-[14px] rounded-full bg-white flex items-center justify-center shadow-[1px_3px_20px_rgba(154,170,207,0.10)]">
            <div 
              className={cn("w-[10px] h-[10px] rounded-full", STATUS_COLORS[status])}
            />
          </div>

          {/* Info Box */}
          <div 
            className={cn(
              "absolute bg-white shadow-[1px_3px_20px_rgba(154,170,207,0.10)] rounded-[5px] p-[3px] flex flex-col justify-center items-center gap-1 -z-10",
              "w-[90px] h-auto",
              getInfoBoxDirection(),
              getInfoBoxPosition()
            )}
          >
            {/* Device Name */}
            <div className="w-full text-center text-[#5E5E5E] text-[8px] font-bold flex items-center justify-center gap-1 px-1 min-w-max">
              {!isDeviceInAutopilotMode(config.properties.deviceId) && <img src={manual_icon} alt="Manual Mode" className="w-[10px] h-[10px]" />}
              {isDeviceUnderMaintenance(config.properties.deviceId) && <img src={maintenance_icon} alt="Under Maintenance" className="w-[10px] h-[10px]" />}
              <span>{config.properties.deviceName}</span>
            </div>

            {/* Parameters */}
            {(config.properties.chillerDisplaySettings?.showEfficiency ?? true) && (
              <div className={cn(
                "w-full px-[3px] py-[1px] rounded-[2px] flex justify-between items-center",
                statusRead ? "bg-[#CBF0EF]" : "bg-[#EDEFF9]"
              )}>
                <div className={cn(
                  "text-[8px] font-normal",
                  statusRead ? "text-[#5E5E5E]" : "text-[#B4B4B4]"
                )}>Efficiency</div>
                <div className={cn(
                  "text-[8px] font-bold",
                  statusRead ? "text-[#0E7EE4]" : "text-[#B4B4B4]"
                )}>
                  {getValue(config.properties.deviceId, 'efficiency') != null 
                    ? getValue(config.properties.deviceId, 'efficiency')?.toFixed(3)
                    : '-'}
                </div>
              </div>
            )}
            
            {(config.properties.chillerDisplaySettings?.showRLA ?? true) && (
              <div className={cn(
                "w-full px-[3px] py-[1px] rounded-[2px] flex justify-between items-center",
                statusRead ? "bg-[#EDEFF9]" : "bg-[#EDEFF9]"
              )}>
                <div className={cn(
                  "text-[8px] font-normal",
                  statusRead ? "text-[#5E5E5E]" : "text-[#B4B4B4]"
                )}>% RLA</div>
                <div className={cn(
                  "text-[8px] font-bold",
                  statusRead ? "text-[#0E7EE4]" : "text-[#B4B4B4]"
                )}>
                  {getValue(config.properties.deviceId, 'percentage_rla') != null 
                    ? `${getValue(config.properties.deviceId, 'percentage_rla')?.toFixed(1)}%`
                    : '-'}
                </div>
              </div>
            )}
            
            {(config.properties.chillerDisplaySettings?.showSetpoint ?? true) && (
              <div className={cn(
                "w-full px-[3px] py-[1px] rounded-[2px] flex justify-between items-center",
                statusRead ? "bg-[#C0E2FF]" : "bg-[#EDEFF9]"
              )}>
                <div className={cn(
                  "text-[8px] font-normal",
                  statusRead ? "text-[#5E5E5E]" : "text-[#B4B4B4]"
                )}>Setpoint</div>
                <div className={cn(
                  "text-[8px] font-bold",
                  statusRead ? "text-[#0E7EE4]" : "text-[#B4B4B4]"
                )}>
                  {getValue(config.properties.deviceId, 'setpoint_read') != null 
                    ? `${getValue(config.properties.deviceId, 'setpoint_read')?.toFixed(1)}°F`
                    : '-'}
                </div>
              </div>
            )}
            
            {(config.properties.chillerDisplaySettings?.showCHW ?? true) && (
              <div className={cn(
                "w-full px-[3px] py-[1px] rounded-[2px] flex justify-between items-center",
                statusRead ? "bg-[#8BC6FF]" : "bg-[#EDEFF9]"
              )}>
                <div className={cn(
                  "text-[8px] font-normal",
                  statusRead ? "text-[#5E5E5E]" : "text-[#B4B4B4]"
                )}>CHW</div>
                <div className={cn(
                  "text-[8px] font-bold",
                  statusRead ? "text-[#052745]" : "text-[#B4B4B4]"
                )}>
                  {getValue(config.properties.deviceId, 'evap_water_flow_rate') != null 
                    ? `${getValue(config.properties.deviceId, 'evap_water_flow_rate')?.toFixed(0)} GPM`
                    : '-'}
                </div>
              </div>
            )}
            
            {(config.properties.chillerDisplaySettings?.showCDW ?? true) && (
              <div className={cn(
                "w-full px-[3px] py-[1px] rounded-[2px] flex justify-between items-center",
                statusRead ? "bg-[#F5E97D]" : "bg-[#EDEFF9]"
              )}>
                <div className={cn(
                  "text-[8px] font-normal",
                  statusRead ? "text-[#5E5E5E]" : "text-[#B4B4B4]"
                )}>CDW</div>
                <div className={cn(
                  "text-[8px] font-bold",
                  statusRead ? "text-[#521503]" : "text-[#B4B4B4]"
                )}>
                  {getValue(config.properties.deviceId, 'cond_water_flow_rate') != null 
                    ? `${getValue(config.properties.deviceId, 'cond_water_flow_rate')?.toFixed(0)} GPM`
                    : '-'}
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div 
          onClick={handleClick}
          className="inline-flex relative cursor-pointer"
        >
          {/* Info Box (Name and/or Frequency) */}
          {(config.properties.showName || config.properties.equipmentType === 'VSD') && (
            <div 
              className={cn(
                "absolute bg-white shadow-[1px_3px_20px_rgba(154,170,207,0.10)] rounded-[5px] p-[4px] flex flex-col items-center gap-1 -z-10",
                getInfoBoxDirection(),
                getInfoBoxPosition()
              )}
            >
              {config.properties.showName && (
                <div className="text-[#5E5E5E] text-[8px] font-bold whitespace-nowrap text-center flex items-center justify-center gap-1 px-1 w-full min-w-max">
                <div className="flex items-center gap-1">
                  {!isDeviceInAutopilotMode(config.properties.deviceId) && <img src={manual_icon} alt="Manual Mode" className="w-[12.5px] h-[12.5px]" />}
                  {isDeviceUnderMaintenance(config.properties.deviceId) && <img src={maintenance_icon} alt="Under Maintenance" className="w-[12.5px] h-[12.5px]" />}
                  <span>{config.properties.deviceName}</span>
                </div>
              </div>
              )}
              {config.properties.equipmentType === 'VSD' && (
                <div className="w-full px-0.5 py-[1px] bg-[#EDEFF9] rounded-[2px] flex justify-center items-center whitespace-nowrap">
                  <div className="text-[#0E7EE4] text-[8px] font-bold">
                    {frequencyRead == null ? '-' : frequencyRead.toFixed(1)} Hz
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Status Circle (Always Centered) */}
          <div className="relative w-[14px] h-[14px] rounded-full bg-white flex items-center justify-center shadow-[1px_3px_20px_rgba(154,170,207,0.10)]">
            <div 
              className={cn("w-[10px] h-[10px] rounded-full", STATUS_COLORS[status])}
            />
          </div>
        </div>
      )}

      {/* Popup Menu */}
      {showPopup && popupPosition && createPortal(
        <div 
          style={{ 
            position: 'fixed',
            left: `${popupPosition.x}px`,
            top: `${popupPosition.y}px`,
          }}
        >
          <PopupMenu
            deviceId={config.properties.deviceId}
            deviceName={config.properties.deviceName}
            equipmentType={config.properties.equipmentType}
            onClose={handleClosePopup}
          />
        </div>,
        document.body
      )}
    </div>
  );
};