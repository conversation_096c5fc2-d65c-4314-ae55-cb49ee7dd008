import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/utils/supabase';
import { sendControl } from '@/services/controlService';

interface RealtimeData {
  [key: string]: {
    [key: string]: any;
  };
}

interface RealtimeContextType {
  realtimeData: RealtimeData;
  getValue: (deviceId: string, datapoint: string) => any;
  setValue: (deviceId: string, datapoint: string, value: any) => Promise<void>;
}

const RealtimeContext = createContext<RealtimeContextType>({
  realtimeData: {},
  getValue: () => null,
  setValue: async () => {},
});

export const useRealtime = () => useContext(RealtimeContext);

export const RealtimeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [realtimeData, setRealtimeData] = useState<RealtimeData>({});

  useEffect(() => {
    // Initial data fetch
    const fetchInitialData = async () => {
      const { data, error } = await supabase
        .from('latest_data')
        .select('*');

      if (error) {
        console.error('Error fetching initial data:', error);
        return;
      }

      const initialData: RealtimeData = {};
      data.forEach((row) => {
        if (!initialData[row.device_id]) {
          initialData[row.device_id] = {};
        }
        initialData[row.device_id][row.datapoint] = row.value;
      });

      setRealtimeData(initialData);
    };

    fetchInitialData();

    // Subscribe to real-time updates
    const channel = supabase
      .channel('db_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'latest_data',
        },
        (payload) => {
          if (payload.eventType === 'DELETE') return;

          const { device_id, datapoint, value } = payload.new;
          
          setRealtimeData((prev) => ({
            ...prev,
            [device_id]: {
              ...prev[device_id],
              [datapoint]: value,
            },
          }));
        }
      )
      .subscribe((status) => {
        console.log('Realtime subscription status:', status);
      });

    return () => {
      console.log('Unsubscribing from realtime updates');
      channel.unsubscribe();
    };
  }, []);

  const getValue = (deviceId: string, datapoint: string) => {
    return realtimeData[deviceId]?.[datapoint];
  };

  const setValue = async (deviceId: string, datapoint: string, value: any) => {
    try {
      // For manual control operations, use controlService
      if (datapoint.endsWith('_write') || datapoint === 'is_manual') {
        
        await sendControl(deviceId, datapoint, value);
      } else {
        // For other operations, update Supabase
        // const { error } = await supabase
        //   .from('latest_data')
        //   .upsert({
        //     device_id: deviceId,
        //     datapoint: datapoint,
        //     value: value,
        //     timestamp: new Date().toISOString(),
        //   });

        // if (error) {
        //   throw error;
        // }
      }

      // Update local state
      // setRealtimeData((prev) => ({
      //   ...prev,
      //   [deviceId]: {
      //     ...prev[deviceId],
      //     [datapoint]: value,
      //   },
      // }));
    } catch (error) {
      console.error('Error setting value:', error);
      throw error;
    }
  };

  return (
    <RealtimeContext.Provider value={{ realtimeData, getValue, setValue }}>
      {children}
    </RealtimeContext.Provider>
  );
}; 