import React, { useState } from 'react';

// Import components
import TotalSystemEfficiencyCard from './components/TotalSystemEfficiencyCard';
import EnergyUsageCompareCard from './components/EnergyUsageCompareCard';
import BuildingLoadGraph from './components/BuildingLoadGraph';
import SystemAlertCard from './components/SystemAlertCard';
import AirSideHeatMap from './components/AirSideHeatMap';
import PlantEquipmentCard from './components/PlantEquipmentCard';
import PlantDiagram from './components/PlantDiagram';
import EfficiencyCard from './components/EfficiencyCard';
import PowerCard from './components/PowerCard';
import WeatherStationCard from './components/WeatherStationCard';
import TooHotTooColdReportCard from './components/TooHotTooColdReportCard';
import NextEventTimeline from '@/components/common/NextEventTimeline';
import { ActionProvider } from '@/contexts/ActionContext';

const AutopilotPage: React.FC = () => {
  return (
    <ActionProvider>
      <div className="w-full h-[calc(100vh-71px)] overflow-auto p-4 bg-background">
        <div className="flex flex-1 gap-4 w-full h-full">
          {/* First section (20% width) */}
          <div className="w-[20%] flex flex-col gap-4 h-fit">
            <TotalSystemEfficiencyCard />
            <EnergyUsageCompareCard />
            <BuildingLoadGraph />
            <SystemAlertCard />
            {/* <TooHotTooColdReportCard /> */}
          </div>
          
          {/* Second section (55% width) */}
          <div className="w-[55%] flex flex-col gap-4 h-fit">
            {/* Air-side section */}
            <div className="flex justify-start flex-col gap-[8px] p-[10px] alto-card">
              <div className="flex self-stretch justify-center items-center flex-row gap-2.5 py-1 bg-white border border-solid border-[#0E7EE4] rounded-lg shadow-[1px_3px_20px_0px_rgba(57,124,221,0.30)] backdrop-blur-[10px]">
                <p className="flex-1 text-[#065BA9] text-sm text-center font-semibold">
                  Air-Side
                </p>
              </div>
              <div className="flex gap-[8px]">
                {/* First card - PlantEfficiencyCard (50% width) */}
                <div className="w-1/2">
                  <EfficiencyCard thresholds={[0.0, 0.2, 0.25, 0.3, 0.4]} deviceId="air_distribution_system" title="Air-Side Efficiency" />
                </div>
                
                {/* Second card - PlantPowerCard (25% width) */}
                <div className="w-1/4">
                  <PowerCard deviceId="air_distribution_system" title="Air-Side Power" />
                </div>
              </div>
              <AirSideHeatMap />
            </div>
            
            {/* Water-side section */}
            <div className="flex justify-start flex-col gap-[8px] p-[10px] alto-card">
              <div className="flex self-stretch justify-center items-center flex-row gap-2.5 py-1 bg-white border border-solid border-[#0E7EE4] rounded-lg shadow-[1px_3px_20px_0px_rgba(57,124,221,0.30)] backdrop-blur-[10px]">
                <p className="flex-1 text-[#065BA9] text-sm text-center font-semibold">
                  Water-Side
                </p>
              </div>
              {/* Three cards side-by-side */}
              <div className="flex gap-[8px]">
                {/* First card - PlantEfficiencyCard (50% width) */}
                <div className="w-1/2">
                  <EfficiencyCard thresholds={[0.0, 0.6, 0.7, 0.8, 1.0]} deviceId="plant" title="Water-Side Efficiency"  />
                </div>
                
                {/* Second card - PlantPowerCard (25% width) */}
                <div className="w-1/4">
                  <PowerCard deviceId="plant" title="Water-Side Power" />
                </div>
                
                {/* Third card - WeatherStationCard (25% width with flex end alignment) */}
                <div className="w-1/4 flex">
                  <WeatherStationCard />
                </div>
              </div>
              
              <div className="flex gap-4">
                <div className="w-2/5 overflow-auto">
                  <PlantEquipmentCard />
                </div>
                <div className="w-3/5">
                  <PlantDiagram />
                </div>
              </div>
            </div>
          </div>
          
          {/* Third section (25% width) */}
          <div className="w-[25%] flex flex-col gap-4 h-full">
            <div className="h-full bg-background">
              <NextEventTimeline />
            </div>
          </div>
        </div>
      </div>
    </ActionProvider>
  );
};

export default AutopilotPage;